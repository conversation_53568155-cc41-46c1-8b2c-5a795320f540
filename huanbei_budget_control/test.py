import pandas as pd
import numpy as np
import sys


def main():
    sys.path.append('/data2/yuwu/huanbei_budget_control')

    from budget_control_by_model import init_model, get_predict_data
    # 1. load model
    model_path = './0_day_model.json'
    model = init_model(model_path)
    print("init model success")

    # data_path = '/data2/xizhang/auto_bid/huanbei/get_data/data_dynamic_creative_level/hourly_reports_REQUEST_TIME'
    data_path = '../adgroup_daily/'
    import os
    df = pd.read_csv(os.path.join(data_path, '********.csv'))
    predict_df = get_predict_data(model, df)
    print("get predict data success")

    adgroup_ids = predict_df['adgroup_id'].unique()
    adgroup_status = {adgroup_id: {'systemStatus': 'ADGROUP_STATUS_ACTIVE'} for adgroup_id in adgroup_ids}

    # 调用接口样例：
    sys.path.append('/data2/yuwu/huanbei_budget_control')
    from config import StringFormat, AccountConfig
    from budget_control_by_model import check_if_over_budget_by_model
    # check_if_over_budget_by_model(select_account_id, adgroup_status, data_path, account_config, string_format, score_thres=0.8)
    """
    select_account_id: str
    adgroup_status: dict, adgroup info
    data_path: str, 小时报文件夹路径
    account_config: AccountConfig object
    string_format: StringFormat object
    score_thres: float, 模型分数阈值, 默认值0.8
    """
    res = check_if_over_budget_by_model('********', adgroup_status, data_path, AccountConfig, StringFormat)


if __name__ == "__main__":
    main()