{"learner": {"attributes": {"scikit_learn": "{\"_estimator_type\": \"classifier\"}"}, "feature_names": ["hour_sin", "hour_cos", "cum_1h_up_to_cur_h_cost", "mean_1h_up_to_cur_h_cost", "cum_1h_up_to_cur_h_reg_pv", "mean_1h_up_to_cur_h_reg_pv", "cum_1h_up_to_cur_h_apply_pv", "mean_1h_up_to_cur_h_apply_pv", "cum_1h_up_to_cur_h_credit_pv", "mean_1h_up_to_cur_h_credit_pv", "cum_2h_up_to_cur_h_cost", "mean_2h_up_to_cur_h_cost", "cum_2h_up_to_cur_h_reg_pv", "mean_2h_up_to_cur_h_reg_pv", "cum_2h_up_to_cur_h_apply_pv", "mean_2h_up_to_cur_h_apply_pv", "cum_2h_up_to_cur_h_credit_pv", "mean_2h_up_to_cur_h_credit_pv", "cum_4h_up_to_cur_h_cost", "mean_4h_up_to_cur_h_cost", "cum_4h_up_to_cur_h_reg_pv", "mean_4h_up_to_cur_h_reg_pv", "cum_4h_up_to_cur_h_apply_pv", "mean_4h_up_to_cur_h_apply_pv", "cum_4h_up_to_cur_h_credit_pv", "mean_4h_up_to_cur_h_credit_pv", "cum_6h_up_to_cur_h_cost", "mean_6h_up_to_cur_h_cost", "cum_6h_up_to_cur_h_reg_pv", "mean_6h_up_to_cur_h_reg_pv", "cum_6h_up_to_cur_h_apply_pv", "mean_6h_up_to_cur_h_apply_pv", "cum_6h_up_to_cur_h_credit_pv", "mean_6h_up_to_cur_h_credit_pv", "cum_8h_up_to_cur_h_cost", "mean_8h_up_to_cur_h_cost", "cum_8h_up_to_cur_h_reg_pv", "mean_8h_up_to_cur_h_reg_pv", "cum_8h_up_to_cur_h_apply_pv", "mean_8h_up_to_cur_h_apply_pv", "cum_8h_up_to_cur_h_credit_pv", "mean_8h_up_to_cur_h_credit_pv", "cum_12h_up_to_cur_h_cost", "mean_12h_up_to_cur_h_cost", "cum_12h_up_to_cur_h_reg_pv", "mean_12h_up_to_cur_h_reg_pv", "cum_12h_up_to_cur_h_apply_pv", "mean_12h_up_to_cur_h_apply_pv", "cum_12h_up_to_cur_h_credit_pv", "mean_12h_up_to_cur_h_credit_pv", "cum_16h_up_to_cur_h_cost", "mean_16h_up_to_cur_h_cost", "cum_16h_up_to_cur_h_reg_pv", "mean_16h_up_to_cur_h_reg_pv", "cum_16h_up_to_cur_h_apply_pv", "mean_16h_up_to_cur_h_apply_pv", "cum_16h_up_to_cur_h_credit_pv", "mean_16h_up_to_cur_h_credit_pv", "prev_h_cost", "prev_h_reg_pv", "prev_h_apply_pv", "prev_h_credit_pv", "prev_h_prev_h_cost_squared", "prev_h_prev_h_cost_cubed", "prev_h_prev_h_reg_pv_squared", "prev_h_prev_h_reg_pv_cubed", "prev_h_prev_h_apply_pv_squared", "prev_h_prev_h_apply_pv_cubed", "prev_h_prev_h_credit_pv_squared", "prev_h_prev_h_credit_pv_cubed", "past_1day_cost", "past_1day_view_count", "past_1day_valid_click_count", "past_1day_reg_pv", "past_1day_apply_pv", "past_1day_credit_pv", "past_1day_reg_cost", "past_1day_apply_cost", "past_1day_credit_cost", "past_3day_cost", "past_3day_view_count", "past_3day_valid_click_count", "past_3day_reg_pv", "past_3day_apply_pv", "past_3day_credit_pv", "past_3day_reg_cost", "past_3day_apply_cost", "past_3day_credit_cost", "past_7day_cost", "past_7day_view_count", "past_7day_valid_click_count", "past_7day_reg_pv", "past_7day_apply_pv", "past_7day_credit_pv", "past_7day_reg_cost", "past_7day_apply_cost", "past_7day_credit_cost", "day_of_week"], "feature_types": ["float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float"], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "100"}, "iteration_indptr": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100], "tree_info": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "trees": [{"base_weights": [1.2335614e-07, 1.0547764, -2.8440504, 1.1428547, -0.28967914, -1.5814232, -0.29049498, 0.119861744, 0.0042443336, -0.28814903, -0.047288056], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 0, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [62973.445, 5332.99, 432.10156, 919.1504, 0.0, 378.40454, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 257.07, -0.28967914, -0.70710677, -0.29049498, 0.119861744, 0.0042443336, -0.28814903, -0.047288056], "split_indices": [56, 61, 48, 58, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [20990.291, 15312.103, 5678.188, 14978.94, 333.1613, 262.51123, 5415.677, 14256.414, 722.5266, 120.08253, 142.42871], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.004809641, 0.9867091, -2.3772533, 1.0713943, -0.24194978, -1.3632147, -0.24246836, 0.11325401, 0.014626386, -0.2409747, -0.04196108], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 1, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [48786.34, 4263.073, 289.51562, 815.1113, 0.0, 269.893, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 219.89, -0.24194978, -0.70710677, -0.24246836, 0.11325401, 0.014626386, -0.2409747, -0.04196108], "split_indices": [56, 61, 48, 58, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [20856.252, 14768.981, 6087.27, 14411.371, 357.61044, 273.33005, 5813.94, 13517.705, 893.6665, 128.8601, 144.46994], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.00721979, 0.9264974, -2.0773203, 1.007541, -0.21140061, -1.2022076, -0.21179059, 0.108079635, 0.02701262, -0.21066694, -0.037329968], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 2, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [39193.04, 3499.586, 219.76758, 746.5664, 0.0, 208.93677, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 171.42, -0.21140061, -0.70710677, -0.21179059, 0.108079635, 0.02701262, -0.21066694, -0.037329968], "split_indices": [56, 61, 48, 58, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [20450.76, 14192.468, 6258.2915, 13824.6455, 367.8223, 278.73697, 5979.554, 12574.97, 1249.6753, 132.55655, 146.18042], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.008378614, 0.8722471, -1.8676236, 0.9495229, -0.19012193, -1.0764539, -0.19045042, 0.103558436, 0.034630958, -0.18950437, -0.033278905], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 3, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [32200.02, 2917.5166, 180.74414, 686.78125, 0.0, 170.6235, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 133.61, -0.19012193, -0.70710677, -0.19045042, 0.103558436, 0.034630958, -0.18950437, -0.033278905], "split_indices": [56, 61, 48, 58, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [19866.955, 13603.363, 6263.5923, 13235.253, 368.11014, 280.31793, 5983.2744, 11582.099, 1653.1543, 132.70003, 147.6179], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.008814315, 0.82261926, -1.7126855, 0.8960737, -0.17446241, -0.97415745, -0.17476167, 0.09510586, -0.0037912775, -0.17390053, -0.02971746], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 4, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [26857.81, 2456.2998, 157.04688, 649.9785, 0.0, 144.55365, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 257.07, -0.17446241, -0.70710677, -0.17476167, 0.09510586, -0.0037912775, -0.17390053, -0.02971746], "split_indices": [56, 61, 48, 58, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [19168.93, 13016.269, 6152.661, 12654.837, 361.43155, 279.17307, 5873.4883, 11951.235, 703.6011, 130.34357, 148.82951], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.008796996, 0.7767543, -1.593614, 0.84639394, -0.1624827, -0.88832885, -0.1627705, 0.099641964, 0.050781805, -0.16194305, -0.026573649], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 5, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [22646.053, 2081.995, 141.90723, 613.6377, 0.0, 125.70082, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 48.99, -0.1624827, -0.70710677, -0.1627705, 0.099641964, 0.050781805, -0.16194305, -0.026573649], "split_indices": [56, 61, 48, 58, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [18400.693, 12439.543, 5961.15, 12089.623, 349.91992, 276.10278, 5685.0474, 8376.144, 3713.4792, 126.24911, 149.85368], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.008489313, 0.7335478, -1.4993528, 0.79936206, -0.15305026, -0.81454587, -0.15333769, 0.083872415, -0.052057307, -0.1747742, -0.034245666], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 6, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [19246.178, 1772.5859, 131.9414, 600.66016, 0.0, 119.54103, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 404.07, -0.15305026, 1.0, -0.15333769, 0.083872415, -0.052057307, -0.1747742, -0.034245666], "split_indices": [56, 61, 48, 58, 0, 75, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [17602.166, 11886.809, 5715.358, 11551.657, 335.15085, 271.70245, 5443.656, 11217.502, 334.15536, 90.36752, 181.33493], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.007971623, 0.69286305, -1.4223517, 0.75491565, -0.14545658, -0.74310243, -0.14575116, 0.08511101, 0.019817451, -0.14686428, -0.018229794], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 7, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [16452.807, 1514.459, 128.79199, 591.0122, 0.0, 109.46158, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 124.65, -0.14545658, -0.70710677, -0.14575116, 0.08511101, 0.019817451, -0.14686428, -0.018229794], "split_indices": [56, 61, 48, 58, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [16793.148, 11355.996, 5437.153, 11037.696, 318.29956, 268.6805, 5168.4727, 9411.106, 1626.5901, 116.43021, 152.2503], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.007349677, 0.6543535, -1.3592594, 0.7127245, -0.1392344, -0.68598616, -0.13954179, 0.088191144, 0.03898174, -0.15964352, -0.025772432], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 8, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [14137.844, 1297.4272, 124.11621, 576.10254, 0.0, 102.45616, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 42.93, -0.1392344, 1.0, -0.13954179, 0.088191144, 0.03898174, -0.15964352, -0.025772432], "split_indices": [56, 61, 48, 58, 0, 75, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [15987.388, 10850.756, 5136.6323, 10550.511, 300.2446, 262.8236, 4873.8086, 6922.002, 3628.509, 83.20628, 179.61731], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.006640763, 0.61737955, -1.3057077, 0.67212707, -0.13406233, -0.6290315, -0.13438724, 0.08419342, 0.035895567, -0.13670534, -0.011844913], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 9, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [12189.954, 1113.4641, 123.89258, 536.65186, 0.0, 97.71875, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 42.93, -0.13406233, -0.70710677, -0.13438724, 0.08419342, 0.035895567, -0.13670534, -0.011844913], "split_indices": [56, 61, 48, 58, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [15206.881, 10377.803, 4829.0776, 10096.162, 281.64084, 258.75378, 4570.3237, 6545.4263, 3550.7363, 105.1243, 153.62949], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.0058638467, 0.5819681, -1.260622, 0.6331926, -0.12971173, -0.5827182, -0.13005832, 0.073316105, 0.011063634, -0.1494724, -0.019061236], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 10, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [10546.24, 957.11646, 121.66797, 505.17603, 0.0, 90.32612, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 124.65, -0.12971173, 1.0, -0.13005832, 0.073316105, 0.011063634, -0.1494724, -0.019061236], "split_indices": [56, 61, 48, 58, 0, 75, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [14452.266, 9933.991, 4518.274, 9671.019, 262.97217, 252.38455, 4265.8896, 8117.656, 1553.3633, 75.0387, 177.34586], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.0050499006, 0.5481078, -1.2211597, 0.59593827, -0.1260153, -0.53568155, -0.12638743, 0.07857128, 0.031680748, -0.12953165, -0.0069037937], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 11, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [9144.919, 823.95166, 122.74463, 490.9746, 0.0, 88.1738, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 34.21, -0.1260153, -0.70710677, -0.12638743, 0.07857128, 0.031680748, -0.12953165, -0.0069037937], "split_indices": [56, 61, 48, 58, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [13731.11, 9516.875, 4214.2354, 9272.283, 244.59157, 247.99442, 3966.241, 5518.541, 3753.7424, 93.695145, 154.29927], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.004212342, 0.5153327, -1.1872567, 0.5598651, -0.12284757, -0.4970068, -0.12324909, 0.060455095, -0.062453426, -0.14228824, -0.013691752], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 12, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [7948.2256, 709.95776, 121.72266, 471.7754, 0.0, 80.70417, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 404.07, -0.12284757, 1.0, -0.12324909, 0.060455095, -0.062453426, -0.14228824, -0.013691752], "split_indices": [56, 61, 48, 58, 0, 75, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [13049.61, 9132.491, 3917.119, 8905.74, 226.75089, 241.65004, 3675.4688, 8582.426, 323.31454, 66.83915, 174.81088], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.0033252526, 0.4837909, -1.1567491, 0.682878, 0.1327927, -0.45733982, -0.120547354, -0.028301477, 0.07662957, 0.02107273, -0.11999857, -0.124279015, -0.0030641907], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 13, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [6917.439, 613.16846, 123.31445, 451.3855, 330.29324, 79.83975, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 48.99, 1.0, 5.38625, 1.0, -0.70710677, -0.120547354, -0.028301477, 0.07662957, 0.02107273, -0.11999857, -0.124279015, -0.0030641907], "split_indices": [56, 58, 48, 51, 61, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [12408.722, 8774.986, 3633.7354, 5598.536, 3176.4507, 237.33458, 3396.401, 444.87973, 5153.6562, 3001.7634, 174.68712, 82.872765, 154.4618], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.0026066613, 0.45317602, -1.1300986, 0.52567047, -0.46503684, -0.42447346, -0.11820761, 0.07460618, 0.033967856, -0.013501274, -0.11836871, -0.12124725, -0.0027587458], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 14, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [6031.505, 563.04663, 122.84912, 320.9795, 146.69983, 72.61813, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 14.86, 1.0, -0.70710677, -0.11820761, 0.07460618, 0.033967856, -0.013501274, -0.11836871, -0.12124725, -0.0027587458], "split_indices": [56, 60, 48, 58, 61, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [11816.081, 8453.882, 3362.1992, 7835.67, 618.2118, 231.30237, 3130.8967, 3584.8213, 4250.8486, 424.4709, 193.7409, 76.788124, 154.51425], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.0017952459, 0.423959, -1.106091, 0.52643496, -0.20651935, -0.39386135, -0.11617088, -0.023944635, 0.059586998, 0.0051676477, -0.088660344, -0.13686605, -0.006620185], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 15, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [5266.0835, 526.8452, 122.60327, 373.09314, 200.32309, 72.26529, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 171.42, 1.0, 5.38625, 7939.11, 1.0, -0.11617088, -0.023944635, 0.059586998, 0.0051676477, -0.088660344, -0.13686605, -0.006620185], "split_indices": [56, 58, 48, 51, 79, 75, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [11257.304, 8151.6914, 3105.6118, 7012.127, 1139.5646, 225.53537, 2880.0764, 582.67365, 6429.4536, 826.6489, 312.9156, 55.938675, 169.59671], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.0011613435, 0.37419495, -0.114389956, 0.6078778, 0.086559065, -0.0055761505, 0.07451521, 0.017595327, -0.084492825], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 16, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [4598.6997, 545.7783, 0.0, 408.20398, 303.35413, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 34.21, -0.114389956, 140.29, 18606.64, -0.0055761505, 0.07451521, 0.017595327, -0.084492825], "split_indices": [48, 58, 0, 50, 79, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [10764.101, 8119.601, 2644.4998, 4479.3486, 3640.2522, 767.84924, 3711.4995, 3322.231, 318.02115], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.0006508535, 0.36848786, -1.065899, 0.4866824, -0.13548687, -0.38497826, -0.112826586, -0.02014189, 0.055094045, -0.0014300204, -0.116401725, -0.118425265, -0.002183033], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 17, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [4051.0464, 457.41895, 112.018555, 275.1162, 181.88597, 64.90529, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 4.9754543, 1.0, -0.70710677, -0.112826586, -0.02014189, 0.055094045, -0.0014300204, -0.116401725, -0.118425265, -0.002183033], "split_indices": [56, 59, 48, 51, 61, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [10323.963, 7676.952, 2647.011, 6218.5767, 1458.3754, 222.70387, 2424.3071, 530.9387, 5687.638, 1305.5614, 152.81395, 68.8677, 153.83618], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-6.0736296e-05, 0.3228291, -0.11144942, 0.55112594, 0.055950955, -0.006589494, 0.0690394, 0.011590002, -0.11997898], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 18, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [3556.5273, 466.87598, 0.0, 354.9497, 266.19788, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 34.21, -0.11144942, 140.29, 1.0, -0.006589494, 0.0690394, 0.011590002, -0.11997898], "split_indices": [48, 58, 0, 50, 61, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [9881.656, 7662.326, 2219.33, 4129.0923, 3533.2344, 760.45514, 3368.637, 3373.1096, 160.12454], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.00063277, 0.32002962, -1.0328825, 0.60134894, 0.13283306, -0.38495442, -0.110232525, -0.007921651, 0.07676562, 0.019768603, -0.07955151, -0.1346532, -0.008468077], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 19, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [3139.9268, 381.07324, 100.73804, 327.33655, 261.88287, 63.171646, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 14.86, 1.0, 148.82, 491.9, 1.0, -0.110232525, -0.007921651, 0.07676562, 0.019768603, -0.07955151, -0.1346532, -0.008468077], "split_indices": [56, 58, 48, 50, 58, 75, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [9484.08, 7236.7236, 2247.3564, 2890.565, 4346.1587, 218.18149, 2029.1748, 567.7464, 2322.8186, 4063.1038, 283.0551, 51.08993, 167.09155], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.0010460949, 0.2772031, -0.10915426, 0.3456103, -0.47880337, 0.013384334, 0.05348297, -0.020580145, -0.1184454], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 20, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [2767.7375, 375.99896, 0.0, 266.98926, 116.003296, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.10915426, -0.25881904, 1.0, 0.013384334, 0.05348297, -0.020580145, -0.1184454], "split_indices": [48, 60, 0, 1, 61, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [9119.512, 7266.2207, 1853.2906, 6663.791, 602.42957, 3145.3477, 3518.4436, 435.309, 167.1206], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0016663912, 0.25605994, -0.108196475, 0.3818669, -0.1480383, -0.018668843, 0.046214085, -0.0009765383, -0.1036431], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 21, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [2445.7576, 360.62057, 0.0, 246.9455, 206.92068, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 111.19, -0.108196475, 6.9990907, 167.0, -0.018668843, 0.046214085, -0.0009765383, -0.1036431], "split_indices": [48, 58, 0, 43, 91, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [8782.381, 7091.361, 1691.0201, 5407.814, 1683.5469, 668.8974, 4738.9165, 1457.6791, 225.86786], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0020349878, 0.23588607, -0.10734373, 0.516109, 0.06221652, -0.010800304, 0.06828462, 0.012854493, -0.0763371], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 22, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [2164.0332, 338.04196, 0.0, 276.6584, 235.01436, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 14.86, -0.10734373, 148.82, 99.0, -0.010800304, 0.06828462, 0.012854493, -0.0763371], "split_indices": [48, 58, 0, 50, 82, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [8487.436, 6945.7964, 1541.639, 2656.8647, 4288.9316, 560.2293, 2096.6353, 3970.7615, 318.17007], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.002267242, 0.21699192, -0.1065829, 0.24996337, -1.1678604, 0.0097909635, 0.050230782, -0.11298708, -0.15608053], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 23, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [1917.4552, 311.5663, 0.0, 255.47247, 0.6056824, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.1065829, -1.8369701e-16, 1.0, 0.0097909635, 0.050230782, -0.11298708, -0.15608053], "split_indices": [48, 61, 0, 1, 56, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [8220.523, 6816.1357, 1404.3881, 6658.427, 157.7086, 4155.724, 2502.7026, 147.33934, 10.369256], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0027855127, 0.2196894, -0.96361524, 0.5031298, 0.06666681, -0.4013738, -0.10590277, -0.009465131, 0.06987288, 0.023953635, -0.021421932, -0.11872732, -0.0049487664], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 24, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1702.4047, 280.44724, 79.89929, 265.14404, 204.02217, 60.461918, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 12.3, 1.0, 167.96, 1355.7537, -0.70710677, -0.10590277, -0.009465131, 0.06987288, 0.023953635, -0.021421932, -0.11872732, -0.0049487664], "split_indices": [56, 58, 48, 50, 87, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [7962.084, 6465.7466, 1496.3373, 2266.069, 4199.6777, 217.84398, 1278.4933, 558.8012, 1707.268, 2599.7969, 1599.8807, 66.639, 151.20499], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.0028999932, 0.18332087, -0.105293594, 0.2421897, -0.5248393, 0.01031162, 0.04779799, -0.030083163, -0.1195265], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 25, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [1511.246, 273.79004, 0.0, 198.71481, 75.35133, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 330.54, -0.105293594, -1.8369701e-16, 6.0, 0.01031162, 0.04779799, -0.030083163, -0.1195265], "split_indices": [48, 58, 0, 1, 36, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [7726.645, 6563.46, 1163.1853, 6060.327, 503.1326, 3812.8647, 2247.4624, 378.2107, 124.92189], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0031561754, 0.16789, -0.104747, 0.19838592, -0.11498957, 0.03660019, 0.00022887986], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 26, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [1343.7341, 259.99435, 0.0, 207.68439, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.104747, 40.21, -0.11498957, 0.03660019, 0.00022887986], "split_indices": [48, 61, 0, 58, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [7520.6333, 6462.923, 1057.7106, 6317.5723, 145.35046, 3405.6729, 2911.8994], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0034944853, 0.15390664, -0.10425567, 0.1816873, -0.11344172, 0.022694087, -0.052462127], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 27, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [1196.3823, 227.57533, 0.0, 198.91437, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.10425567, 99.0, -0.11344172, 0.022694087, -0.052462127], "split_indices": [48, 61, 0, 82, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [7313.1255, 6351.7847, 961.3409, 6218.5503, 133.23418, 5844.753, 373.7977], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0038131091, 0.14078401, -0.10381317, 0.4052548, 0.008511214, -0.016882803, 0.060322147, 0.01984476, -0.020714426], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 28, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [1066.197, 218.79536, 0.0, 237.08609, 170.8614, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 12.3, -0.10381317, 167.96, 9.0, -0.016882803, 0.060322147, 0.01984476, -0.020714426], "split_indices": [48, 58, 0, 50, 82, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [7126.9395, 6253.559, 873.38, 2084.2249, 4169.3345, 534.4685, 1549.7562, 2216.9011, 1952.4333], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0037059844, 0.12871481, -0.10341394, 0.22348079, -0.20984305, 0.0032231645, 0.041003745, -0.009409307, -0.08163602], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 29, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [951.605, 198.31934, 0.0, 172.27322, 94.840744, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.10341394, -0.25881904, 546.95, 0.0032231645, 0.041003745, -0.009409307, -0.08163602], "split_indices": [48, 59, 0, 1, 58, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [6972.003, 6178.837, 793.166, 4827.8184, 1351.0183, 2384.5068, 2443.3118, 1135.4751, 215.54317], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.003736808, 0.11752629, -0.10305299, 0.14256898, -0.111887954, 0.0018904236, 0.036920298], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 30, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [850.0157, 189.21057, 0.0, 167.74356, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.10305299, -1.8369701e-16, -0.111887954, 0.0018904236, 0.036920298], "split_indices": [48, 61, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [6824.799, 6104.7236, 720.07544, 5984.3975, 120.325836, 3872.4475, 2111.9502], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0039055564, 0.10713416, -0.102725945, 0.34806135, -0.011858804, -0.020998435, 0.053412158, 0.014015351, -0.024177521], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 31, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [759.827, 172.93877, 0.0, 207.18378, 141.19246, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 13.0, -0.102725945, 148.82, 1046.68, -0.020998435, 0.053412158, 0.014015351, -0.024177521], "split_indices": [48, 58, 0, 50, 70, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [6684.7114, 6031.1885, 653.52295, 1993.3309, 4037.8577, 498.39102, 1494.9398, 2430.9758, 1606.8817], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0037258854, 0.09775094, -0.10242893, 0.12051909, -0.11060077, 0.023915332, -0.008407745], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 32, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [680.1543, 163.85332, 0.0, 142.3421, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.10242893, 1355.7537, -0.11060077, 0.023915332, -0.008407745], "split_indices": [48, 61, 0, 87, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [6565.5415, 5972.579, 592.9623, 5862.593, 109.98646, 3710.7595, 2151.8333], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0038399552, 0.088973746, -0.10215844, 0.14193769, -0.37796888, -0.06114995, 0.018467695, -0.019524908, -0.09205267], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 33, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [609.0832, 146.19391, 0.0, 171.03482, 59.525116, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 284.86, -0.10215844, 1.918, 0.6666667, -0.06114995, 0.018467695, -0.019524908, -0.09205267], "split_indices": [48, 58, 0, 43, 37, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [6446.049, 5908.1636, 537.88525, 5306.912, 601.2515, 284.16406, 5022.748, 450.79498, 150.45656], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0037781468, 0.10002194, -0.83553815, 0.0057930285, 0.42202878, -0.9866269, -0.2938848, 0.011759035, -0.020247977, 0.073095195, 0.023414833, -0.10494788, -0.07364871, 0.01318449, -0.100366704], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 34, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [547.98456, 171.21602, 57.340485, 101.693596, 73.97241, 7.876953, 46.97338, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 0.25881904, -0.5, 1046.68, 14.48, -0.70710677, 1.0, 0.011759035, -0.020247977, 0.073095195, 0.023414833, -0.10494788, -0.07364871, 0.01318449, -0.100366704], "split_indices": [56, 1, 0, 70, 58, 0, 48, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [6345.0913, 5641.8877, 703.2037, 4365.445, 1276.4429, 549.19086, 154.01282, 2840.9214, 1524.5233, 481.63016, 794.8127, 436.354, 112.83689, 96.77881, 57.234013], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0036278006, 0.07385169, -0.10168844, 0.09461059, -0.109455064, -0.004779732, 0.024951266], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 35, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [490.68506, 140.93394, 0.0, 125.879166, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.10168844, -0.25881904, -0.109455064, -0.004779732, 0.024951266], "split_indices": [48, 61, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [6248.2827, 5805.3027, 442.98007, 5704.864, 100.43877, 2972.6628, 2732.201], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0036618854, 0.0670601, -0.101479866, 0.29346868, -0.0314586, -0.019785816, 0.049853466, -0.00079695147, -0.10809167], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 36, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [440.41696, 128.40604, 0.0, 175.95715, 98.887314, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 11.3, -0.101479866, 171.55, 1.0, -0.019785816, 0.049853466, -0.00079695147, -0.10809167], "split_indices": [48, 58, 0, 50, 61, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [6156.8994, 5755.2905, 401.60886, 1744.4174, 4010.8733, 513.6868, 1230.7305, 3924.0522, 86.82098], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0035363366, 0.06101695, -0.10128713, 0.11346674, -0.3429318, 0.0031979152, 0.042058606, -0.026209405, -0.12290046], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 37, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [395.68146, 120.971176, 0.0, 126.43612, 46.68026, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 1648.21, -0.10128713, 0.25881904, 6.0, 0.0031979152, 0.042058606, -0.026209405, -0.12290046], "split_indices": [48, 50, 0, 1, 46, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [6070.894, 5706.845, 364.04892, 5051.6455, 655.2, 3993.2305, 1058.4148, 601.61005, 53.589993], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0033904049, 0.05539213, -0.10110815, 0.084486745, -0.6277164, -0.04595807, 0.011765784, -0.04668489, -0.11720343], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 38, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [355.67523, 112.81476, 0.0, 98.28044, 19.758224, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 3.0, -0.10110815, 1.918, 287.53058, -0.04595807, 0.011765784, -0.04668489, -0.11720343], "split_indices": [48, 59, 0, 43, 76, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [6002.5693, 5672.609, 329.96033, 5441.715, 230.89442, 311.97015, 5129.7446, 179.65648, 51.237946], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0033011697, 0.05019093, -0.10094098, 0.06736455, -0.107512526, 0.017383216, -0.010951166], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 39, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [319.8557, 109.13187, 0.0, 104.71004, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.10094098, 1355.7537, -0.107512526, 0.017383216, -0.010951166], "split_indices": [48, 61, 0, 87, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5941.2485, 5642.2173, 299.03146, 5558.331, 83.88641, 3469.7761, 2088.5547], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0032697243, 0.045476645, -0.10078385, 0.21555585, -0.062035393, -0.02258655, 0.03729573, 0.0036377255, -0.029968455], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 40, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [287.78625, 102.50368, 0.0, 150.92981, 80.34601, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 20.99, -0.10078385, 140.29, 4.0, -0.02258655, 0.03729573, 0.0036377255, -0.029968455], "split_indices": [48, 58, 0, 50, 52, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5874.962, 5603.9854, 270.9768, 2170.0535, 3433.932, 570.28735, 1599.766, 2428.935, 1004.9968], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0029953306, 0.041377723, -0.10063511, 0.056924358, -0.106559336, 0.008362326, -0.05759899], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 41, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [259.16925, 95.999176, 0.0, 92.9612, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.10063511, 50.0, -0.106559336, 0.008362326, -0.05759899], "split_indices": [48, 61, 0, 83, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5819.22, 5573.685, 245.53548, 5497.426, 76.25893, 5275.7417, 221.68443], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.002984205, 0.03742532, -0.100493215, 0.13563159, -0.12815814, 0.01610396, -0.11941031, -0.010534688, -0.09270019], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 42, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [233.38835, 90.12064, 0.0, 117.71993, 37.502098, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.70710677, -0.100493215, 1.0, 112605.84, 0.01610396, -0.11941031, -0.010534688, -0.09270019], "split_indices": [48, 0, 0, 56, 88, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5762.4204, 5539.9517, 222.46893, 3477.5967, 2062.355, 3413.2566, 64.34021, 2006.199, 56.155827], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0029209547, 0.03386441, -0.10035669, 0.06063687, -0.557444, -0.046598744, 0.009363504, 0.017748354, -0.068250544], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 43, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [210.27742, 87.26396, 0.0, 91.665276, 22.067047, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 546.95, -0.10035669, 17.42, 9.375, -0.046598744, 0.009363504, 0.017748354, -0.068250544], "split_indices": [48, 58, 0, 42, 35, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5710.7207, 5509.161, 201.55954, 5271.3877, 237.77333, 310.05396, 4961.334, 34.516552, 203.25677], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0027698025, 0.030668696, -0.10022414, 0.22479747, -0.049624827, -0.017573437, 0.04162949, 0.008394456, -0.01886695], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 44, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [189.55142, 85.556145, 0.0, 123.25146, 72.13448, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 11.3, -0.10022414, 171.55, 9.0, -0.017573437, 0.04162949, 0.008394456, -0.01886695], "split_indices": [48, 58, 0, 50, 82, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5669.736, 5487.127, 182.60896, 1604.9579, 3882.169, 519.16534, 1085.7925, 1980.2722, 1901.8967], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0024847193, 0.027914327, -0.10009424, 0.041994363, -0.105649434, -0.0020346947, 0.028028414], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 45, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [170.97514, 83.51259, 0.0, 80.18837, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.10009424, 0.25881904, -0.105649434, -0.0020346947, 0.028028414], "split_indices": [48, 61, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5631.126, 5465.6895, 165.43636, 5396.581, 69.10829, 4278.227, 1118.354], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0024239104, 0.025217636, -0.099965684, 0.037983317, -0.10484748, 0.008961605, -0.022755468], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 46, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [154.20227, 74.64835, 0.0, 73.787224, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.099965684, 1465.19, -0.10484748, 0.008961605, -0.022755468], "split_indices": [48, 61, 0, 78, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5592.206, 5442.329, 149.87709, 5379.336, 62.993065, 4504.1797, 875.156], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0023706455, 0.02277012, -0.09983721, 0.12848541, -0.09826792, -0.031916454, 0.021960322, 0.0095786825, -0.023319108], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 47, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [139.11484, 69.35134, 0.0, 118.072586, 66.18431, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 48.99, -0.09983721, 6.61, 1425.0, -0.031916454, 0.021960322, 0.0095786825, -0.023319108], "split_indices": [48, 58, 0, 43, 96, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5553.7437, 5417.9624, 135.78143, 2892.0066, 2525.9558, 488.6804, 2403.3262, 1036.0851, 1489.8707], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.002122015, 0.02073004, -0.099707544, 0.032293282, -0.10410663, -0.0023324802, 0.025050852], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 48, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [125.58431, 66.327515, 0.0, 64.84369, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.099707544, 0.25881904, -0.10410663, -0.0023324802, 0.025050852], "split_indices": [48, 61, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5521.5024, 5398.4893, 123.01314, 5341.2905, 57.19848, 4257.1367, 1084.1539], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0020609892, 0.018704507, -0.09957545, -0.019469313, 0.33503532, 0.0064117583, -0.01930168, 0.03505109, -0.3136163], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 49, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [113.35498, 64.98638, 0.0, 69.68034, 32.229416, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 1538.46, -0.09957545, 1299.98, 27.0, 0.0064117583, -0.01930168, 0.03505109, -0.3136163], "split_indices": [48, 95, 0, 70, 52, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5491.555, 5380.107, 111.44844, 4801.603, 578.504, 3241.1672, 1560.4355, 576.8314, 1.6726453], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0019378643, 0.01691455, -0.09943968, 0.03026379, -0.8343485, -0.033794478, 0.006166771, -0.21821551, -0.041413445], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 50, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [102.350655, 61.0305, 0.0, 61.149715, 46.060127, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 11.0, -0.09943968, 2.802, 32.0, -0.033794478, 0.006166771, -0.21821551, -0.041413445], "split_indices": [48, 75, 0, 51, 74, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5468.4004, 5367.4253, 100.97485, 5285.5044, 81.92117, 414.5992, 4870.9053, 18.474041, 63.447132], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0018443606, 0.015264125, -0.09929895, 0.18374212, -0.052225534, -0.015071628, 0.035865527, -0.003937334, -0.10291983], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 51, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [92.42788, 60.936726, 0.0, 89.72304, 48.006794, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 11.3, -0.09929895, 171.55, 1.0, -0.015071628, 0.035865527, -0.003937334, -0.10291983], "split_indices": [48, 58, 0, 50, 61, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5448.8306, 5357.3403, 91.490234, 1531.7728, 3825.5676, 526.0431, 1005.7297, 3776.922, 48.64557], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0017114884, 0.027766958, -0.5268577, -0.03097306, 0.2637163, -1.0966389, -0.08986314, 0.006709223, -0.0155153815, 0.057372242, 0.011224325, -0.11894255, -0.09876322, -0.14609884, 0.02296979], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 52, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [83.958466, 71.18166, 71.67578, 50.092667, 48.00683, 0.08988953, 72.29397, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 0.25881904, -0.70710677, 9.0, 14.48, 1.0, 1.0, 0.006709223, -0.0155153815, 0.057372242, 0.011224325, -0.11894255, -0.09876322, -0.14609884, 0.02296979], "split_indices": [56, 1, 0, 82, 58, 48, 75, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5421.5557, 5134.2954, 287.26035, 4111.579, 1022.7163, 124.03115, 163.2292, 2297.616, 1813.9631, 334.7751, 687.9412, 61.64626, 62.38489, 30.17725, 133.05194], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0016017198, 0.015028192, -0.8599208, 0.030587241, -0.6610051, 0.2727079, -1.0958161, -0.0003928755, 0.03196424, 0.01044463, -0.08775961, 0.044575743, -0.13465348, -0.21537042, -0.08289447], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 53, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [77.008385, 55.690605, 27.696503, 51.627724, 19.83976, 5.7774544, 22.230759, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 855.01, 3125.49, 1538.46, 15.175, 2.8, 4.0, -0.0003928755, 0.03196424, 0.01044463, -0.08775961, 0.044575743, -0.13465348, -0.21537042, -0.08289447], "split_indices": [46, 58, 88, 95, 35, 31, 93, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5393.1924, 5291.6494, 101.54308, 5173.534, 118.11524, 17.476294, 84.06679, 4622.544, 550.9903, 26.164696, 91.95055, 16.44366, 1.0326322, 15.510889, 68.5559], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.001483582, 0.012369727, -0.09896154, 0.021959348, -0.102832794, 0.00546107, -0.027935743], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 54, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [73.68279, 52.99941, 0.0, 51.773766, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.09896154, 219.69, -0.102832794, 0.00546107, -0.027935743], "split_indices": [48, 61, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5380.775, 5307.355, 73.42003, 5259.866, 47.488705, 4746.359, 513.50714], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0014609484, 0.011130092, -0.09879554, 0.087482676, -0.11034858, 0.01100848, -0.11665173, 4.5532594e-05, -0.021403551], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 55, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [66.5678, 49.092888, 0.0, 92.24455, 23.470009, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.70710677, -0.09879554, 1.0, 1565.6267, 0.01100848, -0.11665173, 4.5532594e-05, -0.021403551], "split_indices": [48, 0, 0, 56, 96, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5357.4136, 5290.8726, 66.54089, 3249.0425, 2041.8301, 3192.4146, 56.62792, 987.5216, 1054.3085], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0014164177, 0.010031409, -0.09861879, 0.018761564, -0.1021711, -0.03413249, 0.004379962], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 56, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [60.151005, 47.527332, 0.0, 47.178295, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.09861879, 17.42, -0.1021711, -0.03413249, 0.004379962], "split_indices": [48, 61, 0, 42, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5333.7363, 5273.4233, 60.312977, 5230.15, 43.273434, 339.20587, 4890.9443], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0013820266, 0.012367121, -0.76622635, 0.025940405, -0.6780431, -0.060355455, -1.1210876, -0.014734626, 0.007672202, -0.032655638, -0.20212126, 0.041073576, -0.12642194, -0.28783947, -0.085281186], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 57, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [55.928917, 48.978584, 23.457695, 45.109177, 46.915, 18.93784, 26.983086, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 9.0, 100.90444, 140.29, 977.5058, 759.0, 121.169106, -0.014734626, 0.007672202, -0.032655638, -0.20212126, 0.041073576, -0.12642194, -0.28783947, -0.085281186], "split_indices": [46, 75, 94, 50, 86, 72, 94, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5316.5796, 5223.6606, 92.918785, 5123.8887, 99.77188, 31.476835, 61.44195, 1160.8193, 3963.0696, 80.0636, 19.708284, 23.09731, 8.379527, 6.8490257, 54.592926], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0013243486, 0.032609798, -0.295789, 0.113337316, -0.09380131, -0.22346318, -0.9383911, 0.01425521, -0.11044743, 0.0016369999, -0.019494608, -0.018747177, -0.13417035, -0.112607434, -0.018671846], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 58, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [53.06443, 48.603775, 25.294048, 103.56274, 20.688816, 19.652292, 7.577305, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [219.69, -0.70710677, 0.5, 1.0, 1459.6729, 124.0, 13967.66, 0.01425521, -0.11044743, 0.0016369999, -0.019494608, -0.018747177, -0.13417035, -0.112607434, -0.018671846], "split_indices": [2, 0, 47, 56, 96, 83, 70, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5308.51, 4760.7676, 547.74225, 2905.431, 1855.3365, 493.54117, 54.201057, 2838.226, 67.20489, 888.44336, 966.8932, 479.28275, 14.258431, 42.973114, 11.227943], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0012626272, 0.007975668, -0.10132809, -0.022648262, 0.27591258, 0.0056057475, -0.015576151, 0.02915825, -0.2667502], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 59, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [49.43734, 43.00276, 0.0, 49.286663, 25.57183, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 1538.46, -0.10132809, 399.4, 27.0, 0.0056057475, -0.015576151, 0.02915825, -0.2667502], "split_indices": [61, 95, 0, 77, 52, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5285.9355, 5239.102, 46.833473, 4702.539, 536.5626, 2955.5771, 1746.9622, 534.61743, 1.9452169], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0011812856, 0.008146987, -0.09819177, 0.026700037, -0.4078025, -0.017167801, 0.0066753477, -0.062254082, -0.008671215], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 60, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [48.27027, 40.345818, 0.0, 39.77532, 15.338364, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 3.0, -0.09819177, 8.215834, 4.0, -0.017167801, 0.0066753477, -0.062254082, -0.008671215], "split_indices": [48, 59, 0, 43, 74, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5274.3857, 5225.674, 48.712296, 5003.4355, 222.2379, 839.96124, 4163.4746, 132.6114, 89.626495], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0010966788, 0.0071563846, -0.100818716, 0.03903486, -0.22798531, -0.0008627241, 0.025222344, -0.0119756935, -0.07481897], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 61, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [43.75637, 39.15164, 0.0, 46.73558, 35.017464, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 1506.69, -0.100818716, 0.25881904, 398.56213, -0.0008627241, 0.025222344, -0.0119756935, -0.07481897], "split_indices": [61, 42, 0, 1, 94, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5262.638, 5220.846, 41.791718, 4598.2847, 622.5616, 3758.8796, 839.4049, 516.36273, 106.198906], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0009878096, 0.007311188, -0.09791743, 0.1523547, -0.04450029, -0.015085818, 0.032554407, -0.0036636323, -0.09987892], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 62, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [42.63827, 39.146473, 0.0, 72.0459, 28.767502, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 9.84, -0.09791743, 171.55, 1.0, -0.015085818, 0.032554407, -0.0036636323, -0.09987892], "split_indices": [48, 58, 0, 50, 61, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5250.4517, 5207.265, 43.186253, 1370.0215, 3837.244, 498.09897, 871.9225, 3806.9106, 30.333057], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0009045739, 0.005335367, -1.1854764, 0.0126848845, -0.09758277, -2.3875148, -0.17983712, -0.00046105628, 0.042580765, -0.2691868, 0.03038581, -0.1986153, 0.040540546], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 63, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [38.705616, 37.575573, 32.73946, 36.952038, 0.0, 10.989517, 17.74207, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [470.0, 1.0, 2713.9368, 801.945, -0.09758277, 217.51811, 1.0, -0.00046105628, 0.042580765, -0.2691868, 0.03038581, -0.1986153, 0.040540546], "split_indices": [92, 48, 78, 94, 0, 94, 97, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5234.5366, 5208.0977, 26.439075, 5170.3555, 37.742085, 11.417562, 15.021514, 4963.5444, 206.81071, 10.257421, 1.1601408, 3.0901687, 11.931345], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.0009096161, 0.012402939, -0.53944135, -0.015984163, 0.26645145, 0.4798661, -0.6627744, -0.03698944, 0.0007031769, 0.048890337, 0.0064116344, 0.09166794, -0.023655618, -0.07402981, 0.070395835], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 64, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [37.477962, 36.79524, 16.17181, 37.388844, 23.049965, 4.6581, 12.485241, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [855.01, 1538.46, 5.54, 1.5608333, 458.996, 1.0, 5312.79, -0.03698944, 0.0007031769, 0.048890337, 0.0064116344, 0.09166794, -0.023655618, -0.07402981, 0.070395835], "split_indices": [58, 95, 11, 43, 94, 60, 78, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5225.6772, 5100.568, 125.109535, 4588.7617, 511.80612, 13.191904, 111.91763, 279.27948, 4309.4824, 243.10953, 268.6966, 8.021533, 5.1703715, 106.34394, 5.5736904], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007331973, 0.07484286, -0.09268648, -0.26132554, 0.12927964, -0.92207295, -0.06868518, -0.020413822, -0.24863108, 0.014835335, -0.06720386, -0.31624338, -0.049122978, 0.07963806, -0.00873689], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 65, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [36.288937, 52.484234, 46.829582, 50.360016, 37.811443, 62.31891, 37.090164, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [794.68, 2.802, 137.0, 4618.745, 1.0, 1.0, 55.0, -0.020413822, -0.24863108, 0.014835335, -0.06720386, -0.31624338, -0.049122978, 0.07963806, -0.00873689], "split_indices": [70, 51, 71, 96, 56, 97, 90, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5219.832, 2865.1565, 2354.6753, 398.77353, 2466.3828, 65.17226, 2289.5032, 389.84534, 8.928191, 2409.8367, 56.54627, 9.489142, 55.683113, 47.523346, 2241.9797], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.00060721516, 0.006100281, -0.09983735, 0.076966465, -0.08438523, -0.010333437, 0.019006759, 0.001928756, -0.02127821], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 66, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [34.879143, 33.203, 0.0, 59.2334, 30.276731, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 39.56, -0.09983735, 148.82, 0.22222222, -0.010333437, 0.019006759, 0.001928756, -0.02127821], "split_indices": [61, 2, 0, 50, 53, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5209.7554, 5175.9536, 33.80153, 2902.742, 2273.2117, 1118.9877, 1783.7544, 1258.167, 1015.0448], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0005196678, 0.0059675537, -0.09723976, 0.26499933, -0.017925188, 0.029457334, -0.33635002, 0.005752445, -0.010085594], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 67, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [32.779728, 31.9695, 0.0, 47.801823, 29.599115, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.47, -0.09723976, 284.0, 9.0, 0.029457334, -0.33635002, 0.005752445, -0.010085594], "split_indices": [48, 58, 0, 92, 82, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5197.2603, 5163.786, 33.474075, 435.22528, 4728.5605, 432.61804, 2.6072283, 2476.118, 2252.4429], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0005252489, 0.0055075996, -0.09929142, -0.009763055, 0.3876399, 0.0022425118, -0.019196026, 0.04398261, -0.13122223], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 68, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [31.067352, 30.102398, 0.0, 29.096811, 18.161438, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 801.945, -0.09929142, 441.56833, 4.0, 0.0022425118, -0.019196026, 0.04398261, -0.13122223], "split_indices": [61, 94, 0, 94, 84, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5187.281, 5156.9253, 30.355103, 4959.7017, 197.22363, 4215.789, 743.91254, 192.06834, 5.1552887], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.00048450218, -0.013722611, 0.43733564, -0.008174986, -1.0440861, 0.66977894, -0.44600308, -0.00024991186, -0.0967313, -0.21471672, -0.013213935, -0.21088405, 0.07990443, 0.101492755, -0.16646062], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 69, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [30.049873, 28.7574, 31.542147, 27.252808, 26.81915, 45.09728, 59.192513, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2538.15, 470.0, 4173.68, 1.0, 2713.9368, 438.13, 486.74, -0.00024991186, -0.0967313, -0.21471672, -0.013213935, -0.21088405, 0.07990443, 0.101492755, -0.16646062], "split_indices": [95, 92, 95, 48, 78, 79, 85, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5182.633, 5031.468, 151.16486, 5005.5254, 25.942533, 119.865105, 31.299747, 4977.075, 28.45011, 11.127879, 14.814653, 4.6476483, 115.21746, 14.310567, 16.989182], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.00044504876, 0.004971386, -0.0987321, -0.017841514, 0.23208028, 0.0062253657, -0.008657049, 0.030480115, -0.11027979], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 70, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [27.676409, 26.683668, 0.0, 25.770138, 45.972023, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.5, -0.0987321, 1443.46, 1.0, 0.0062253657, -0.008657049, 0.030480115, -0.11027979], "split_indices": [61, 1, 0, 96, 56, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5175.744, 5148.4814, 27.262302, 4679.3647, 469.11688, 2161.0366, 2518.3281, 445.61267, 23.504213], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.00043056402, 0.00875323, -0.5663758, 0.018965483, -0.48769742, 0.44873196, -0.799726, 0.0005508242, 0.049083073, -0.017408652, -0.16369629, 0.07008363, -0.061462235, -0.19649026, -0.0428409], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 71, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [26.852333, 25.784103, 19.900576, 31.635626, 36.661346, 4.736134, 28.335041, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 9.0, 3125.49, 2385.0, 977.5058, 1.5833334, 6.0, 0.0005508242, 0.049083073, -0.017408652, -0.16369629, 0.07008363, -0.061462235, -0.19649026, -0.0428409], "split_indices": [46, 75, 88, 72, 86, 47, 74, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5164.4165, 5082.9185, 81.497795, 4981.41, 101.50844, 15.060297, 66.4375, 4844.267, 137.14316, 80.651726, 20.856718, 12.449612, 2.610685, 15.018474, 51.41902], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.00041512927, 0.009305012, -0.5213052, -0.015000948, 0.23449974, -0.37952104, -1.6667167, -0.019838572, 0.0020052067, 0.025007969, -0.2471799, 0.0067852335, -0.076999865, -0.043491643, -0.2131028], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 72, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [26.135735, 27.740622, 14.7242985, 29.411226, 21.435535, 14.977727, 4.8617954, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [994.87, 1538.46, 528.75336, 7.39, 27.0, 78.12, 944.2775, -0.019838572, 0.0020052067, 0.025007969, -0.2471799, 0.0067852335, -0.076999865, -0.043491643, -0.2131028], "split_indices": [58, 95, 94, 43, 52, 35, 96, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5160.0186, 5066.4575, 93.5612, 4573.7334, 492.7238, 84.44016, 9.12104, 733.2057, 3840.5278, 490.80582, 1.9179404, 39.738117, 44.702045, 3.0270593, 6.093981], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0004410076, 0.11518526, -0.04382343, -0.09478878, 0.26099217, 0.10477535, -0.08254844, -0.019086352, 0.061098583, 0.0415713, -0.0077265142, 0.01888245, -0.024582466, -0.051477402, -0.0054660696], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 73, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [25.867378, 43.099117, 21.587269, 39.263584, 43.502525, 22.902802, 35.834705, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [11.3, 162.91, 3320.0, 1381.64, 44.69, 373.2796, 45.324, -0.019086352, 0.061098583, 0.0415713, -0.0077265142, 0.01888245, -0.024582466, -0.051477402, -0.0054660696], "split_indices": [58, 42, 89, 95, 11, 85, 85, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5154.8154, 1405.9392, 3748.876, 576.3284, 829.61084, 774.64386, 2974.2322, 507.91696, 68.41145, 569.08093, 260.52988, 625.2213, 149.42253, 179.21178, 2795.0205], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.00022107114, 0.00482466, -0.09638635, -0.00872135, 0.35688585, 0.0009566, -0.02987859, 0.021744603, 0.10098528], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 74, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [25.055197, 24.449965, 0.0, 26.192413, 17.075495, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 801.945, -0.09638635, 578.171, 480.57, 0.0009566, -0.02987859, 0.021744603, 0.10098528], "split_indices": [48, 94, 0, 94, 77, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5151.016, 5125.1753, 25.840952, 4936.2227, 188.95232, 4644.3813, 291.84167, 156.80081, 32.1515], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.00017480178, 0.004478276, -1.017838, 0.23591582, -0.016093124, -0.46380863, -2.1256633, -0.0143895, 0.04331973, -0.0027245083, 0.049998995, -0.10409867, 0.10955429, -0.11850875, -0.26592955], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 75, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [24.38198, 24.406853, 13.398228, 31.417871, 27.110641, 16.352509, 0.62994766, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [6423971.0, 0.47, 2968267.0, 152.94, 8224.0, 2937.0, -0.25881904, -0.0143895, 0.04331973, -0.0027245083, 0.049998995, -0.10409867, 0.10955429, -0.11850875, -0.26592955], "split_indices": [80, 58, 71, 50, 81, 72, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5147.0576, 5124.6216, 22.43579, 417.46652, 4707.1553, 15.901852, 6.5339365, 142.8052, 274.66132, 4608.5176, 98.637535, 11.848627, 4.0532246, 3.5310066, 3.0029302], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.00015414032, 0.004658749, -0.09812241, 0.064928, -0.06736395, -0.02039316, 0.01112244, -0.08147936, -0.004489539], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 76, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [24.300772, 22.235264, 0.0, 34.73468, 39.15483, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 794.68, -0.09812241, 2.802, 137.0, -0.02039316, 0.01112244, -0.08147936, -0.004489539], "split_indices": [61, 70, 0, 51, 71, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5144.559, 5120.435, 24.12435, 2787.733, 2332.702, 409.01767, 2378.7153, 67.04797, 2265.6538], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.00013110238, 0.0044188625, -0.09597811, -0.0067383093, 0.395265, -0.0012321601, 0.09227134, 0.06310854, -0.041657344], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 77, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [22.440472, 22.308537, 0.0, 25.824368, 27.445173, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 2538.15, -0.09597811, 1699.54, 4173.68, -0.0012321601, 0.09227134, 0.06310854, -0.041657344], "split_indices": [48, 95, 0, 85, 95, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5137.4126, 5114.16, 23.252247, 4973.1777, 140.98256, 4944.463, 28.715042, 109.420944, 31.561619], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-7.81048e-05, 0.02518225, -0.16936478, -0.07590783, 0.07425416, -0.13756101, -0.9784027, 0.0016768819, -0.036932003, -0.0220159, 0.012154941, -0.016879437, 0.086152814, -0.12600528, 0.06383016], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 78, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [21.958508, 22.171288, 17.020887, 39.726032, 41.919147, 20.2003, 12.212927, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.0, 166.28, 46.0, 68.0, 1.0, 32.0, 47.0, 0.0016768819, -0.036932003, -0.0220159, 0.012154941, -0.016879437, 0.086152814, -0.12600528, 0.06383016], "split_indices": [12, 42, 74, 72, 97, 74, 44, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5133.0034, 4467.2656, 665.73816, 1459.6932, 3007.5723, 641.6832, 24.05493, 1110.0254, 349.66776, 415.76675, 2591.8054, 623.03705, 18.646233, 20.673885, 3.3810444], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [8.666489e-06, 0.061322313, -0.06946665, -0.06172499, 0.17697419, -0.060682595, -1.2097193, -0.011825357, 0.035884615, 0.044832863, 0.0067838705, 0.00127887, -0.01541905, -0.15027948, 0.010000744], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 79, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [21.862583, 38.80791, 24.025438, 31.46388, 41.596783, 16.412575, 7.1902924, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [34.81, 166.28, 2066.13, 1381.64, 4.28, 0.22222222, 156.0, -0.011825357, 0.035884615, 0.044832863, 0.0067838705, 0.00127887, -0.01541905, -0.15027948, 0.010000744], "split_indices": [2, 42, 58, 95, 58, 53, 82, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5130.321, 2725.2974, 2405.0234, 1320.6566, 1404.6406, 2387.6829, 17.340555, 1164.814, 155.84274, 402.0, 1002.6406, 1337.5793, 1050.1034, 14.049345, 3.2912097], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [9.8915225e-05, 0.0044174558, -0.09749153, 0.006253953, -0.2138115, -0.0012896267, 0.020483697], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 80, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [21.576279, 20.086958, 0.0, 19.390776, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [1.0, 194.0, -0.09749153, 0.5, -0.2138115, -0.0012896267, 0.020483697], "split_indices": [61, 93, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5122.207, 5100.611, 21.596195, 5097.239, 3.3720777, 4649.7754, 447.46292], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [2.743391e-05, 0.0041142777, -0.095507376, 0.07038598, -0.051838826, 0.0045254696, 0.05417207, -0.036141858, -0.00050113833], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 81, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [19.984148, 18.907295, 0.0, 27.624592, 40.077984, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 1425.0, -0.095507376, 21.0, 501.0, 0.0045254696, 0.05417207, -0.036141858, -0.00050113833], "split_indices": [48, 96, 0, 82, 71, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5117.737, 5096.923, 20.81386, 2333.1943, 2763.7285, 2216.1277, 117.0666, 362.23666, 2401.492], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [5.2280768e-05, 0.0039544543, -0.09688328, -0.007777652, 0.32028016, 0.0021684158, -0.017175758, 0.018597087, 0.09763027], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 82, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [19.34249, 18.91274, 0.0, 23.745176, 15.87652, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 801.945, -0.09688328, 441.56833, 487.1554, 0.0021684158, -0.017175758, 0.018597087, 0.09763027], "split_indices": [61, 94, 0, 94, 77, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5114.0215, 5094.4995, 19.52212, 4913.249, 181.25055, 4165.6807, 747.56836, 151.51411, 29.736427], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [7.2034476e-05, 0.0059654284, -0.61397123, 0.28706902, -0.008591929, 1.033489, -0.9944798, -0.002618039, 0.052720845, -0.001758175, 0.045766953, 0.13338725, -0.06517611, -0.18735164, -0.035947274], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 83, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [18.50398, 20.72774, 31.49959, 18.774296, 20.19598, 5.7346363, 21.653347, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1070.0, 0.11, 164.0, 164.0, 8224.0, 301974.4, 2713.9368, -0.002618039, 0.052720845, -0.001758175, 0.045766953, 0.13338725, -0.06517611, -0.18735164, -0.035947274], "split_indices": [91, 58, 82, 71, 81, 88, 78, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5111.2812, 5063.672, 47.609245, 248.3966, 4815.2754, 8.610952, 38.99829, 108.1767, 140.21991, 4725.1343, 90.14133, 7.485261, 1.1256919, 15.538311, 23.45998], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [9.8217024e-05, 0.0037875294, -0.09502361, -0.005985816, 0.36066714, -0.0010985044, 0.089532435, -0.15804306, 0.045793016], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 84, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [17.919195, 17.759449, 0.0, 22.342554, 26.347736, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 2538.15, -0.09502361, 1699.54, 393.54, -0.0010985044, 0.089532435, -0.15804306, 0.045793016], "split_indices": [48, 95, 0, 85, 79, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5108.865, 5090.101, 18.764372, 4955.3784, 134.72255, 4929.027, 26.35164, 5.700833, 129.02171], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [9.780519e-05, 0.060285658, -0.059063334, 0.015466903, 0.37085545, -1.2933208, -0.042615745, 0.004599331, -0.07360681, 0.04427786, -0.10722239, -0.0554821, -0.35823655, -0.40660235, -0.0038582515], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 85, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [18.185303, 35.232307, 52.229443, 50.812172, 33.60687, 55.210354, 41.104145, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2147.74, 14046.0, 256.0, 644.0, 12.0, 19.0, 18.0, 0.004599331, -0.07360681, 0.04427786, -0.10722239, -0.0554821, -0.35823655, -0.40660235, -0.0038582515], "split_indices": [79, 71, 80, 81, 73, 91, 71, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5105.11, 2530.587, 2574.523, 2212.367, 318.21994, 32.835735, 2541.6873, 2126.913, 85.453735, 303.77304, 14.446899, 25.765545, 7.0701914, 1.5367622, 2540.1506], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [8.498102e-05, 0.005327127, -0.64786357, -0.022358455, 0.12334537, 0.10728886, -0.8947711, 0.0047363034, -0.01480396, 0.013587807, -0.26996273, 0.042667724, -0.13685563, -0.1326618, -0.02759326], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 86, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [17.339256, 16.548367, 7.7346897, 35.955826, 34.41419, 5.763117, 7.913391, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1601.78, 1162.462, 47.77, 420.8775, 16.0, 510.9, 9.0, 0.0047363034, -0.01480396, 0.013587807, -0.26996273, 0.042667724, -0.13685563, -0.1326618, -0.02759326], "split_indices": [58, 95, 10, 77, 75, 94, 83, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5102.809, 5062.8403, 39.96849, 4101.4927, 961.3476, 9.987567, 29.980925, 2638.4426, 1463.0502, 958.0462, 3.30142, 8.795106, 1.1924603, 16.984695, 12.996228], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [1.6480488e-05, 0.0035200252, -0.09619764, -0.04690206, 0.06855232, -0.006118056, 0.032606594, 0.00960734, -0.11124011], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 87, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [17.186342, 16.663202, 0.0, 15.26355, 72.23634, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.25881904, -0.09619764, 2385.0, 1.0, -0.006118056, 0.032606594, 0.00960734, -0.11124011], "split_indices": [61, 1, 0, 72, 56, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5097.2173, 5079.714, 17.5038, 2861.421, 2218.2927, 2756.7202, 104.700775, 2168.6724, 49.62034], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-5.3009415e-05, 0.0076488857, -0.40758562, 0.013550785, -0.5417896, -0.249992, -0.336968, 0.00041584516, 0.05776242, 0.121570304, -0.08031892, 0.032444045, -0.06468579], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 88, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [15.967522, 16.197754, 13.099634, 26.173506, 25.364527, 0.0, 19.155437, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [0.5, 35.0, 24.968334, 8224.0, 286.0, -0.249992, 154.04, 0.00041584516, 0.05776242, 0.121570304, -0.08031892, 0.032444045, -0.06468579], "split_indices": [47, 84, 27, 81, 82, 0, 27, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5085.1924, 4991.8335, 93.35865, 4939.7476, 52.085915, 1.9248991, 91.43375, 4859.8345, 79.91328, 6.271228, 45.814686, 29.15669, 62.27706], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-4.9000922e-05, 0.0031338406, -0.09552955, -0.013707339, 0.18271556, -0.0018009583, 0.06996532, 0.024497239, -0.108976595], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 89, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [15.457369, 15.327361, 0.0, 14.233368, 34.690094, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.5, -0.09552955, 1699.54, 1.0, -0.0018009583, 0.06996532, 0.024497239, -0.108976595], "split_indices": [61, 1, 0, 85, 56, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5082.0103, 5066.127, 15.883405, 4632.605, 433.52216, 4605.8022, 26.802624, 414.071, 19.45116], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-6.552769e-05, 0.055277232, -0.05458277, -0.05980758, 0.1894785, -0.03929778, -0.50290143, 0.002963825, -0.03287773, 0.021819571, -0.071861364, 0.025604224, -0.0061002304, -0.058170494, 0.09553237], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 90, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [15.318395, 38.92369, 17.5066, 32.659374, 30.495335, 15.87706, 10.236069, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [35.845, 166.28, 855.01, 68.0, 22.0, 65.93, 5619.71, 0.002963825, -0.03287773, 0.021819571, -0.071861364, 0.025604224, -0.0061002304, -0.058170494, 0.09553237], "split_indices": [11, 42, 58, 72, 74, 88, 78, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5075.1333, 2518.4917, 2556.6414, 1356.1094, 1162.3824, 2473.401, 83.24056, 1018.3478, 337.76157, 1127.4868, 34.895554, 168.58722, 2304.8137, 79.54309, 3.6974757], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [2.772409e-05, 0.0030271818, -0.09387677, -0.005428188, 0.32457647, -0.0009628565, 0.08499344, 0.05708759, -0.045585256], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 91, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [14.277978, 13.74367, 0.0, 17.705997, 25.156332, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 2538.15, -0.09387677, 1699.54, 4173.68, -0.0009628565, 0.08499344, 0.05708759, -0.045585256], "split_indices": [48, 95, 0, 85, 95, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5068.5166, 5053.3677, 15.148767, 4924.848, 128.5193, 4901.766, 23.082066, 97.89322, 30.626091], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [5.6625202e-05, 0.0029350133, -0.09480917, 0.005128448, -1.2400321, -0.00054686744, 0.029592741, -0.1835676, 0.09750977], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 92, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [13.829709, 13.780156, 0.0, 15.545881, 13.537733, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 2968267.0, -0.09480917, 2385.0, 2869.312, -0.00054686744, 0.029592741, -0.1835676, 0.09750977], "split_indices": [61, 71, 0, 72, 96, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5065.4067, 5051.07, 14.337131, 5043.166, 7.9036813, 4866.7944, 176.37164, 6.363776, 1.5399052], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [1.33495505e-05, 0.09981012, -0.026540386, 0.11863016, -1.690437, -0.43987986, 0.0028134615, 0.00087927683, 0.050433885, 0.04517479, -0.3010515, -0.15339477, -0.022261042, 0.0025902267, -0.028715579], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 93, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [13.422306, 36.065228, 48.54087, 44.66255, 32.860687, 62.716488, 25.022259, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [114.87556, 37876.44, 143.23286, 242.454, 9.0, 1.0, 96.0, 0.00087927683, 0.050433885, 0.04517479, -0.3010515, -0.15339477, -0.022261042, 0.0025902267, -0.028715579], "split_indices": [94, 88, 94, 95, 84, 97, 82, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5063.0654, 1063.4705, 3999.5947, 1053.3309, 10.139444, 264.27542, 3735.3193, 820.6571, 232.67387, 4.1167264, 6.0227184, 42.782505, 221.49292, 3460.6738, 274.64554], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [5.5641878e-05, 0.010807628, -0.23563685, 0.17921261, -0.009617217, -0.45177013, 0.09947571, 0.025164222, -0.05166281, -0.045794886, 0.0013579332, -0.026650239, -0.09233585, 0.038143873, -0.0453791], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 94, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [12.822749, 16.646833, 16.034214, 26.513323, 44.8989, 11.495007, 13.803604, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3.0, 4.0, 4.0, 2267.0, 256.0, 253.08223, 284.025, 0.025164222, -0.05166281, -0.045794886, 0.0013579332, -0.026650239, -0.09233585, 0.038143873, -0.0453791], "split_indices": [59, 81, 74, 89, 80, 76, 11, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5057.951, 4838.193, 219.75795, 522.48395, 4315.709, 133.3833, 86.37465, 473.80432, 48.679634, 211.38673, 4104.3223, 96.88591, 36.49739, 57.421383, 28.953268], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [8.246815e-05, -0.009514233, 0.2713412, 0.019320888, -0.16880625, 0.32790008, -1.3124286, -0.0014433139, 0.027022187, -0.007942723, -0.073185384, 0.038309246, -0.17539237, -0.3579144, 0.08371281], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 95, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [13.164162, 22.437172, 15.908502, 35.03501, 37.620903, 19.933357, 32.999874, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [801.945, 441.56833, 4.0, 368.7257, 7.0, 2.0, 480.57, -0.0014433139, 0.027022187, -0.007942723, -0.073185384, 0.038309246, -0.17539237, -0.3579144, 0.08371281], "split_indices": [94, 94, 84, 94, 83, 46, 77, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5054.945, 4883.152, 171.79318, 4135.4326, 747.7193, 166.63535, 5.157842, 3645.889, 489.54358, 646.2704, 101.44889, 163.12659, 3.508758, 2.1867962, 2.9710457], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [8.777611e-05, 0.0027749238, -0.09318319, -0.17687157, 0.01709052, -0.035832386, 0.015073351, 0.020146472, -0.0013078124], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 96, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [12.661046, 12.968081, 0.0, 22.177565, 25.979633, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 17.42, -0.09318319, 118.17857, 9.95, -0.035832386, 0.015073351, 0.020146472, -0.0013078124], "split_indices": [48, 42, 0, 85, 10, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5053.8804, 5040.3438, 13.536577, 371.17032, 4669.1733, 238.80717, 132.36314, 655.77264, 4013.401], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [9.087996e-05, 0.018629404, -0.1348022, 0.0037321278, 0.51673007, -1.0823948, -0.050639033, 0.0063117105, -0.0075997943, -0.22302225, 0.061362695, 0.08624777, -0.15945843, -0.07643265, 0.0016082634], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 97, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [12.634173, 32.957558, 48.629177, 20.42961, 35.69934, 50.929546, 26.759228, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3797.1626, 3517.3174, 7.0, 794.68, 765.0, 350.0, 4462.6, 0.0063117105, -0.0075997943, -0.22302225, 0.061362695, 0.08624777, -0.15945843, -0.07643265, 0.0016082634], "split_indices": [96, 96, 72, 70, 89, 80, 88, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5050.213, 4440.774, 609.43915, 4312.7935, 127.98024, 48.746124, 560.69305, 2471.8818, 1840.9116, 3.6104445, 124.36979, 10.018859, 38.727264, 47.042973, 513.6501], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.00017611813, 0.0027622825, -0.09399304, -0.020379765, 0.109105155, -0.0015656537, -0.06530558, 0.015225879, -0.110234015], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 98, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [12.279944, 12.398019, 0.0, 12.35387, 47.237816, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.25881904, -0.09399304, 9289.74, 1.0, -0.0015656537, -0.06530558, 0.015225879, -0.110234015], "split_indices": [61, 1, 0, 96, 56, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5048.767, 5035.9106, 12.856361, 4136.537, 899.37366, 4106.902, 29.635233, 869.2827, 30.090929], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [9.5165786e-05, -0.086670324, 0.028013544, 0.070999846, -0.26387176, 0.41268113, -0.00075982005, 0.009979962, -0.21742128, -0.022149932, -0.2015115, 0.050512392, -0.038365107, -0.08776932, 0.001610279], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 99, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [12.214237, 34.31398, 42.223377, 42.268257, 42.455666, 19.738125, 52.51411, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [279.0, 2.0, 3320.0, 163.0, 1913.77, 563.3907, 4295.0, 0.009979962, -0.21742128, -0.022149932, -0.2015115, 0.050512392, -0.038365107, -0.08776932, 0.001610279], "split_indices": [71, 72, 89, 81, 50, 85, 89, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5040.3027, 1226.4875, 3813.8154, 649.3282, 577.15936, 264.49338, 3549.322, 642.04803, 7.2801237, 564.6244, 12.534971, 237.31068, 27.182692, 65.999405, 3483.3228], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "6.558235E-1", "boost_from_average": "1", "num_class": "0", "num_feature": "98", "num_target": "1"}, "objective": {"name": "binary:logistic", "reg_loss_param": {"scale_pos_weight": "1"}}}, "version": [3, 0, 1]}