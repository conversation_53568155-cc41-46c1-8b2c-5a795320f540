import datetime
import json

dapan_step_1_cost = 220
dapan_step_2_cost = 701
dapan_step_3_cost = 2588


def get_pre_effect_limits(account_status, scale_ratio, control_config):
    dapan_follow_cost_14 = account_status['past2WeeksRegCost']  # 关注成本
    dapan_register_cost_14 = account_status['past2WeeksApplyCost']  # 注册成本
    dapan_reservation_cost_14 = account_status['past2WeeksCreditCost']  # 表单预约成本
    print(f"大盘前效数据: 14天关注成本: {dapan_follow_cost_14}; 14天注册成本：{dapan_register_cost_14}; "
          f"14天表单成本{dapan_reservation_cost_14}")

    # 配置
    step_1_final_cost_goal = int(dapan_follow_cost_14 * scale_ratio)
    step_2_final_cost_goal = int(dapan_register_cost_14 * scale_ratio)
    step_3_final_cost_goal = int(dapan_reservation_cost_14 * scale_ratio)

    now = datetime.datetime.now()
    day = str(now).split(' ')[0]
    hour = int(str(now).split(' ')[1].split(':')[0])
    if hour < 12:  # 上午适当放开成本约束系数
        adgroup_step_1_cost_limit = step_1_final_cost_goal * control_config.STEP_1_COST_LIMIT_SCALE * control_config.MORNING_SCALE
        adgroup_step_2_cost_limit = step_2_final_cost_goal * control_config.STEP_2_COST_LIMIT_SCALE * control_config.MORNING_SCALE
        adgroup_step_3_cost_limit = step_3_final_cost_goal * control_config.STEP_3_COST_LIMIT_SCALE * control_config.MORNING_SCALE
    elif hour >= 12 and hour < 20:  # 上午适当放开成本约束系数
        adgroup_step_1_cost_limit = step_1_final_cost_goal * control_config.STEP_1_COST_LIMIT_SCALE * control_config.AFTERNOON_SCALE
        adgroup_step_2_cost_limit = step_2_final_cost_goal * control_config.STEP_2_COST_LIMIT_SCALE * control_config.AFTERNOON_SCALE
        adgroup_step_3_cost_limit = step_3_final_cost_goal * control_config.STEP_3_COST_LIMIT_SCALE * control_config.AFTERNOON_SCALE
    else:
        adgroup_step_1_cost_limit = step_1_final_cost_goal * control_config.STEP_1_COST_LIMIT_SCALE * control_config.NIGHT_SCALE
        adgroup_step_2_cost_limit = step_2_final_cost_goal * control_config.STEP_2_COST_LIMIT_SCALE * control_config.NIGHT_SCALE
        adgroup_step_3_cost_limit = step_3_final_cost_goal * control_config.STEP_3_COST_LIMIT_SCALE * control_config.NIGHT_SCALE

    print('--- 目标关注成本： ', step_1_final_cost_goal)
    print('--- 目标注册成本： ', step_2_final_cost_goal)
    print('--- 目标表单预约成本： ', step_3_final_cost_goal)

    print('--- 关注成本限制： ', adgroup_step_1_cost_limit)
    print('--- 目标注册成本限制： ', adgroup_step_2_cost_limit)
    print('--- 目标表单预约成本限制： ', adgroup_step_3_cost_limit)

    return {"adgroup_step_1_cost_limit": adgroup_step_1_cost_limit,
            "adgroup_step_2_cost_limit": adgroup_step_2_cost_limit,
            "adgroup_step_3_cost_limit": adgroup_step_3_cost_limit
            }


def get_check_pre_effect_thres(account_status, control_config):
    dapan_reservation_cost_14 = account_status['past2WeeksCreditCost']  # 表单预约成本
    check_cost_thres = dapan_reservation_cost_14 * control_config.CHECK_PRE_THRESHOLD_RATIO
    print(f"前效控停阈值: {check_cost_thres}")
    return check_cost_thres
    # return dapan_step_3_cost * control_config.CHECK_PRE_THRESHOLD_RATIO


def check_pre_effect(data, account_id, adgroup_id, check_cost_thres, pre_effect_limits, data_header):
    # today_cost = data_header.JOIN_CHAR.join([data_header.TODAY_PREFIX, data_header.COST_POSTFIX])
    today_step_1_cost = data_header.JOIN_CHAR.join(
        [data_header.TODAY_PREFIX, data_header.STEP_1_STAT_NAME, data_header.COST_POSTFIX])
    today_cost = data_header.JOIN_CHAR.join([data_header.TODAY_PREFIX, data_header.COST_POSTFIX])
    today_step_2_cost = data_header.JOIN_CHAR.join(
        [data_header.TODAY_PREFIX, data_header.STEP_2_STAT_NAME, data_header.COST_POSTFIX])
    today_step_3_cost = data_header.JOIN_CHAR.join(
        [data_header.TODAY_PREFIX, data_header.STEP_3_STAT_NAME, data_header.COST_POSTFIX])
    today_step_3_uv = data_header.JOIN_CHAR.join(
        [data_header.TODAY_PREFIX, data_header.STEP_3_STAT_NAME, data_header.UV_POSTFIX])
    # today_step_2_uv = data_header.JOIN_CHAR.join(
    #     [data_header.TODAY_PREFIX, data_header.STEP_2_STAT_NAME, data_header.UV_POSTFIX])
    if data[today_cost] > check_cost_thres:
        if data[today_step_3_cost] > pre_effect_limits['adgroup_step_3_cost_limit']:
            if (data[today_step_1_cost] > pre_effect_limits['adgroup_step_1_cost_limit'] or
                    data[today_step_2_cost] > pre_effect_limits['adgroup_step_2_cost_limit']):
                print(account_id, adgroup_id, '前效控停')
                msg = '--- 当天credit成本 %.2f 大于 限制 %.2f 且reg成本 %.2f 大于限制 %.2f 或者apply成本 %.2f 大于限制 %.2f' % (data[today_step_3_cost], pre_effect_limits['adgroup_step_3_cost_limit'], data[today_step_1_cost], pre_effect_limits['adgroup_step_1_cost_limit'], data[today_step_2_cost], pre_effect_limits['adgroup_step_2_cost_limit'])
                return True, msg
    """if data[today_step_3_uv] > 0:  # 当天有表单
        if data[today_step_3_cost] > pre_effect_limits['adgroup_step_3_cost_limit'] * 1.5: # 1.5倍表单成本
            print(account_id, adgroup_id, '前效控停')
            msg = '--- 当天有表单，表单预约成本 %.2f 大于 1.5倍限制 %.2f' % (data[today_step_3_cost], pre_effect_limits['adgroup_step_3_cost_limit'] * 1.5)
            return True, msg
        if data[today_step_2_cost] > pre_effect_limits['adgroup_step_2_cost_limit'] * 1.5: # 3倍注册成本
            print(account_id, adgroup_id, '前效控停')
            msg = '--- 当天有表单，注册成本 %.2f 大于 3 倍限制 %.2f' % (data[today_step_2_cost], pre_effect_limits['adgroup_step_2_cost_limit'] * 1.5)
            return True, msg
    else:  # 当天无表单
        if data[today_cost] > pre_effect_limits['adgroup_step_3_cost_limit']: # 1倍表单成本
            print(account_id, adgroup_id, '前效控停')
            msg = '--- 当天无表单，表单预约成本 %.2f 大于 1倍限制 %.2f' % (data[today_step_3_cost], pre_effect_limits['adgroup_step_3_cost_limit'])
            return True, msg
        if data[today_step_2_cost] > pre_effect_limits['adgroup_step_2_cost_limit']: # 2倍注册成本
            print(account_id, adgroup_id, '前效控停')
            msg = '--- 当天无表单，注册成本 %.2f 大于 2倍限制 %.2f' % (data[today_step_2_cost], pre_effect_limits['adgroup_step_2_cost_limit'])
            return True, msg"""

    return False, ''


def check_suspend(data, account_id, adgroup_id, check_cost_thres, pre_effect_limits, data_header):
    life_long_days = data[data_header.LIFE_LONG_DAYS]
    past_3_day_step_1_cost = data_header.JOIN_CHAR.join(
        [data_header.PAST_3_DAYS_PREFIX, data_header.STEP_1_STAT_NAME, data_header.COST_POSTFIX])
    if life_long_days >= 3 and data.get(past_3_day_step_1_cost) and data[past_3_day_step_1_cost] > pre_effect_limits['adgroup_step_2_cost_limit'] * 0.5: # 1倍注册成本
        print(account_id, adgroup_id, '暂停（30天）')
        msg = '--- 3天内关注成本 %.2f 大于 1倍注册成本 %.2f' % (data[past_3_day_step_1_cost], pre_effect_limits['adgroup_step_2_cost_limit'] * 0.5)
        return True, msg
    return False, ''


def get_post_effect_limits(account_status):
    dapan_d7_cost_rate_14 = account_status['aveD7CostRate']  # 费率
    dapan_d7_reservation_cost_14 = account_status['assessCostD7']  # 进件成本
    # cost_rate_14 = account_status['assessCostRateD14']
    print(f"大盘后效数据 14天费率: {dapan_d7_cost_rate_14}; 14天进件成本: {dapan_d7_reservation_cost_14}")
    return {"dapan_d7_cost_rate_14": dapan_d7_cost_rate_14,
            "dapan_d7_reservation_cost_14": dapan_d7_reservation_cost_14
            }


def check_post_effect(data, adgroup_past_houxiao_data, account_id, adgroup_id, post_effect_limits, control_config,
                      data_header):
    dapan_d7_cost_rate_14, dapan_d7_reservation_cost_14 = post_effect_limits['dapan_d7_cost_rate_14'], \
    post_effect_limits['dapan_d7_reservation_cost_14']
    is_houxiao_stop = 0
    life_long_days = data[data_header.LIFE_LONG_DAYS]
    if (life_long_days >= 3 and data.get('past_3day_reservation_uv') and data['past_3day_reservation_uv'] >= control_config.POST_EFFECT_STEP_3_NUM_LIMIT and
            adgroup_past_houxiao_data['past3DaysD7ShouxinUv'] == 0):
        print(account_id, adgroup_id, life_long_days,
              '--- 3天内表单>=%d, 没有授信' * control_config.POST_EFFECT_STEP_3_NUM_LIMIT)
        is_houxiao_stop = 1
    elif life_long_days >= 7 and data.get('past_7day_reservation_uv'):
        if data['past_7day_reservation_uv'] >= control_config.POST_EFFECT_STEP_3_NUM_LIMIT:
            if data['past_7day_reservation_cost'] > dapan_d7_reservation_cost_14:
                print(account_id, adgroup_id, life_long_days, '7天内表单>=%d, 表单成本 %.2f 大于大盘 %.2f' % (
                control_config.POST_EFFECT_STEP_3_NUM_LIMIT, data['past_7day_reservation_cost'],
                dapan_d7_reservation_cost_14))
                is_houxiao_stop = 1
            else:
                if adgroup_past_houxiao_data['past7DaysD7ShouxinUv'] == 0:
                    print(account_id, adgroup_id, life_long_days,
                        f'--- 7天内表单>={control_config.POST_EFFECT_STEP_3_NUM_LIMIT}, 没有授信')
                    is_houxiao_stop = 1
                else:
                    if adgroup_past_houxiao_data[
                        'past7DaysD7CostRate'] > control_config.COST_RATE_SCALE * dapan_d7_cost_rate_14:
                        print(account_id, adgroup_id, life_long_days,
                            f'--- 7天内表单>={control_config.POST_EFFECT_STEP_3_NUM_LIMIT}, 有授信, 费率 %.2f大于大盘 %.2f' % (
                            adgroup_past_houxiao_data['past7DaysD7CostRate'], dapan_d7_cost_rate_14))
                        is_houxiao_stop = 1
    elif life_long_days >= 14 and data.get('past_14day_reservation_uv') and data['past_14day_reservation_uv'] < control_config.POST_EFFECT_STEP_3_NUM_LIMIT:
        if data['past_14day_reservation_cost'] > dapan_d7_reservation_cost_14:
            print(account_id, adgroup_id, life_long_days,
                  f'--- 14天内表单<{control_config.POST_EFFECT_STEP_3_NUM_LIMIT}, 表单成本 %.2f 大于大盘 %.2f' % \
                  (data['past_14day_reservation_cost'], dapan_d7_reservation_cost_14))
            is_houxiao_stop = 1
        else:
            if adgroup_past_houxiao_data['past2WeeksD7ShouxinUv'] == 0:
                print(account_id, adgroup_id, life_long_days,
                      f'--- 14天内表单<{control_config.POST_EFFECT_STEP_3_NUM_LIMIT}, 没有授信')
                is_houxiao_stop = 1
            else:
                if adgroup_past_houxiao_data[
                    'aveD7CostRate'] > control_config.COST_RATE_SCALE * dapan_d7_cost_rate_14:
                    print(account_id, adgroup_id, life_long_days,
                          f'--- 14天内表单<{control_config.POST_EFFECT_STEP_3_NUM_LIMIT}, 有授信, 14天费率 %.2f 大于大盘的%.2f倍' % \
                          (adgroup_past_houxiao_data['aveD7CostRate'], control_config.COST_RATE_SCALE * 1.0))
                    is_houxiao_stop = 1
    elif life_long_days >= 7 and adgroup_past_houxiao_data[
        'past7DaysD7ReservationUv'] >= control_config.POST_EFFECT_STEP_3_NUM_LIMIT * 2:
        if adgroup_past_houxiao_data['past7DaysD7ShouxinUv'] == 0:
            print(account_id, adgroup_id, life_long_days,
                  '--- 7天内进件>=%d, 没有授信' * control_config.POST_EFFECT_STEP_3_NUM_LIMIT * 2)
            is_houxiao_stop = 1
        else:
            if adgroup_past_houxiao_data[
                'past7DaysD7CostRate'] > control_config.COST_RATE_SCALE * 0.5 * dapan_d7_cost_rate_14:
                print(account_id, adgroup_id, life_long_days,
                      f'--- 7天内进件>={control_config.POST_EFFECT_STEP_3_NUM_LIMIT}, 有授信, 费率 %.2f 大于大盘的%.2f倍' % (
                      adgroup_past_houxiao_data['past7DaysD7CostRate'], control_config.COST_RATE_SCALE * 0.5))
                is_houxiao_stop = 1
    return is_houxiao_stop


def check_if_over_budget(select_account_id, adgroup_status, account_status, control_config, account_config, 
                         path_config, data_header, string_format, scale_ratio=1):
    print(
        f"前效控停配置: {control_config.STEP_1_COST_LIMIT_SCALE}-{control_config.STEP_2_COST_LIMIT_SCALE}-{control_config.STEP_3_COST_LIMIT_SCALE}")

    pre_effect_limits = get_pre_effect_limits(account_status, scale_ratio, control_config)

    print(f"后效控停开启：{bool(control_config.USE_HOUXIAO_STOP)}")
    if control_config.USE_HOUXIAO_STOP:
        print(f"后效控停配置: {control_config.POST_EFFECT_STEP_3_NUM_LIMIT}-{control_config.COST_RATE_SCALE}")
        
    # post_effect_limits = get_post_effect_limits(account_status)

    now = datetime.datetime.now()
    day = str(now).split(' ')[0]
    hour = int(str(now).split(' ')[1].split(':')[0])
    miniute = int(str(now).split(' ')[1].split(':')[1])

    print('--- 保成本 ...')
    print('--- 当前时间：', day, hour, miniute)
    res = {}
    adgroup_report_data = json.load(open(path_config.ADGROUP_REPORT_PATH, encoding='utf8'))
    for account_id in [select_account_id]:
        print('-----------------------------')
        print('account_id: ', account_id)
        adgroup_data = adgroup_report_data[account_id]
        adgroup_ids = adgroup_data.keys()

        select_up_adgroup_ids = []
        for adgroup_id in adgroup_ids:
            if adgroup_id not in adgroup_status:
                continue
            data = adgroup_data[adgroup_id]

            adgroup_past_houxiao_data = adgroup_status[adgroup_id]
            system_status = adgroup_status[adgroup_id]['systemStatus']
            if system_status not in account_config.ACTIVE_STATUS + ['ADGROUP_STATUS_NOT_IN_DELIVERY_TIME']:
                continue

            print('*** %d 当前数据 ...' % int(adgroup_id))
            today_cost = data_header.JOIN_CHAR.join([data_header.TODAY_PREFIX, data_header.COST_POSTFIX])
            today_step_1_cost = data_header.JOIN_CHAR.join(
                [data_header.TODAY_PREFIX, data_header.STEP_1_STAT_NAME, data_header.COST_POSTFIX])
            today_step_1_uv = data_header.JOIN_CHAR.join(
                [data_header.TODAY_PREFIX, data_header.STEP_1_STAT_NAME, data_header.UV_POSTFIX])
            today_step_2_cost = data_header.JOIN_CHAR.join(
                [data_header.TODAY_PREFIX, data_header.STEP_2_STAT_NAME, data_header.COST_POSTFIX])
            today_step_2_uv = data_header.JOIN_CHAR.join(
                [data_header.TODAY_PREFIX, data_header.STEP_2_STAT_NAME, data_header.UV_POSTFIX])
            today_step_3_cost = data_header.JOIN_CHAR.join(
                [data_header.TODAY_PREFIX, data_header.STEP_3_STAT_NAME, data_header.COST_POSTFIX])
            today_step_3_uv = data_header.JOIN_CHAR.join(
                [data_header.TODAY_PREFIX, data_header.STEP_3_STAT_NAME, data_header.UV_POSTFIX])

            msg = '--- 当天关注数 %d, 当天消耗 %.2f 当天关注成本 %.2f 限制: %d' % \
                  (data[today_step_1_uv], data[today_cost], data[today_step_1_cost],
                   pre_effect_limits['adgroup_step_1_cost_limit'])
            print(msg)
            msg = '--- 当天注册数 %d, 当天消耗 %.2f 当天注册成本 %.2f 限制: %d' % \
                  (data[today_step_2_uv], data[today_cost], data[today_step_2_cost],
                   pre_effect_limits['adgroup_step_2_cost_limit'])
            print(msg)
            msg = '--- 当天表单预约数 %d, 当天消耗 %.2f 当天表单预约成本 %.2f 限制: %d' % \
                  (data[today_step_3_uv], data[today_cost], data[today_step_3_cost],
                   pre_effect_limits['adgroup_step_3_cost_limit'])
            print(msg)

            weekends = [4, 5, 6] # Fri, Sat, Sun
            if datetime.datetime.today().weekday() in weekends: 
                tomorrow = str(datetime.datetime.today() + datetime.timedelta(days=7-datetime.datetime.today().weekday())).split(' ')[0]
            else:
                tomorrow = str(datetime.datetime.today() + datetime.timedelta(days=1)).split(' ')[0]

            if system_status not in account_config.ACTIVE_STATUS:
                print('--- 在投放中，检查是否控停 ...')
                # 后效控停
                """if control_config.USE_HOUXIAO_STOP:
                    is_houxiao_stop, new_msg = check_post_effect(data, adgroup_past_houxiao_data, account_id, adgroup_id, post_effect_limits)
                    if is_houxiao_stop:
                        select_up_adgroup_ids.append(
                            [f'{string_format.UPDATE_DELIVERY_DATE}%s' % tomorrow, account_id, int(adgroup_id), new_msg])"""
                
                # 前效控停
                check_cost_thres = get_check_pre_effect_thres(account_status, control_config)
                stop_pre_effect, new_msg = check_pre_effect(data, account_id, adgroup_id, check_cost_thres, pre_effect_limits, data_header)
                if stop_pre_effect:
                    select_up_adgroup_ids.append(
                        [f'{string_format.UPDATE_DELIVERY_DATE}%s' % tomorrow, account_id, int(adgroup_id), new_msg])
                
            # suspend, new_msg = check_suspend(data, account_id, adgroup_id, check_cost_thres, pre_effect_limits, data_header)
            # if suspend:
            #    select_up_adgroup_ids.append(
            #         [f'{string_format.SUSPEND}%s' % account_id, int(adgroup_id), new_msg])
        print('--- select adgroup_ids: ')
        for d in select_up_adgroup_ids:
            print(d)
        res[account_id] = select_up_adgroup_ids
    return res
