import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from collections import defaultdict
import sys
import os 

reg_cost_thres = 220


def load_and_parse_data(csv_file):
    try:
        df = pd.read_csv(csv_file)
        print(f"Loaded {len(df)} rows from {csv_file}")
        print(f"Columns: {list(df.columns)}")
        # filter data up to 7 days ago 
        df = df[df['date'] >= (datetime.now() - timedelta(days=8)).strftime('%Y%m%d')]
        return df
    except Exception as e:
        print(f"Error loading CSV file: {e}")
        sys.exit(1)


def aggregate_by_adgroup(df):
    df['cost'] = df['cost'] * 0.01
    grouped = df.groupby(['account_id', 'adgroup_id', 'date', 'hour'])    
    aggregated = grouped.agg({
        'dynamic_creative_id': 'nunique',  # Count of unique dynamic_creative_ids
        'cost': 'sum',
        # 'acquisition_cost': 'sum',
        'view_count': 'sum',
        'valid_click_count': 'sum',
        'reg_pv': 'sum',
        'apply_pv': 'sum',
        'credit_pv': 'sum'
    }).reset_index()
    
    # Rename col name
    aggregated.rename(columns={'dynamic_creative_id': 'unique_dynamic_creative_count'}, inplace=True)
    
    return aggregated


def calculate_daily_totals(df):
    daily_totals = df.groupby(['account_id', 'adgroup_id', 'date']).agg({
        'cost': 'sum',
        # 'acquisition_cost': 'sum',
        'view_count': 'sum',
        'valid_click_count': 'sum',
        'reg_pv': 'sum',
        'apply_pv': 'sum',
        'credit_pv': 'sum'
    }).reset_index()
    
    # avoid zero division 
    daily_totals['reg_cost'] = np.where(daily_totals['reg_pv'] == 0, 0, daily_totals['cost'] / daily_totals['reg_pv'])
    daily_totals['apply_cost'] = np.where(daily_totals['apply_pv'] == 0, 0, daily_totals['cost'] / daily_totals['apply_pv'])
    daily_totals['credit_cost'] = np.where(daily_totals['credit_pv'] == 0, 0, daily_totals['cost'] / daily_totals['credit_pv'])
    #daily_totals['reg_cost'] = daily_totals['cost'].div(daily_totals['reg_pv'], 0)
    #daily_totals['apply_cost'] = daily_totals['cost'].div(daily_totals['apply_pv'], 0)
    #daily_totals['credit_cost'] = daily_totals['cost'].div(daily_totals['credit_pv'], 0)
    
    # metric_columns = ['cost', 'acquisition_cost', 'view_count', 'valid_click_count', 'reg_pv', 'apply_pv', 'credit_pv', 'reg_cost', 'apply_cost', 'credit_cost']
    metric_columns = ['cost', 'view_count', 'valid_click_count', 'reg_pv', 'apply_pv', 'credit_pv', 'reg_cost', 'apply_cost', 'credit_cost']

    rename_dict = {col: f'cur_day_{col}' for col in metric_columns}
    daily_totals = daily_totals.rename(columns=rename_dict)

    return daily_totals


def calculate_historical_totals(df, days_back):
    df['date'] = pd.to_datetime(df['date'])

    historical_data = []

    grouped_data = df.groupby(['account_id', 'adgroup_id'])

    for (account_id, adgroup_id), group_data in grouped_data:
        dates_for_group = sorted(group_data['date'].unique())

        for current_date in dates_for_group:
            start_date = current_date - timedelta(days=days_back)
            end_date = current_date - timedelta(days=1)  # Exclude current day
            historical_period_data = group_data[
                (group_data['date'] >= start_date) &
                (group_data['date'] <= end_date)
            ]

            if len(historical_period_data) > 0:
                totals = historical_period_data.agg({
                    'cost': 'sum',
                    # 'acquisition_cost': 'sum',
                    'view_count': 'sum',
                    'valid_click_count': 'sum',
                    'reg_pv': 'sum',
                    'apply_pv': 'sum',
                    'credit_pv': 'sum'
                })

            else:
                # If no historical data, set all values to 0
                totals = pd.Series({
                    'cost': 0,
                    # 'acquisition_cost': 0,
                    'view_count': 0,
                    'valid_click_count': 0,
                    'reg_pv': 0,
                    'apply_pv': 0,
                    'credit_pv': 0,
                    
                })
            # calculate cost per pv
            if totals['reg_pv'] != 0:
                totals['reg_cost'] = totals['cost'] / totals['reg_pv']
            else:
                totals['reg_cost'] = 0
            if totals['apply_pv'] != 0:
                totals['apply_cost'] = totals['cost'] / totals['apply_pv']
            else:
                totals['apply_cost'] = 0
            if totals['credit_pv'] != 0:
                totals['credit_cost'] = totals['cost'] / totals['credit_pv']
            else:
                totals['credit_cost'] = 0
            
            historical_data.append({
                'account_id': account_id,
                'adgroup_id': adgroup_id,
                'date': current_date,
                **{f'past_{days_back}day_{col}': totals[col] for col in totals.index}
            })

    return pd.DataFrame(historical_data)


# Feature Engineering
def daily_feature_engineering(data):
    data['date'] = pd.to_datetime(data['date'])
    data['day_of_week'] = data['date'].dt.dayofweek

    return data


def hourly_feature_engineering(hourly_data):
    """
    Ultra-efficient hourly feature engineering using pandas time-based rolling.
    """
    result_data = hourly_data.copy()
    # result_data['is_auto_acquisition'] = (result_data['acquisition_cost'] * 0.01 > 100).astype(int)
    if 'acquisition_cost' in result_data.columns:
        result_data.drop(['acquisition_cost'], axis=1, inplace=True)
    # Create cyclic features
    result_data['hour_sin'] = np.sin(2 * np.pi * result_data['hour'] / 24)
    result_data['hour_cos'] = np.cos(2 * np.pi * result_data['hour'] / 24)

    result_data['date'] = pd.to_datetime(result_data['date'])
    result_data['ds'] = result_data['date'] + pd.to_timedelta(result_data['hour'], unit='h')
    result_data = result_data.sort_values(['account_id', 'adgroup_id', 'ds']).reset_index(drop=True)
    
    metrics = ['cost', 'reg_pv', 'apply_pv', 'credit_pv']
    hour_windows = [1, 2, 4, 6, 8, 12, 16]

    """
    def calculate_group_features(group):
        group = group.sort_values('ds')
        # group['ds'] = pd.to_datetime(group['ds'])
        for window in hour_windows:
            for metric in metrics:
                col_name = f'cum_{window}h_up_to_cur_h_{metric}'
                group[col_name] = 0

                for i, current_time in enumerate(group['ds']):
                    time_window_start = current_time - pd.Timedelta(hours=window)
                    group[col_name].iloc[i] = group.loc[(group['ds'] >= time_window_start) & (group['ds'] < current_time), metric].sum()

        return group
    """

    def calculate_group_features(group):
        group = group.set_index('ds').sort_index()

        for window in hour_windows:
            for metric in metrics:
                # calculate cumulative sum
                col_name = f'cum_{window}h_up_to_cur_h_{metric}'
                group[col_name] = group[metric].rolling(
                    window=f'{window}h',
                    min_periods=1,
                    closed='left'
                ).sum().fillna(0)
                # calculate mean
                col_name = f'mean_{window}h_up_to_cur_h_{metric}'
                group[col_name] = group[metric].rolling(
                    window=f'{window}h',
                    min_periods=1,
                    closed='left'
                ).mean().fillna(0)

        return group.reset_index()

    # Process each adgroup separately with vectorized operations
    result_list = []
    for (account_id, adgroup_id), group in result_data.groupby(['account_id', 'adgroup_id']):
        processed_group = calculate_group_features(group)
        result_list.append(processed_group)

    # Concatenate all results
    result_data = pd.concat(result_list, ignore_index=True)
    # print(f"Total rows after cumulative sum: {result_data.shape[0]}")

    # Create features for metrics of previous hour
    for metric in metrics:
        sub_data = result_data.sort_values(by=['account_id', 'adgroup_id', 'ds'])
        merged = pd.merge(
                sub_data,
                sub_data,
                left_on=['account_id', 'adgroup_id', 'ds'],
                right_on=['account_id', 'adgroup_id', sub_data['ds'] - pd.Timedelta(hours=1)],
                suffixes=('', '_prev'),
                how='left'
            )
        merged = merged.fillna(0)
        result_data[f'prev_h_{metric}'] = merged[f"{metric}_prev"]
    
    # print(f"Total rows after adding previous hour features: {result_data.shape[0]}")

    # Create squared, cubed features for metrics of previous hour
    for metric in [f"prev_h_{m}" for m in metrics]:
        result_data[f'prev_h_{metric}_squared'] = result_data[metric] ** 2
        result_data[f'prev_h_{metric}_cubed'] = result_data[metric] ** 3
    
    return result_data


def init_model(model_path):
    import xgboost as xgb
    model = xgb.XGBClassifier()
    model.load_model(model_path)
    return model


def prepare_data(hourly_data, daily_data, reg_cost_thres):
    hourly_data = hourly_data.copy()
    daily_data = daily_data.copy()
    # drop low cost entries
    daily_data = daily_data[daily_data['cur_day_cost'] > reg_cost_thres]
    cur_day_cols = [col for col in daily_data.columns if 'cur_day_' in col]
    daily_data.drop(cur_day_cols, axis=1, inplace=True)
    hourly_data = hourly_data.sort_values(['account_id', 'adgroup_id', 'ds']).reset_index(drop=True)
    hourly_data.drop(['cost', 'view_count', 'valid_click_count', 'reg_pv', 'apply_pv', 'credit_pv', 'unique_dynamic_creative_count'], axis=1, inplace=True)
    merged_data = hourly_data.merge(daily_data, on=['account_id', 'adgroup_id', 'date'], how='left')
    return merged_data


def get_predict_data(model, data_file_dir):
    # iterate through the given directory, each csv is for one account id
    print("Loading data...")
    for file in os.listdir(data_file_dir):
        if file.endswith('.csv'):
            data_file_path = os.path.join(data_file_dir, file)
            print(f"Loading {data_file_path}...")
            df = load_and_parse_data(data_file_path)
            # Convert date to datetime
            df['date'] = pd.to_datetime(df['date'])
            
    df = load_and_parse_data(data_file_path)
    
    # Convert date to datetime
    df['date'] = pd.to_datetime(df['date'])
    
    # Aggregate by adgroup
    print("Aggregating data by adgroup...")
    hourly_data = aggregate_by_adgroup(df)


    print("Calculating daily totals...")
    daily_totals = calculate_daily_totals(df)
    
    print("Calculating historical totals...")
    past_1day = calculate_historical_totals(hourly_data, 1)
    past_3day = calculate_historical_totals(hourly_data, 3)
    past_7day = calculate_historical_totals(hourly_data, 7)
    
    # Merge all data together
    print("Merging all data...")
    daily_data = daily_totals.copy()
    
    daily_data = daily_data.merge(past_1day, on=['account_id', 'adgroup_id', 'date'], how='left')
    daily_data = daily_data.merge(past_3day, on=['account_id', 'adgroup_id', 'date'], how='left')
    daily_data = daily_data.merge(past_7day, on=['account_id', 'adgroup_id', 'date'], how='left')

    print(f"Columns after merge: {list(daily_data.columns)}")
    
    daily_data = daily_data.fillna(0)
    
    # Sort by account_id, adgroup_id, date, hour (if hour column exists)
    sort_columns = ['account_id', 'adgroup_id', 'date']
    if 'hour' in daily_data.columns:
        sort_columns.append('hour')
    daily_data = daily_data.sort_values(sort_columns)

    hourly_data = hourly_feature_engineering(hourly_data)
    daily_data = daily_feature_engineering(daily_data)

    features = prepare_data(hourly_data, daily_data, reg_cost_thres)
    # only keep the newest entry for each account id, adgroup id 
    grouped = features.groupby(['account_id', 'adgroup_id', 'date'])
    for (account_id, adgroup_id, date), group in grouped:
        group = group.sort_values('hour')
    features = grouped.apply(lambda x: x.iloc[-1]).reset_index(drop=True)
    predict_features = features.drop(['account_id', 'adgroup_id', 'date', 'hour', 'ds'], axis=1)
    predict_score = model.predict_proba(predict_features)[:, 1]
    predict_df = features[['account_id', 'adgroup_id', 'date', 'hour', 'ds']].copy()
    predict_df['predict_score'] = predict_score
    return predict_df


def check_if_over_budget_by_model(select_account_id, data_path, model_path, string_format, score_thres=0.8):
    print(f"--- Checking if over budget by model...")
    res = {}
    model = init_model(model_path)
    predict_df = get_predict_data(model, data_path)
    weekends = [4, 5, 6] # Fri, Sat, Sun
    if datetime.datetime.today().weekday() in weekends: 
        tomorrow = str(datetime.datetime.today() + datetime.timedelta(days=7-datetime.datetime.today().weekday())).split(' ')[0]
    else:
        tomorrow = str(datetime.datetime.today() + datetime.timedelta(days=1)).split(' ')[0]
    for account_id in select_account_id:
        print('-----------------------------')
        print('account_id: ', account_id)
        select_adgroup_ids = []
        account_df = predict_df[predict_df['account_id'] == account_id]
        account_df = account_df[account_df['predict_score'] > score_thres]
        if len(account_df) == 0:
            continue
        for idx, row in account_df.iterrows():
            adgroup_id = row['adgroup_id']
            print(account_id, adgroup_id, '模型控停')
            msg = (f"--- 模型控停 分数: {row['predict_score']} > {score_thres}")
            select_adgroup_ids.append([f'{string_format.UPDATE_DELIVERY_DATE}%s' % tomorrow, account_id, int(adgroup_id), msg])
        for d in select_adgroup_ids:
            print(d)
        res[account_id] = select_adgroup_ids
    return res



