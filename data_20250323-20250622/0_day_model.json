{"learner": {"attributes": {"scikit_learn": "{\"_estimator_type\": \"classifier\"}"}, "feature_names": ["hour_sin", "hour_cos", "cum_1h_up_to_cur_h_cost", "mean_1h_up_to_cur_h_cost", "cum_1h_up_to_cur_h_reg_pv", "mean_1h_up_to_cur_h_reg_pv", "cum_1h_up_to_cur_h_apply_pv", "mean_1h_up_to_cur_h_apply_pv", "cum_1h_up_to_cur_h_credit_pv", "mean_1h_up_to_cur_h_credit_pv", "cum_2h_up_to_cur_h_cost", "mean_2h_up_to_cur_h_cost", "cum_2h_up_to_cur_h_reg_pv", "mean_2h_up_to_cur_h_reg_pv", "cum_2h_up_to_cur_h_apply_pv", "mean_2h_up_to_cur_h_apply_pv", "cum_2h_up_to_cur_h_credit_pv", "mean_2h_up_to_cur_h_credit_pv", "cum_4h_up_to_cur_h_cost", "mean_4h_up_to_cur_h_cost", "cum_4h_up_to_cur_h_reg_pv", "mean_4h_up_to_cur_h_reg_pv", "cum_4h_up_to_cur_h_apply_pv", "mean_4h_up_to_cur_h_apply_pv", "cum_4h_up_to_cur_h_credit_pv", "mean_4h_up_to_cur_h_credit_pv", "cum_6h_up_to_cur_h_cost", "mean_6h_up_to_cur_h_cost", "cum_6h_up_to_cur_h_reg_pv", "mean_6h_up_to_cur_h_reg_pv", "cum_6h_up_to_cur_h_apply_pv", "mean_6h_up_to_cur_h_apply_pv", "cum_6h_up_to_cur_h_credit_pv", "mean_6h_up_to_cur_h_credit_pv", "cum_8h_up_to_cur_h_cost", "mean_8h_up_to_cur_h_cost", "cum_8h_up_to_cur_h_reg_pv", "mean_8h_up_to_cur_h_reg_pv", "cum_8h_up_to_cur_h_apply_pv", "mean_8h_up_to_cur_h_apply_pv", "cum_8h_up_to_cur_h_credit_pv", "mean_8h_up_to_cur_h_credit_pv", "cum_12h_up_to_cur_h_cost", "mean_12h_up_to_cur_h_cost", "cum_12h_up_to_cur_h_reg_pv", "mean_12h_up_to_cur_h_reg_pv", "cum_12h_up_to_cur_h_apply_pv", "mean_12h_up_to_cur_h_apply_pv", "cum_12h_up_to_cur_h_credit_pv", "mean_12h_up_to_cur_h_credit_pv", "cum_16h_up_to_cur_h_cost", "mean_16h_up_to_cur_h_cost", "cum_16h_up_to_cur_h_reg_pv", "mean_16h_up_to_cur_h_reg_pv", "cum_16h_up_to_cur_h_apply_pv", "mean_16h_up_to_cur_h_apply_pv", "cum_16h_up_to_cur_h_credit_pv", "mean_16h_up_to_cur_h_credit_pv", "prev_h_cost", "prev_h_reg_pv", "prev_h_apply_pv", "prev_h_credit_pv", "prev_h_prev_h_cost_squared", "prev_h_prev_h_cost_cubed", "prev_h_prev_h_reg_pv_squared", "prev_h_prev_h_reg_pv_cubed", "prev_h_prev_h_apply_pv_squared", "prev_h_prev_h_apply_pv_cubed", "prev_h_prev_h_credit_pv_squared", "prev_h_prev_h_credit_pv_cubed", "past_1day_cost", "past_1day_view_count", "past_1day_valid_click_count", "past_1day_reg_pv", "past_1day_apply_pv", "past_1day_credit_pv", "past_1day_reg_cost", "past_1day_apply_cost", "past_1day_credit_cost", "past_3day_cost", "past_3day_view_count", "past_3day_valid_click_count", "past_3day_reg_pv", "past_3day_apply_pv", "past_3day_credit_pv", "past_3day_reg_cost", "past_3day_apply_cost", "past_3day_credit_cost", "past_7day_cost", "past_7day_view_count", "past_7day_valid_click_count", "past_7day_reg_pv", "past_7day_apply_pv", "past_7day_credit_pv", "past_7day_reg_cost", "past_7day_apply_cost", "past_7day_credit_cost", "day_of_week"], "feature_types": ["float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float"], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "100"}, "iteration_indptr": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100], "tree_info": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "trees": [{"base_weights": [1.03316395e-07, -0.8088413, 3.6023273, -0.94174075, 1.5611353, 5.3801455, 2.0795283, -0.0982468, 0.048974976, 0.092570685, 0.29198262, 0.12519877, 0.7149912, 0.02979041, 0.29803252], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 0, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [18331.26, 1619.7092, 3111.9092, 284.14795, 233.1521, 3874.8838, 998.94775, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2273.4, 320.73, 0.13333334, 1869.16, 768.87, 1.0, 438.76874, -0.0982468, 0.048974976, 0.092570685, 0.29198262, 0.12519877, 0.7149912, 0.02979041, 0.29803252], "split_indices": [50, 58, 57, 50, 58, 56, 51, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [6289.379, 5136.778, 1152.6008, 4864.5923, 272.1856, 530.6044, 621.9964, 4730.324, 134.2683, 186.59892, 85.5867, 159.72867, 370.8757, 209.32253, 412.67386], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.019844618, -0.82130617, 2.2640564, -0.9808872, 0.81175137, 2.8173864, 1.0925614, -0.101931214, -0.052764513, 0.03339436, 0.19517563, 0.21974948, 0.4390974, 0.04659128, 0.30807164], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 1, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [11638.08, 1227.313, 1067.5127, 74.13965, 227.85864, 1083.9424, 660.703, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2031.34, 225.26, 0.27272728, 129.72, 634.57, 392.4325, 2796268.0, -0.101931214, -0.052764513, 0.03339436, 0.19517563, 0.21974948, 0.4390974, 0.04659128, 0.30807164], "split_indices": [50, 58, 49, 58, 58, 43, 80, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [6355.997, 4705.4214, 1650.5758, 4286.9077, 418.5136, 1120.111, 530.4648, 3950.7239, 336.18372, 295.8201, 122.69351, 805.2794, 314.83154, 404.2752, 126.18955], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.027464489, -0.8194073, 1.6735238, -0.97602564, 0.62196136, 2.2909749, 1.1397548, -0.100963406, -0.05896324, 0.0036785018, 0.13096577, 0.045080613, 0.31898215, -0.018452253, 0.16906755], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 2, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [8528.733, 975.81177, 660.2612, 50.010498, 170.58252, 1539.3789, 788.78467, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1869.16, 225.26, 2.0, 129.72, 457.64, 1.0, 4903.26, -0.100963406, -0.05896324, 0.0036785018, 0.13096577, 0.045080613, 0.31898215, -0.018452253, 0.16906755], "split_indices": [50, 58, 48, 58, 58, 56, 50, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [6329.2305, 4318.9434, 2010.287, 3895.936, 423.00757, 930.5532, 1079.7338, 3582.7007, 313.23526, 229.10794, 193.89961, 305.8669, 624.6863, 317.4139, 762.3199], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.031965163, -0.79750043, 1.3540806, -0.9521777, 0.49165994, 1.6028652, 0.42820445, -0.09891039, -0.053915698, 0.0010781199, 0.12387045, 0.048204612, 0.19429061, -0.022578103, 0.14415042], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 3, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [6640.576, 804.3894, 512.23267, 54.372314, 155.38013, 667.85205, 313.75815, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1664.16, 225.26, 0.33333334, 129.72, 4162.42, 2559.75, 1916572.0, -0.09891039, -0.053915698, 0.0010781199, 0.12387045, 0.048204612, 0.19429061, -0.022578103, 0.14415042], "split_indices": [42, 58, 57, 58, 79, 50, 80, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [6256.378, 4030.6243, 2225.754, 3599.0598, 431.5644, 1753.7788, 471.975, 3302.412, 296.64764, 263.17252, 168.39189, 408.78552, 1344.9933, 287.3172, 184.65784], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.03420424, -0.78972405, 1.0975593, -0.9492174, 0.39880154, 0.6193521, 1.4160225, -0.10123547, -0.07232013, 0.00049355405, 0.10600429, 0.08609111, -0.07408356, 0.062815554, 0.16275717], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 4, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [5275.007, 701.6531, 375.22705, 45.81714, 114.17477, 325.5877, 245.92627, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1724.09, 225.26, 2119.0483, 65.5, 564.59, 5.0, 210.71083, -0.10123547, -0.07232013, 0.00049355405, 0.10600429, 0.08609111, -0.07408356, 0.062815554, 0.16275717], "split_indices": [50, 58, 96, 58, 58, 40, 43, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [6167.0815, 3698.4485, 2468.6333, 3261.0374, 437.41116, 987.99554, 1480.6378, 2546.2627, 714.7746, 274.75928, 162.6519, 839.30786, 148.68764, 314.25092, 1166.3868], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.035246108, -0.7764589, 0.9150611, -0.91507787, 0.40945855, 0.07632067, 1.0782393, -0.09628107, -0.044729073, 0.02571185, 0.18970352, -0.05411012, 0.19055061, 0.19944195, 0.059733152], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 5, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [4276.6143, 561.1045, 364.0437, 67.76831, 80.56515, 491.60953, 980.0698, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1474.84, 267.09, 1.0, 139.39, 1642.43, 225.26, 2.0, -0.09628107, -0.044729073, 0.02571185, 0.18970352, -0.05411012, 0.19055061, 0.19944195, 0.059733152], "split_indices": [42, 58, 56, 58, 58, 58, 56, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [6069.451, 3409.9607, 2659.4902, 3053.2969, 356.6639, 433.37888, 2226.1113, 2769.627, 283.6698, 324.5955, 32.068405, 324.53857, 108.84031, 765.1922, 1460.9191], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.035762224, -0.7426931, 0.8031326, -0.91675305, 0.3027836, 1.0420203, 0.37907127, -0.09900352, -0.06656288, 0.012118973, 0.14201574, 0.008947978, 0.13332467, -0.058864888, 0.08010622], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 6, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [3531.1904, 588.29956, 275.45886, 50.32422, 93.56061, 482.91333, 401.8168, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1724.09, 225.26, 3.0, 65.5, 78998.0, 1.0, 580.2733, -0.09900352, -0.06656288, 0.012118973, 0.14201574, 0.008947978, 0.13332467, -0.058864888, 0.08010622], "split_indices": [50, 58, 48, 58, 71, 56, 43, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5952.3765, 3230.3215, 2722.055, 2769.375, 460.94635, 1740.2549, 981.80005, 2141.5063, 627.8688, 397.46017, 63.486168, 407.7844, 1332.4705, 298.00732, 683.7927], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.03570659, -0.75232726, 0.6672129, -0.91307116, 0.18990675, 0.036733717, 0.8313042, -0.09790909, -0.06889578, -0.023374757, 0.0625389, -0.08771511, 0.058982607, 0.12146876, 0.043215577], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 7, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [2937.7256, 437.5586, 304.56128, 35.8291, 77.945175, 308.37567, 356.9613, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1480.04, 225.26, 31.83, 65.5, 457.64, 2559.75, 2.0, -0.09790909, -0.06889578, -0.023374757, 0.0625389, -0.08771511, 0.058982607, 0.12146876, 0.043215577], "split_indices": [50, 58, 58, 58, 58, 50, 40, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5829.9873, 2886.8237, 2943.1636, 2466.1353, 420.68857, 608.0623, 2335.1013, 1902.3645, 563.77075, 213.47496, 207.21362, 229.03598, 379.02637, 1190.0302, 1145.0712], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.03528814, -0.7190264, 0.59276104, -0.9068177, 0.079552434, 0.32473528, 0.93741906, -0.095668145, -0.06165555, -0.018895356, 0.08790242, 0.06352254, -0.0073108072, 0.1199433, 0.0102277], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 8, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [2456.9387, 410.8723, 275.18604, 31.510132, 112.31493, 207.46587, 285.23523, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [123.20417, 170.35, 16683.43, 1988.65, 2152.05, 2.0, 6.0, -0.095668145, -0.06165555, -0.018895356, 0.08790242, 0.06352254, -0.0073108072, 0.1199433, 0.0102277], "split_indices": [43, 58, 79, 70, 70, 48, 40, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5719.519, 2738.2854, 2981.2336, 2216.8438, 521.4415, 1678.1503, 1303.0834, 1889.8868, 326.95694, 390.9148, 130.52675, 942.2167, 735.9336, 991.5595, 311.52393], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.034492295, -0.5749579, 0.6482456, -0.8716655, 0.18945111, 0.8659453, 0.11965404, -0.095552705, -0.06096365, -0.031059062, 0.05218337, 0.06978155, 0.19902922, -0.02911242, 0.07764151], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 9, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [2072.4937, 711.17566, 285.47302, 49.04602, 145.98927, 330.85864, 195.84212, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2411.57, 139.39, 0.26666668, 47.71, 700.96, 529.4308, 1916572.0, -0.095552705, -0.06096365, -0.031059062, 0.05218337, 0.06978155, 0.19902922, -0.02911242, 0.07764151], "split_indices": [50, 58, 57, 58, 50, 51, 80, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5614.5596, 3133.8857, 2480.6736, 2257.4932, 876.3927, 1756.5873, 724.08636, 1708.1566, 549.33655, 349.9651, 526.4276, 1529.4941, 227.09306, 445.80783, 278.27853], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.03367883, -0.689779, 0.45530146, -0.89030814, 0.028422339, -0.1448262, 0.58238775, -0.08970052, 0.1377646, -0.03024711, 0.049656566, -0.07702943, 0.07597529, 0.11838087, 0.02521177], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 10, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1766.1244, 338.58484, 240.65466, 29.148071, 79.78211, 312.91858, 516.7866, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1427.02, 170.35, 1.0, 6539674.0, 457.64, 126.66, 2.0, -0.08970052, 0.1377646, -0.03024711, 0.049656566, -0.07702943, 0.07597529, 0.11838087, 0.02521177], "split_indices": [50, 58, 56, 89, 58, 58, 56, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5503.0522, 2349.7766, 3153.2756, 1836.7091, 513.0675, 551.03503, 2602.2407, 1831.9042, 4.8049774, 300.8057, 212.26184, 325.86514, 225.1699, 921.53796, 1680.7026], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.03266503, -0.66128486, 0.41455337, -0.8324187, -0.07267597, 0.13971701, 0.66236454, -0.08791149, 0.02514552, -0.029910618, 0.060282927, -0.050183404, 0.03162123, 0.07958886, -0.042039372], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 11, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1514.8998, 225.57501, 214.35364, 88.05078, 77.523026, 169.34616, 239.67145, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [965.73, 149.72, 10965.13, 2627.44, 2152.05, 1.0, 2.0, -0.08791149, 0.02514552, -0.029910618, 0.060282927, -0.050183404, 0.03162123, 0.07958886, -0.042039372], "split_indices": [34, 58, 79, 50, 70, 56, 61, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5386.6274, 2238.9841, 3147.643, 1734.3269, 504.65735, 1493.1835, 1654.4596, 1662.8746, 71.45217, 378.38083, 126.27651, 321.75986, 1171.4236, 1473.0681, 181.39153], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.031475425, -0.6355913, 0.3677291, -0.81300426, -0.06640551, 0.43430352, -0.4819893, -0.08618533, 0.022487467, -0.02531225, 0.06381809, 0.025202453, 0.087548815, -0.08760112, -0.0012628884], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 12, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1277.1432, 212.71832, 180.59143, 81.63074, 66.17542, 237.62012, 42.922676, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [965.73, 149.72, 2.0, 2627.44, 768.87, 29091.95, 2413.34, -0.08618533, 0.022487467, -0.02531225, 0.06381809, 0.025202453, 0.087548815, -0.08760112, -0.0012628884], "split_indices": [34, 58, 61, 50, 58, 79, 96, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5293.7363, 2106.0542, 3187.6821, 1605.2697, 500.7845, 2956.5305, 231.15172, 1533.2738, 71.995834, 396.38287, 104.40161, 2093.2559, 863.2745, 125.18999, 105.96173], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.030096462, -0.5770193, 0.35983622, -0.75476724, 0.5191545, 0.76682806, 0.0923204, -0.07881307, 0.09018987, -0.01184402, 0.09552654, 0.025202999, 0.13474181, -0.06782329, 0.044544745], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 13, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1111.6981, 423.12677, 331.2637, 103.92078, 84.45374, 360.36456, 500.25922, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 309.19, 2.0, 1.0, 4048.96, 2492.4, 4699.87, -0.07881307, 0.09018987, -0.01184402, 0.09552654, 0.025202999, 0.13474181, -0.06782329, 0.044544745], "split_indices": [56, 58, 56, 61, 79, 42, 50, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5210.828, 2168.6216, 3042.2065, 1866.3058, 302.31586, 1205.8262, 1836.3802, 1829.9763, 36.32948, 123.08859, 179.22726, 639.8858, 565.9404, 576.8103, 1259.57], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.02886255, -0.62391114, 0.28716296, -0.85466397, -0.07749802, -0.21164373, 0.39599198, -0.09255318, -0.058198284, -0.025423527, 0.06145222, -0.08954286, 0.021011887, 0.054201495, -0.013408228], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 14, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [959.51245, 223.0899, 180.9927, 23.50586, 64.56259, 172.51215, 211.85266, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1142.93, 129.72, 27.16, 4162.42, 24.0, 2559.75, 1.0, -0.09255318, -0.058198284, -0.025423527, 0.06145222, -0.08954286, 0.021011887, 0.054201495, -0.013408228], "split_indices": [42, 58, 58, 79, 92, 50, 61, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5100.465, 1768.8673, 3331.5977, 1243.266, 525.60126, 596.52795, 2735.0696, 984.9029, 258.36316, 419.17853, 106.42273, 227.14493, 369.38303, 2144.3337, 590.736], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.027649647, -0.5319141, 0.2979593, -0.818566, 0.11198626, 0.68006206, 0.051865827, -0.08348613, 0.049970776, -0.017433451, 0.0968535, 0.023097409, 0.11982765, -0.06546872, 0.035866674], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 15, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [825.2329, 364.07007, 287.14624, 29.731812, 149.36221, 278.1377, 403.15167, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 132.63, 2.0, 1.0, 14364.03, 2492.4, 4699.87, -0.08348613, 0.049970776, -0.017433451, 0.0968535, 0.023097409, 0.11982765, -0.06546872, 0.035866674], "split_indices": [56, 58, 56, 61, 79, 42, 50, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5024.028, 1970.9779, 3053.05, 1363.6404, 607.33746, 1195.3298, 1857.7202, 1347.3524, 16.28802, 455.7794, 151.55809, 641.1505, 554.1794, 562.0962, 1295.624], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.026376035, -0.5761176, 0.23630364, -0.82810616, -0.07833026, -0.031153368, 0.41394794, -0.09082708, -0.05305254, -0.028148044, 0.051084828, 0.010399366, -0.0891861, 0.0298117, 0.091427706], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 16, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [710.8459, 199.57928, 158.27696, 24.723267, 64.30038, 154.84439, 115.73001, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1142.93, 120.71, 2119.0483, 4162.42, 634.57, 5.0, 747781.0, -0.09082708, -0.05305254, -0.028148044, 0.051084828, 0.010399366, -0.0891861, 0.0298117, 0.091427706], "split_indices": [42, 58, 96, 79, 58, 40, 71, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4920.611, 1590.5924, 3330.0186, 1055.5773, 535.01514, 1329.3722, 2000.6464, 829.94745, 225.62984, 398.22757, 136.78758, 1149.7235, 179.64868, 1625.844, 374.80234], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.02505548, -0.48911253, 0.25048977, -0.65894717, 0.548174, 0.3938882, -0.35241398, -0.08610869, -0.033449873, 0.023834018, 0.13356096, 0.07261529, 0.013562572, -0.061638128, 0.01640393], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 17, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [620.80164, 319.0038, 263.5066, 101.677, 61.90148, 211.12021, 79.96906, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 64.2, 215.22166, 2.0, 4934442.0, -0.08610869, -0.033449873, 0.023834018, 0.13356096, 0.07261529, 0.013562572, -0.061638128, 0.01640393], "split_indices": [56, 60, 61, 58, 27, 56, 89, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4853.034, 1807.7512, 3045.283, 1553.725, 254.02621, 2460.4263, 584.8566, 956.28235, 597.4426, 183.23216, 70.79405, 1075.256, 1385.1703, 386.9083, 197.94835], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.02382355, -0.58270544, 0.18219516, -0.7049761, 0.23467794, -0.30190462, 0.28708988, -0.082698785, -0.023093719, 0.0029693858, 0.09579181, -0.09224756, 0.008787299, 0.038266454, -0.03149399], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 18, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [551.02954, 128.98874, 177.65387, 64.67499, 24.948627, 150.83365, 165.52068, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [995.18, 457.64, 27.16, 4162.42, 1472.28, 2480.09, 4.0, -0.082698785, -0.023093719, 0.0029693858, 0.09579181, -0.09224756, 0.008787299, 0.038266454, -0.03149399], "split_indices": [50, 58, 58, 79, 58, 50, 24, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4783.808, 1287.9807, 3495.8274, 1120.5044, 167.47633, 622.24164, 2873.5857, 890.5447, 229.95972, 131.3029, 36.173424, 239.52441, 382.71722, 2480.2073, 393.37842], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.022668531, -0.5569122, 0.16320327, -0.8430916, -0.19836468, -0.314361, 0.25047106, -0.03272097, -0.08951697, -0.042257313, 0.032544665, -0.09512871, 0.0070122606, 0.031489644, -0.05054113], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 19, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [469.0104, 124.92279, 146.11621, 17.913147, 63.842766, 132.75594, 144.46492, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [995.18, 71.8, 21.3, 78.75, 4162.42, 2480.09, 2.0, -0.03272097, -0.08951697, -0.042257313, 0.032544665, -0.09512871, 0.0070122606, 0.031489644, -0.05054113], "split_indices": [50, 58, 58, 50, 79, 50, 61, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4721.2114, 1218.0948, 3503.1165, 676.6591, 541.4358, 540.83716, 2962.2793, 62.712826, 613.9463, 379.28333, 162.15245, 203.02942, 337.8077, 2730.1653, 232.11403], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.021540744, -0.43000707, 0.19436093, -0.74583185, 0.068923496, 0.528845, -0.014031837, -0.0849222, -0.048436433, -0.03818461, 0.04534329, 0.015167102, 0.099819906, -0.063334614, 0.027022103], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 20, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [411.68463, 254.41183, 212.88795, 26.313293, 108.78538, 207.393, 331.60178, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 120.71, 2.0, -0.70710677, 2.0, 2492.4, 4903.26, -0.0849222, -0.048436433, -0.03818461, 0.04534329, 0.015167102, 0.099819906, -0.063334614, 0.027022103], "split_indices": [56, 58, 56, 0, 84, 42, 50, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4666.284, 1613.2224, 3053.0618, 987.5837, 625.63873, 1171.3807, 1881.681, 706.1199, 281.4638, 288.00754, 337.6312, 650.2009, 521.1798, 591.571, 1290.11], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.020395707, -0.5094473, 0.1357695, -0.8219201, -0.17709535, -0.09225922, 0.2879063, -0.031360265, -0.08891636, -0.032679003, 0.04667371, 0.0025226267, -0.08377436, 0.023852581, 0.12866789], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 21, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [350.12277, 115.08481, 120.576515, 19.259644, 52.09645, 121.88193, 102.50246, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [995.18, 65.5, 2119.0483, 78.88, 7225.61, 6.0, 78.0, -0.031360265, -0.08891636, -0.032679003, 0.04667371, 0.0025226267, -0.08377436, 0.023852581, 0.12866789], "split_indices": [50, 58, 96, 42, 79, 56, 84, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4582.4824, 1108.5748, 3473.9077, 570.61615, 537.95874, 1390.3656, 2083.5422, 67.32752, 503.2886, 436.87518, 101.08353, 1201.9146, 188.45097, 1986.563, 96.97918], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.019170409, -0.4683914, 0.13728194, -0.8191136, 0.0733515, -0.20042205, 0.25582615, -0.091892056, -0.0532667, 0.056349207, -0.052458495, 0.04190426, -0.036568277, 0.03450323, -0.03318628], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 22, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [319.02603, 222.84232, 134.83398, 19.861298, 135.60748, 89.764244, 130.78322, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [39.92, 2480.09, 38070.0, -0.70710677, 2.0, 180.67, 0.7, -0.091892056, -0.0532667, 0.056349207, -0.052458495, 0.04190426, -0.036568277, 0.03450323, -0.03318628], "split_indices": [58, 50, 89, 0, 56, 88, 49, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4537.3335, 1171.5302, 3365.8035, 710.8298, 460.70044, 874.3368, 2491.4668, 525.5157, 185.3141, 253.20268, 207.49774, 183.8092, 690.5276, 2163.4817, 327.98508], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.018153192, -0.3833563, 0.1612914, -0.5609925, 0.4641922, 0.28613764, -0.36820042, -0.07821775, -0.026889667, 0.011020181, 0.09310133, 0.058616575, 0.0063753626, -0.06004855, 0.013814445], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 23, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [294.54318, 223.20172, 199.33218, 78.91196, 42.387836, 162.7182, 67.76667, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 4162.42, 1.0, 2.0, 166001.88, -0.07821775, -0.026889667, 0.011020181, 0.09310133, 0.058616575, 0.0063753626, -0.06004855, 0.013814445], "split_indices": [56, 60, 61, 79, 61, 56, 88, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4492.5845, 1479.7936, 3012.791, 1223.6658, 256.12787, 2438.3293, 574.4618, 695.40405, 528.26166, 146.37088, 109.756966, 1037.2686, 1401.0608, 393.67703, 180.78479], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.017158292, -0.2755767, 0.2174071, -0.671018, -0.006494053, 0.48766348, -0.0578161, -0.07544555, -0.011492093, 0.009884642, -0.075626805, -0.014263742, 0.062660754, -0.06889178, 0.020699251], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 24, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [269.3482, 224.96313, 173.28334, 39.635498, 99.52306, 103.02338, 193.15723, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [217.28546, 64.2, 3.0, 2480.09, 2.0, 1.0, 618.3109, -0.07544555, -0.011492093, 0.009884642, -0.075626805, -0.014263742, 0.062660754, -0.06889178, 0.020699251], "split_indices": [43, 58, 48, 50, 48, 56, 43, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4441.527, 2113.2288, 2328.2983, 855.09515, 1258.1335, 1174.3597, 1153.9385, 743.2243, 111.87088, 1103.9069, 154.22667, 212.11273, 962.2471, 340.6019, 813.33655], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.01626575, -0.40738484, 0.12040417, -0.76309955, 0.05109874, -0.0115493955, 0.39153403, -0.08776846, -0.04499414, 0.04786176, -0.046387, 0.007897548, -0.04911155, 0.053134155, -0.033063818], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 25, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [234.17804, 185.05049, 116.155685, 22.529663, 109.542465, 94.88436, 107.48421, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [47.71, 2480.09, 498651.0, -0.70710677, 2.0, 4.0, 0.8181818, -0.08776846, -0.04499414, 0.04786176, -0.046387, 0.007897548, -0.04911155, 0.053134155, -0.033063818], "split_indices": [58, 50, 80, 0, 56, 56, 57, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4378.9907, 1133.418, 3245.5725, 637.8654, 495.55258, 2183.7446, 1061.828, 465.67255, 172.1929, 270.80508, 224.74748, 1837.69, 346.05463, 889.8278, 172.00021], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.015352788, -0.45528042, 0.09715438, -0.64988905, -0.13742052, 0.14093311, -0.5143674, -0.06900161, 0.14520405, -0.01817601, 0.34822407, 0.0031605233, 0.04655873, -0.057242017, 0.20974629], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 26, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [214.66582, 54.548737, 92.54038, 47.187775, 54.669857, 114.4166, 36.220215, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [429.28, 115052.0, 2.0, 1642.43, 3779.77, 754773.0, 3156.58, -0.06900161, 0.14520405, -0.01817601, 0.34822407, 0.0031605233, 0.04655873, -0.057242017, 0.20974629], "split_indices": [26, 89, 61, 58, 86, 80, 77, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4335.226, 882.27826, 3452.9478, 546.58746, 335.69083, 3222.9844, 229.9634, 537.0074, 9.580063, 332.5668, 3.12404, 2411.8777, 811.1069, 225.72702, 4.236365], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.014169856, -0.21827398, 0.20207882, -0.7154072, -0.08257938, 0.46156076, -0.1241919, -0.080140226, 0.013650288, 0.00127029, -0.075462, 0.026392242, 0.10514009, -0.08406713, 0.011443786], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 27, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [190.03186, 149.35426, 177.04836, 34.893387, 111.49833, 135.49583, 158.56972, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [245.8525, 16.77, 4.0, 2492.4, 2.0, 6027.82, 9100.51, -0.080140226, 0.013650288, 0.00127029, -0.075462, 0.026392242, 0.10514009, -0.08406713, 0.011443786], "split_indices": [43, 58, 56, 42, 48, 42, 50, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4303.4766, 2213.9, 2089.5764, 473.8012, 1740.0988, 1163.6863, 925.8902, 430.41293, 43.388268, 1524.8813, 215.21742, 872.71295, 290.9734, 230.69919, 695.19104], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.013364639, -0.31710038, 0.12401286, -0.4922577, 0.39689288, 0.23742962, -0.35994875, -0.072264485, -0.022916144, 0.008732291, 0.07884028, 0.051511418, 0.0035490182, -0.05649634, 0.010640558], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 28, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [177.78214, 166.13904, 161.12073, 64.44614, 31.678795, 133.29614, 53.35324, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 4162.42, 1.0, 2.0, 6539674.0, -0.072264485, -0.022916144, 0.008732291, 0.07884028, 0.051511418, 0.0035490182, -0.05649634, 0.010640558], "split_indices": [56, 60, 61, 79, 61, 56, 89, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4258.7153, 1325.8998, 2932.8157, 1064.9548, 260.94495, 2376.4114, 556.4043, 566.83997, 498.1149, 146.40466, 114.54028, 999.90405, 1376.5072, 386.34933, 170.05498], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.012618619, -0.3356924, 0.112024084, -0.6745086, 0.019321058, 0.15622994, -0.4975458, -0.07030957, 0.10469102, 0.040576685, -0.043115076, 0.0062697227, 0.06243821, -0.09908271, 0.016346976], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 29, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [169.88483, 141.2847, 82.11266, 30.310425, 100.186264, 124.285484, 67.36003, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [64.2, 2480.09, 9.0, 10.0, 2.0, 782.93164, 2501.8179, -0.07030957, 0.10469102, 0.040576685, -0.043115076, 0.0062697227, 0.06243821, -0.09908271, 0.016346976], "split_indices": [58, 50, 32, 75, 56, 43, 87, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4216.8374, 1173.4814, 3043.3562, 599.9776, 573.5039, 2838.2693, 205.0869, 590.7586, 9.218995, 308.74252, 264.76138, 2366.5808, 471.68835, 117.158615, 87.92828], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.011905767, -0.25466955, 0.13010548, -0.47289482, -0.01156891, 0.20057082, -0.35158256, -0.05444848, 0.045015804, 0.023541557, -0.04135, 0.01414606, 0.18053803, -0.07789871, 0.02725835], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 30, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [144.38927, 81.9977, 89.76296, 54.069977, 72.78594, 218.48375, 90.27341, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [120.33, 1.0, 9.0, 1.0, 2.0, 1439.7, 2501.8179, -0.05444848, 0.045015804, 0.023541557, -0.04135, 0.01414606, 0.18053803, -0.07789871, 0.02725835], "split_indices": [2, 56, 48, 61, 56, 43, 87, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4186.247, 1544.7507, 2641.4966, 813.5249, 731.2259, 2304.9001, 336.5964, 755.3516, 58.173298, 453.16977, 278.0561, 2224.0686, 80.83137, 199.64159, 136.95482], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.011188824, -0.21255924, 0.14957824, -0.40453786, -0.0247731, -0.07687814, 0.309156, -0.04244748, 0.13664083, 0.01248637, -0.08001129, -0.015958698, 0.057980478, 0.028437195, 0.22505824], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 31, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [134.63547, 66.548134, 83.59436, 32.81047, 108.50484, 52.057957, 64.64484, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2119.0483, 350.6, 10672.69, 2458.34, 5.0, 43.0, 631.18, -0.04244748, 0.13664083, 0.01248637, -0.08001129, -0.015958698, 0.057980478, 0.028437195, 0.22505824], "split_indices": [96, 10, 79, 58, 40, 92, 94, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4156.805, 1845.2262, 2311.5789, 911.857, 933.3692, 955.76843, 1355.8103, 902.4576, 9.399366, 783.07794, 150.29128, 849.52783, 106.24057, 1339.8524, 15.95794], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.010345054, -0.33996662, 0.07835327, -0.6727773, 0.0061436724, 0.119842894, -0.4610219, -0.08249241, -0.025461828, 0.03629694, -0.042696204, 0.0010512116, 0.036963373, -0.051262964, 0.16061617], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 32, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [120.842834, 100.953964, 72.94057, 28.215073, 66.701065, 82.61101, 25.663425, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [39.92, 2480.09, 2.0, -0.70710677, 2.0, 498651.0, 3156.58, -0.08249241, -0.025461828, 0.03629694, -0.042696204, 0.0010512116, 0.036963373, -0.051262964, 0.16061617], "split_indices": [58, 50, 61, 0, 56, 80, 77, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4131.3213, 875.38275, 3255.9387, 445.78433, 429.59845, 3024.098, 231.84074, 326.14426, 119.640076, 235.63017, 193.96828, 2104.168, 919.9298, 226.92747, 4.913281], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.009687055, -0.16038461, 0.16681828, -0.06915365, -0.7867142, 0.52825963, -0.06448916, -0.026389033, 0.021717636, -0.09951394, 0.03436307, -0.013730481, 0.06835928, -0.06987474, 0.012316521], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 33, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [109.14955, 126.430115, 158.04262, 107.852036, 66.62283, 76.35272, 137.3577, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [274.08, 2.0, 3.0, 1.0, 1120.83, 1.0, 462.66626, -0.026389033, 0.021717636, -0.09951394, 0.03436307, -0.013730481, 0.06835928, -0.06987474, 0.012316521], "split_indices": [43, 48, 56, 48, 58, 56, 51, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4101.527, 2212.5645, 1888.9625, 1932.2273, 280.3372, 736.6259, 1152.3365, 1150.1047, 782.12256, 236.79414, 43.54304, 139.40887, 597.21704, 262.47305, 889.8635], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.009127818, -0.27716383, 0.085143, -0.5991422, 0.018568043, 0.12749438, -0.43215945, 0.044895567, -0.066270076, -0.077713974, 0.019468723, -0.013352645, 0.021894814, -0.07245836, -0.0108631635], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 34, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [102.540184, 100.552765, 65.830444, 33.974747, 77.41722, 66.29262, 21.437164, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [64.2, 2480.09, 2.0, 78.75, 1.0, 38070.0, 2433.2217, 0.044895567, -0.066270076, -0.077713974, 0.019468723, -0.013352645, 0.021894814, -0.07245836, -0.0108631635], "split_indices": [58, 50, 61, 50, 56, 89, 96, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4056.1746, 1054.8959, 3001.2786, 504.54553, 550.3504, 2774.8555, 226.42296, 28.50053, 476.04498, 99.11872, 451.2317, 719.85016, 2055.0054, 118.2772, 108.14577], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.008672299, -0.18222228, 0.12591687, 0.4225188, -0.24402697, 0.08844319, 0.976481, 0.03616153, 0.32307458, -0.018221367, -0.076880924, -0.0013284767, 0.030888945, 0.12367862, -0.102391385], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 35, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [94.12854, 65.88945, 72.25806, 26.8076, 51.682365, 48.780518, 51.08744, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2119.0483, 30.0, 78.0, 25.0, 0.36363637, 341.7735, 28.0, 0.03616153, 0.32307458, -0.018221367, -0.076880924, -0.0013284767, 0.030888945, 0.12367862, -0.102391385], "split_indices": [96, 89, 84, 80, 57, 76, 40, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4027.8472, 1759.1304, 2268.7168, 162.57216, 1596.5582, 2174.038, 94.678566, 160.22603, 2.3461368, 1429.5239, 167.03424, 1488.2189, 685.81934, 84.11471, 10.563854], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.007913575, -0.38176683, 0.051831897, -0.69184524, 0.050083514, 0.09290466, -0.34477404, 0.03280736, -0.07575669, -0.10140411, 0.02646572, 0.0044206395, 0.12970339, -0.07455765, 0.019212835], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 36, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [89.72916, 74.15668, 56.456608, 21.849777, 53.30402, 184.04298, 70.13757, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [18.1, 2492.4, 9.0, 78.75, 1.0, 14915.33, 2501.8179, 0.03280736, -0.07575669, -0.10140411, 0.02646572, 0.0044206395, 0.12970339, -0.07455765, 0.019212835], "split_indices": [58, 42, 48, 50, 56, 42, 87, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4015.3508, 552.5316, 3462.8193, 321.25858, 231.27307, 3138.5544, 324.2649, 19.206429, 302.05212, 38.183872, 193.0892, 3017.553, 121.00133, 185.44208, 138.82283], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0074868943, -0.19962719, 0.09657749, -0.5099918, -0.0006847025, 0.15854259, -0.28666928, -0.068230435, -0.012330448, 0.03317966, -0.036941364, 0.007072986, 0.062048472, -0.06122582, 0.010282292], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 37, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [79.94153, 86.72736, 61.635727, 36.4738, 105.18145, 90.543686, 45.88385, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [120.71, 1.0, 4.0, -0.70710677, 0.11111111, 718.2217, 2501.8179, -0.068230435, -0.012330448, 0.03317966, -0.036941364, 0.007072986, 0.062048472, -0.06122582, 0.010282292], "split_indices": [58, 56, 24, 0, 57, 43, 87, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3996.1191, 1403.6177, 2592.5015, 547.662, 855.9556, 2232.179, 360.32266, 378.31552, 169.34645, 450.15393, 405.80173, 1876.6003, 355.5786, 195.94995, 164.37271], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0071039503, -0.22823349, 0.07476594, 0.25976333, -0.36737648, 0.12144473, -0.30134547, -0.010414924, 0.052727975, -0.047480687, 0.03638604, 0.00810256, 0.13879417, -0.08029239, 0.0051788595], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 38, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [71.899704, 72.959526, 50.923607, 23.29015, 65.80068, 131.91785, 56.77432, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [38070.0, 180.67, 0.7, 1.0, 25201.91, 1173.7644, 1730695.0, -0.010414924, 0.052727975, -0.047480687, 0.03638604, 0.00810256, 0.13879417, -0.08029239, 0.0051788595], "split_indices": [89, 88, 49, 48, 88, 51, 80, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3969.5715, 1072.088, 2897.4836, 237.67093, 834.41705, 2578.185, 319.29874, 100.95236, 136.71858, 727.8368, 106.58021, 2499.4802, 78.70473, 131.3956, 187.90312], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0066211843, -0.12225666, 0.1400732, -0.011151528, -0.7190325, 0.4398722, -0.15352128, -0.020399896, 0.021613741, -0.08993435, 0.021937585, 0.017405542, 0.079971366, -0.071576014, 0.00492423], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 39, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [67.05535, 146.55826, 153.44289, 81.77164, 58.89981, 82.35179, 100.48322, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [254.23062, 2.0, 4.0, 1.0, 1120.83, 5462.71, 9819.03, -0.020399896, 0.021613741, -0.08993435, 0.021937585, 0.017405542, 0.079971366, -0.071576014, 0.00492423], "split_indices": [51, 56, 56, 56, 58, 42, 50, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3951.0093, 2209.4905, 1741.5189, 1863.5597, 345.93073, 861.40826, 880.11066, 1008.2197, 855.33997, 290.20984, 55.72089, 496.2842, 365.12408, 232.6024, 647.50824], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0062980307, -0.3421122, 0.044195395, -0.69079727, 0.014822431, 0.09398215, -0.24401385, 0.0379676, -0.07745005, -0.0882839, 0.019711018, 0.0033046126, 0.11266243, -0.0832495, -0.0017733317], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 40, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [66.42824, 63.739746, 48.890472, 23.504868, 41.73906, 182.74205, 66.82292, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [18.1, 2559.75, 6.0, 78.75, 1.0, 858.7783, 1049.23, 0.0379676, -0.07745005, -0.0882839, 0.019711018, 0.0033046126, 0.11266243, -0.0832495, -0.0017733317], "split_indices": [58, 50, 48, 50, 56, 43, 51, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3915.697, 511.05734, 3404.6396, 258.043, 253.01434, 2903.7114, 500.92822, 18.45737, 239.58562, 42.058334, 210.95601, 2742.8862, 160.82523, 138.37677, 362.55148], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.005983582, -0.1516754, 0.104265675, -0.09656898, -0.70942897, 0.07523762, 0.9887283, -0.017980125, 0.07068236, -0.09759073, -0.040948387, -0.0017661992, 0.027655352, 0.121489644, -0.06429915], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 41, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [62.609356, 51.537983, 56.893127, 102.38318, 11.65493, 40.20394, 26.771584, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2119.0483, 6.0, 138.0, 529.4308, 1173.7644, 341.7735, 25.0, -0.017980125, 0.07068236, -0.09759073, -0.040948387, -0.0017661992, 0.027655352, 0.121489644, -0.06429915], "split_indices": [96, 56, 74, 51, 51, 76, 40, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3895.8948, 1678.0349, 2217.8599, 1528.219, 149.81593, 2148.4333, 69.4265, 1385.4601, 142.75883, 78.14048, 71.67545, 1470.6819, 677.7514, 61.199284, 8.227214], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0055270228, -0.17357258, 0.08557016, -0.43972716, 0.011359365, 0.13136885, -0.40682435, -0.05879047, 0.003025224, 0.02992693, -0.033659413, 0.007708502, 0.048310164, -0.045739893, 0.13730328], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 42, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [59.44474, 67.1927, 56.83963, 38.991936, 80.88396, 43.965256, 19.938946, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [126.66, 2029.28, 2.0, 148343.0, 2.0, 747781.0, 3156.58, -0.05879047, 0.003025224, 0.02992693, -0.033659413, 0.007708502, 0.048310164, -0.045739893, 0.13730328], "split_indices": [58, 42, 61, 80, 56, 71, 77, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3881.1558, 1364.0354, 2517.1204, 558.64874, 805.38666, 2303.5916, 213.52885, 424.54507, 134.1037, 440.79556, 364.59106, 1996.6609, 306.93063, 208.35156, 5.1772876], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.00520455, -0.30612376, 0.03769675, -0.63339907, 0.031907186, 0.10006652, -0.18598178, 0.04213391, -0.0718486, -0.08461893, 0.020198502, 0.0015420428, 0.06980908, -0.05958394, 0.009600411], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 43, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [49.80932, 53.279675, 47.129436, 22.258812, 35.68095, 133.69183, 85.19525, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [18.1, 2627.44, 0.26666668, 78.75, 1.0, 6027.82, 935.14935, 0.04213391, -0.0718486, -0.08461893, 0.020198502, 0.0015420428, 0.06980908, -0.05958394, 0.009600411], "split_indices": [58, 50, 57, 50, 56, 42, 51, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3856.3547, 480.4233, 3375.9314, 243.6511, 236.77223, 2640.2778, 735.6536, 17.893347, 225.75775, 37.773865, 198.99837, 2313.8015, 326.47638, 299.38867, 436.26495], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0049384325, -0.13240066, 0.089777686, -0.09427043, -0.8745593, 0.05983462, 0.79200315, -0.011568262, 0.16245253, -0.10736006, -0.02665983, 0.015965667, -0.013145928, 0.10095405, -0.091993034], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 44, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [46.306755, 46.19007, 46.22242, 57.467083, 9.368347, 40.342842, 34.4126, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2119.0483, 9.0, 78.0, 18035.4, 23.0, 3.0, 28.0, -0.011568262, 0.16245253, -0.10736006, -0.02665983, 0.015965667, -0.013145928, 0.10095405, -0.091993034], "split_indices": [96, 56, 84, 50, 75, 56, 40, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3833.6655, 1634.1476, 2199.5178, 1555.364, 78.78364, 2110.6062, 88.911545, 1537.1475, 18.216581, 58.77522, 20.008415, 1386.9998, 723.60645, 79.25129, 9.660255], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.004548818, -0.0916015, 0.13852756, -0.31685713, 0.027784409, 0.29103652, -0.13608977, -0.037819326, 0.074489, 0.029375011, -0.023145748, 0.032859627, -0.073588245, 0.07122909, -0.027537366], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 45, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [47.631798, 63.947865, 60.62733, 53.923485, 107.2775, 36.12909, 61.31351, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [393.81, 1.0, 1.0, 1.0, 0.125, 17.0, 1.0, -0.037819326, 0.074489, 0.029375011, -0.023145748, 0.032859627, -0.073588245, 0.07122909, -0.027537366], "split_indices": [58, 56, 61, 61, 57, 40, 56, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3822.2363, 2376.5935, 1445.643, 822.69336, 1553.9, 929.4258, 516.2172, 778.3714, 44.321983, 766.9376, 786.9624, 897.28735, 32.138428, 72.21859, 443.9986], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.00433979, -0.10674301, 0.11150712, -0.022020759, -0.7085048, 0.4230671, -0.08579908, -0.023213265, 0.009891778, -0.093506955, 0.020507583, -0.010749981, 0.05480658, -0.065410234, 0.007847396], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 46, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [45.10801, 102.88147, 109.70978, 45.005478, 51.74205, 45.947838, 102.10629, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [274.08, 2.0, 3.0, 6066.33, 1029.11, 1.0, 462.66626, -0.023213265, 0.009891778, -0.093506955, 0.020507583, -0.010749981, 0.05480658, -0.065410234, 0.007847396], "split_indices": [43, 48, 56, 88, 58, 56, 51, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3800.3777, 2017.2777, 1783.1, 1769.2245, 248.05313, 690.93, 1092.1699, 645.9929, 1123.2316, 198.74223, 49.31089, 131.76842, 559.16156, 244.23973, 847.9302], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.004180726, -0.14638363, 0.07513651, -0.10900826, -0.7640715, 0.1539878, -0.20189635, -0.023676876, 0.020524764, -0.08364059, 0.1341892, 0.023453211, -0.0078209555, 0.07926006, -0.03264023], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 47, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [42.535526, 31.089916, 52.914234, 51.21566, 12.83313, 35.273506, 66.728004, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [131.13, 0.23076923, 1.0, 2492.4, 242744.95, 0.5, 1.0, -0.023676876, 0.020524764, -0.08364059, 0.1341892, 0.023453211, -0.0078209555, 0.07926006, -0.03264023], "split_indices": [2, 57, 61, 42, 79, 25, 56, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3769.192, 1349.2897, 2419.9023, 1273.4138, 75.87587, 1884.0837, 535.81854, 905.52136, 367.89246, 73.939095, 1.9367752, 1398.8444, 485.23932, 59.020847, 476.79773], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.003896405, -0.08672319, 0.12991704, -0.28914836, 0.018703477, 0.20260616, -0.3361269, -0.048776012, 0.00011015596, 0.02514863, -0.021079317, 0.026775485, -0.03626887, -0.060776383, -0.0019971968], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 48, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [41.60047, 49.48179, 48.68185, 45.77276, 81.54231, 45.839115, 16.64844, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [393.81, 1.0, 2.0, 3.0, 0.125, 10.0, 15461.57, -0.048776012, 0.00011015596, 0.02514863, -0.021079317, 0.026775485, -0.03626887, -0.060776383, -0.0019971968], "split_indices": [58, 56, 61, 84, 57, 40, 70, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3751.4077, 2317.3713, 1434.0363, 793.00824, 1524.3632, 1241.0365, 192.99982, 470.42563, 322.58258, 756.7159, 767.6472, 1113.2623, 127.77412, 103.312485, 89.68734], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0037693407, -0.0931681, 0.120170094, 0.0095609175, -0.60442245, 0.37412032, -0.1063792, -0.019820884, 0.012254175, -0.08296578, -0.0021427206, 0.012307665, 0.06550827, -0.06183989, 0.0062572896], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 49, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [41.34948, 113.8721, 90.0117, 42.42464, 47.616745, 51.942085, 71.60529, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [275.48834, 2.0, 4.0, 6066.33, 719.1, 5462.71, 9819.03, -0.019820884, 0.012254175, -0.08296578, -0.0021427206, 0.012307665, 0.06550827, -0.06183989, 0.0062572896], "split_indices": [51, 56, 56, 88, 58, 42, 50, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3729.881, 2167.0293, 1562.8517, 1805.2686, 361.76077, 736.5572, 826.2945, 635.62006, 1169.6486, 260.64117, 101.1196, 389.7491, 346.8081, 204.3473, 621.9472], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0036278684, -0.08386459, 0.12917137, 0.16488121, -0.17199627, 0.8689739, 0.07510389, 0.009640825, 0.12863153, -0.014183178, -0.09121151, -0.045635417, 0.12666693, -0.016249327, 0.025762096], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 50, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [39.511703, 50.70202, 55.798805, 46.35041, 37.98601, 50.663216, 56.52994, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [799.00134, 135.2493, 648.8684, 15.0, 9.0, 0.26666668, 3.0, 0.009640825, 0.12863153, -0.014183178, -0.09121151, -0.045635417, 0.12666693, -0.016249327, 0.025762096], "split_indices": [86, 94, 95, 75, 24, 53, 84, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3706.1306, 2310.5032, 1395.6276, 604.23083, 1706.2723, 94.02441, 1301.6031, 570.48395, 33.746864, 1640.5992, 65.67309, 21.667965, 72.356445, 565.52435, 736.0788], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0033463715, -0.011362019, 1.30692, -0.1029438, 0.10220455, 0.6488417, 4.0466576, -0.0012607193, -0.062424947, 0.036462903, -0.006665025, 0.17484058, -0.018486587, 0.62051505, -0.066698246], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 51, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [38.861732, 38.25651, 37.938396, 95.88167, 72.78852, 18.04521, 46.530106, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3779.77, 254.23062, 249341.0, 2.0, 3.0, 52251.0, 1288.64, -0.0012607193, -0.062424947, 0.036462903, -0.006665025, 0.17484058, -0.018486587, 0.62051505, -0.066698246], "split_indices": [86, 51, 80, 56, 48, 89, 70, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3697.7754, 3676.2766, 21.498554, 2035.1748, 1641.1019, 18.332064, 3.1664915, 1735.4623, 299.71255, 642.07153, 999.0304, 7.4324164, 10.899647, 1.9548793, 1.2116122], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.002980654, -0.14626487, 0.07079561, -0.34093702, 0.013560827, 0.13889273, -0.14234251, -0.01131435, -0.066695414, -0.069384955, 0.015378649, -0.024313712, 0.018800674, 0.057499226, -0.030308297], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 52, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [38.938614, 38.958397, 35.3119, 41.84665, 68.40272, 34.631786, 68.119385, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [126.66, 193.86667, 1.0, -1.8369701e-16, 1.0, 429.28, 1.0, -0.01131435, -0.066695414, -0.069384955, 0.015378649, -0.024313712, 0.018800674, 0.057499226, -0.030308297], "split_indices": [58, 43, 61, 1, 56, 26, 56, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3681.5615, 1250.9849, 2430.5767, 563.4968, 687.48804, 1842.311, 588.2657, 332.51212, 230.9847, 113.07902, 574.409, 209.4179, 1632.8932, 107.21615, 481.0495], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0029589438, -0.017584365, 0.6547345, -0.100136004, 0.11431769, 0.9000834, -0.72868234, -0.003973032, -0.049853686, 0.0032469982, 0.05849708, 0.10259873, -0.10794207, -0.14315371, 0.11497082], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 53, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [35.24528, 39.03895, 27.710407, 53.045982, 53.147747, 18.091991, 18.227287, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [139.0, 799.00134, 681.795, 6.0, 1214.04, 162.0, 2495.088, -0.003973032, -0.049853686, 0.0032469982, 0.05849708, 0.10259873, -0.10794207, -0.14315371, 0.11497082], "split_indices": [74, 86, 86, 56, 58, 46, 78, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3661.9185, 3583.2102, 78.708176, 2204.0403, 1379.1702, 67.14873, 11.559449, 1914.815, 289.22525, 1175.7699, 203.40024, 63.586105, 3.5626192, 8.585008, 2.974441], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.002751172, -0.075991735, 0.119545475, -0.03534987, -0.5347948, 0.18317102, -0.29063985, -0.023867782, 0.0105519695, -0.07149612, -0.006266025, 0.024081271, -0.031224761, -0.05958709, 0.00039355518], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 54, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [32.763817, 42.639935, 35.810555, 60.251076, 15.784782, 33.96445, 16.579754, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [413.37, 0.33333334, 2.0, 1.0, 2599.07, 10.0, 2433.2217, -0.023867782, 0.0105519695, -0.07149612, -0.006266025, 0.024081271, -0.031224761, -0.05958709, 0.00039355518], "split_indices": [58, 25, 61, 48, 10, 40, 96, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3655.8562, 2286.7527, 1369.1034, 2101.6597, 185.09297, 1185.7329, 183.37051, 859.86115, 1241.7986, 133.5978, 51.49518, 1062.6124, 123.120445, 89.55413, 93.81638], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.002674964, -0.037609257, 0.2467897, -0.006091866, -0.5121835, -0.06784331, 0.56799775, -0.0050812555, 0.066815995, -0.09189925, -0.019774264, 0.014527172, -0.10014834, 0.14923634, 0.042372257], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 55, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [31.739573, 47.779118, 45.250786, 90.391495, 25.265263, 45.290535, 28.904617, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [747781.0, 8.0, 2425.219, 706.9275, 18443.08, 2426.9324, 6.0, -0.0050812555, 0.066815995, -0.09189925, -0.019774264, 0.014527172, -0.10014834, 0.14923634, 0.042372257], "split_indices": [71, 48, 87, 51, 34, 78, 75, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3639.9363, 3193.5676, 446.36853, 2995.6348, 197.93304, 225.89062, 220.4779, 2810.171, 185.46385, 85.453995, 112.47905, 184.59818, 41.29244, 28.504816, 191.9731], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0024946015, -0.009943995, 1.17635, -0.31597587, 0.018333422, 2.2034478, -0.032755323, -0.06999262, 0.0030913574, 0.0004762298, 0.06040908, 0.45303613, 0.062152624, 0.115769126, -0.10299235], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 56, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [31.89718, 31.238203, 28.397808, 40.687904, 26.268032, 42.634617, 14.798073, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3779.77, 7.89, 3191.2, 2627.44, 139.0, 1288.64, 301.0, -0.06999262, 0.0030913574, 0.0004762298, 0.06040908, 0.45303613, 0.062152624, 0.115769126, -0.10299235], "split_indices": [86, 58, 96, 50, 74, 70, 90, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3629.9102, 3608.1013, 21.808867, 304.3292, 3303.7722, 11.347302, 10.461564, 143.9653, 160.3639, 3229.9473, 73.82492, 3.8379989, 7.5093036, 4.6952577, 5.766306], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0022351758, -0.042450454, 0.20546949, -0.009111165, -0.41746983, -0.3918347, 0.35627463, -0.015720535, 0.0066310936, -0.05315859, 0.09965291, -0.056403365, 0.1623718, 0.027843086, 0.119261004], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 57, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [30.27377, 37.9598, 53.09642, 31.168106, 40.52685, 42.213684, 30.261452, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [426.0677, 1134.748, 8150.0, 312.89, 11065.95, 3.0, 184.0, -0.015720535, 0.0066310936, -0.05315859, 0.09965291, -0.056403365, 0.1623718, 0.027843086, 0.119261004], "split_indices": [85, 95, 89, 10, 70, 75, 91, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3622.298, 3035.389, 586.9091, 2788.5137, 246.87508, 117.987946, 468.92117, 940.5748, 1847.939, 229.01648, 17.858599, 109.36454, 8.623408, 430.2112, 38.70995], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0020674923, 0.4248852, -0.020585326, 0.53577876, -0.97518814, -0.3829878, 0.028414479, 0.047174368, 0.28519043, -0.11524073, 0.04302754, -0.0018664375, -0.05482442, -0.0024121313, 0.03156405], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 58, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [28.596348, 23.866169, 61.56733, 19.413109, 3.3326616, 24.851624, 46.099434, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [30.0, 14.0, 8850.0, 25.0, 7.0, 158.07, 426.0677, 0.047174368, 0.28519043, -0.11524073, 0.04302754, -0.0018664375, -0.05482442, -0.0024121313, 0.03156405], "split_indices": [89, 54, 89, 80, 6, 70, 85, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3614.8276, 149.3531, 3465.4746, 138.96373, 10.389372, 411.94128, 3053.5332, 136.3964, 2.5673404, 9.385924, 1.003448, 128.89435, 283.04694, 2582.1538, 471.37933], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0019360809, -0.031120367, 0.24616085, -0.0013035486, -0.49866307, 0.13279857, 1.3132212, -0.006460187, 0.018061088, -0.03475861, -0.1829519, 0.007554485, 0.21048747, 0.05775958, 0.22690955], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 59, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [26.167116, 45.07697, 45.837753, 35.024868, 38.33969, 38.79002, 24.296684, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1340.8966, 1144.82, 554453.0, 803.64, 2323.88, 4033.19, 412.23264, -0.006460187, 0.018061088, -0.03475861, -0.1829519, 0.007554485, 0.21048747, 0.05775958, 0.22690955], "split_indices": [86, 86, 80, 86, 77, 95, 94, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3611.919, 3232.5413, 379.37766, 3039.6924, 192.84894, 343.96048, 35.417194, 2255.5156, 784.17664, 174.32137, 18.52756, 335.2653, 8.695185, 20.920994, 14.4962015], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0017433356, 0.07320035, -0.098801695, 0.18317933, -0.027560664, -0.46309277, 0.019655144, 0.021776661, -0.053525694, -0.0710948, 0.001330137, -0.07475797, -0.00857651, 0.02539638, -0.0112881465], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 60, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [26.272669, 22.591116, 67.92073, 24.335033, 29.73589, 41.393272, 36.938194, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [-1.8369701e-16, 10.0, 133.424, 779.0, 24846.0, 240.96, 2.0, 0.021776661, -0.053525694, -0.0710948, 0.001330137, -0.07475797, -0.00857651, 0.02539638, -0.0112881465], "split_indices": [1, 73, 51, 72, 89, 58, 56, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3609.9097, 2037.1605, 1572.7491, 973.6355, 1063.525, 385.2078, 1187.5414, 929.5801, 44.055367, 59.078697, 1004.44635, 219.06334, 166.14445, 428.71283, 758.82855], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0018339739, -0.014033375, 0.58277494, 0.02868345, -0.1949714, 0.82470745, -1.0274181, -0.0028240145, 0.07594828, -0.058108866, 0.009671251, -0.008430458, 0.11801495, -0.0020678875, -0.11860353], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 61, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [25.686432, 27.272827, 29.52712, 118.78603, 75.95717, 20.95869, 1.5822363, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [87.0, 0.26666668, 11.0, 529.4308, 935.14935, 2148.685, 3.6875, -0.0028240145, 0.07594828, -0.058108866, 0.009671251, -0.008430458, 0.11801495, -0.0020678875, -0.11860353], "split_indices": [84, 57, 24, 51, 51, 96, 55, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3599.4893, 3526.868, 72.621376, 2853.9365, 672.93134, 63.559364, 9.062014, 2648.593, 205.34348, 289.16122, 383.77008, 18.08117, 45.478195, 1.387289, 7.674724], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0016940408, -0.22777888, 0.027382558, -0.57405853, -0.010011044, -0.009990136, 0.22106554, 0.07405328, -0.07117721, -0.07300178, 0.0126621565, 0.0026262365, -0.036398835, 0.0148667395, 0.11549858], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 62, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [23.583525, 30.822016, 23.011484, 29.097588, 24.882696, 34.2175, 34.595413, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [18.1, 155.78813, 426.0677, 78.75, 1.0, 369.35315, 184.0, 0.07405328, -0.07117721, -0.07300178, 0.0126621565, 0.0026262365, -0.036398835, 0.0148667395, 0.11549858], "split_indices": [58, 51, 85, 50, 56, 94, 91, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3585.5654, 407.80984, 3177.7556, 156.81583, 250.994, 2664.5564, 513.1993, 14.454838, 142.361, 39.34045, 211.65355, 2417.871, 246.68526, 477.35364, 35.8456], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0016698746, 0.36774364, -0.018962806, 0.29986456, 0.41454282, -0.38647702, 0.017634327, 0.03953209, -0.09581977, -0.027579183, -0.11516782, -0.020874392, 0.005644962], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 63, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [22.865664, 39.801136, 45.99469, 19.390892, 0.0, 25.912148, 27.343464, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 40.0, 350.0, 14.0, 0.41454282, 868.7388, 1415.8575, 0.03953209, -0.09581977, -0.027579183, -0.11516782, -0.020874392, 0.005644962], "split_indices": [71, 91, 71, 54, 0, 77, 96, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3577.244, 159.06273, 3418.1812, 157.3154, 1.7473333, 308.69153, 3109.4897, 146.8582, 10.457197, 270.87088, 37.820644, 454.48254, 2655.007], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.0015496926, 0.06628059, -0.09083755, 0.17284858, -0.03192846, -0.58561784, -0.015930144, 0.014849312, 0.11225336, -0.065421104, 0.00037432506, -0.07377336, 0.011268652, 0.014036225, -0.012397199], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 64, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [21.64448, 21.261366, 57.173225, 22.366144, 23.482765, 21.62947, 22.66201, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [-1.8369701e-16, 10.0, 75.0, 3.0, 24846.0, 596.58, 0.083333336, 0.014849312, 0.11225336, -0.065421104, 0.00037432506, -0.07377336, 0.011268652, 0.014036225, -0.012397199], "split_indices": [1, 73, 51, 75, 89, 58, 57, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3571.8137, 2029.954, 1541.8596, 973.1814, 1056.7726, 201.8404, 1340.0192, 949.97485, 23.206543, 56.353985, 1000.41864, 165.69058, 36.14982, 547.58636, 792.43286], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0015656976, 0.0037997807, -0.111840956, -0.022793667, 0.22830878, 0.0004394727, -0.04579396, 0.008524637, 0.10324844], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 65, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [21.381329, 21.208158, 0.0, 37.57828, 43.192554, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1244.24, 1340.8966, -0.111840956, 1144.82, 2.0, 0.0004394727, -0.04579396, 0.008524637, 0.10324844], "split_indices": [94, 86, 0, 86, 75, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3566.3647, 3550.3052, 16.05958, 3175.1072, 375.19803, 2989.321, 185.78613, 319.4704, 55.72761], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0014780617, -0.008183165, 0.88291883, 0.010797763, -0.43123323, 1.3915107, -0.26115423, -0.0013483906, 0.09334391, -0.0999001, 0.015813515, 0.15629688, -0.069074266, -0.15387572, 0.09909646], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 66, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [21.153374, 28.431528, 15.981504, 75.93944, 51.03386, 7.7698708, 16.08569, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [249.0, 1.0909091, 224.0, 23195.69, 2501.8179, 160.0, 41500.8, -0.0013483906, 0.09334391, -0.0999001, 0.015813515, 0.15629688, -0.069074266, -0.15387572, 0.09909646], "split_indices": [74, 49, 93, 50, 87, 38, 42, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3564.9768, 3539.1355, 25.841402, 3388.0967, 151.03879, 17.739237, 8.102166, 3302.1768, 85.91985, 76.575134, 74.46365, 16.620083, 1.1191536, 3.8969648, 4.2052016], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0013947542, 0.031949054, -0.18118155, -0.03189491, 0.60354257, -0.50697726, 0.13837664, 0.0028525724, -0.052595593, -0.09172534, 0.072282165, -0.06613549, 0.014761152, 0.08051983, -0.029554024], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 67, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [21.351234, 109.66058, 58.081608, 80.71993, 55.43033, 28.001259, 81.85736, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.33333334, 529.4308, 1323.0508, 3.0, 1.0, 2882.21, 0.84615386, 0.0028525724, -0.052595593, -0.09172534, 0.072282165, -0.06613549, 0.014761152, 0.08051983, -0.029554024], "split_indices": [57, 51, 51, 48, 48, 58, 57, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3559.6743, 3003.4648, 556.2095, 2702.5486, 300.91614, 275.12634, 281.0831, 2408.8975, 293.65112, 21.398426, 279.5177, 222.61475, 52.511593, 110.467125, 170.61598], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0014109692, -0.053882487, 0.11091091, -0.0145578785, -0.4523527, 0.24183737, -0.08999783, -0.007175164, 0.03201836, -0.0900576, -0.022448089, 0.027458912, -0.07346257, -0.02969937, 0.026752327], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 68, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [20.901693, 37.87037, 29.725096, 42.16371, 21.97097, 22.075191, 33.10553, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [533.89, 0.5, 1.0, 560.27124, 658.6625, 19.0, 796.20856, -0.007175164, 0.03201836, -0.0900576, -0.022448089, 0.027458912, -0.07346257, -0.02969937, 0.026752327], "split_indices": [58, 25, 61, 35, 19, 40, 86, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3544.433, 2416.2122, 1128.2208, 2200.121, 216.09113, 682.95496, 445.26587, 1879.736, 320.3851, 71.8366, 144.25452, 661.4867, 21.468271, 282.10397, 163.16191], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0014121447, -0.0076847044, 0.9216428, 0.3528445, -0.023885055, 2.1579187, 0.1636145, 0.02960734, 0.23917535, -0.027423257, 0.0029057781, -0.06876973, 0.289591, 0.15883808, -0.10939062], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 69, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [20.481844, 20.529154, 22.022486, 16.787516, 44.584892, 20.877975, 30.195114, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3779.77, 30.0, 317.08075, 25.0, 1415.8575, 148343.0, 52251.0, 0.02960734, 0.23917535, -0.027423257, 0.0029057781, -0.06876973, 0.289591, 0.15883808, -0.10939062], "split_indices": [86, 89, 76, 80, 96, 80, 89, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3535.2788, 3512.4023, 22.876625, 150.14882, 3362.2534, 7.9933834, 14.883243, 147.19554, 2.953282, 586.19055, 2776.063, 1.6601543, 6.333229, 6.8542724, 8.02897], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.001202661, 0.023111256, -0.23124783, -0.0044587594, 0.97853315, -0.76924056, 0.042835984, 0.0024392507, -0.0480806, 0.1539988, 0.05728453, -0.09322605, -0.014893434, 0.07268351, -0.02369692], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 70, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [19.754318, 84.14354, 49.813644, 42.693165, 19.524956, 11.388664, 43.158268, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 14915.33, 23195.69, 5.0, 855614.0, 3474.86, 0.8181818, 0.0024392507, -0.0480806, 0.1539988, 0.05728453, -0.09322605, -0.014893434, 0.07268351, -0.02369692], "split_indices": [48, 42, 50, 48, 80, 58, 57, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3529.8276, 3193.2288, 336.59897, 3104.6355, 88.5932, 112.99571, 223.60326, 2928.2283, 176.4073, 35.989403, 52.603798, 89.08165, 23.914059, 64.45117, 159.15208], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0011638346, -0.011708829, 0.52385134, 0.029335147, -0.15831818, 0.7316749, -0.9542169, -0.003297589, 0.04940224, -0.05616039, 0.0051562074, -0.0025004668, 0.11075828, 0.00067462324, -0.111586444], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 71, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [19.516375, 20.798836, 22.01923, 78.21467, 64.01694, 17.497906, 1.4040508, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [87.0, 4.0, 11.0, 5462.71, 1036.9062, 2161.5486, 53.0, -0.003297589, 0.04940224, -0.05616039, 0.0051562074, -0.0025004668, 0.11075828, 0.00067462324, -0.111586444], "split_indices": [84, 56, 24, 42, 35, 96, 38, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3523.069, 3454.6594, 68.40956, 2699.6733, 754.98627, 60.419296, 7.990268, 2381.2896, 318.38382, 257.85022, 497.13605, 20.363098, 40.0562, 1.2885218, 6.7017465], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0010810494, -0.105926976, 0.050732408, -0.06107531, -0.6401083, 0.11062661, -0.13870046, -0.022938428, 0.01333988, -0.109868124, -0.027837334, 0.02440082, -0.0016830286, 0.05003611, -0.029053539], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 72, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [19.077246, 27.788374, 26.681437, 35.13882, 14.600502, 30.370953, 54.97183, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [126.66, 3.0, 1.0, 2492.4, 565.955, 248.62817, 1.0, -0.022938428, 0.01333988, -0.109868124, -0.027837334, 0.02440082, -0.0016830286, 0.05003611, -0.029053539], "split_indices": [58, 40, 61, 42, 51, 94, 56, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3509.7444, 1160.466, 2349.2783, 1071.6047, 88.861206, 1785.2428, 564.0356, 574.35, 497.25473, 38.28716, 50.574043, 871.89954, 913.34326, 107.8419, 456.1937], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0011309758, 0.003694713, -0.11086821, -0.03105443, 0.18308383, 0.00013267765, -0.03124179, 0.030968735, -0.022047987], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 73, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [18.709179, 21.733335, 0.0, 26.61148, 28.974518, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1244.24, 426.0677, -0.11086821, 1065.9138, 1281.45, 0.00013267765, -0.03124179, 0.030968735, -0.022047987], "split_indices": [94, 85, 0, 95, 77, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3498.746, 3484.5603, 14.185843, 2919.7983, 564.76196, 2619.342, 300.45627, 430.0743, 134.68764], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.00111732, 0.06169502, -0.08506923, 0.1652805, -0.0348416, -0.37100416, 0.032336883, 0.019636074, -0.050768614, -0.06421101, -0.0005058295, -0.07020568, -0.009438175, 0.026959298, -0.008441005], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 74, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [18.459356, 20.029469, 50.30667, 20.306906, 18.757793, 39.877285, 29.46681, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [-1.8369701e-16, 10.0, 2337.42, 779.0, 22045.0, 174.96, 2.0, 0.019636074, -0.050768614, -0.06421101, -0.0005058295, -0.07020568, -0.009438175, 0.026959298, -0.008441005], "split_indices": [1, 73, 50, 72, 89, 58, 56, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3498.5925, 2001.4124, 1497.1802, 965.115, 1036.2974, 435.17642, 1062.0038, 923.1869, 41.928154, 47.48756, 988.8098, 197.39847, 237.77797, 349.80728, 712.1965], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0012143536, -0.011595201, 0.49394608, 0.014828, -0.27539095, 0.71207976, -0.7795921, -0.00089163304, 0.10428619, -0.06576981, 0.013669878, -0.06880395, 0.0859237, -0.133999, 0.094279565], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 75, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [17.94804, 23.841896, 20.47331, 75.91161, 49.152237, 13.311346, 11.844368, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [139.0, 8.0, 681.795, 14915.33, 2501.8179, 1974.2206, 2495.088, -0.00089163304, 0.10428619, -0.06576981, 0.013669878, -0.06880395, 0.0859237, -0.133999, 0.094279565], "split_indices": [74, 56, 86, 42, 87, 96, 78, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3489.5972, 3418.898, 70.69929, 3108.4797, 310.4182, 60.73702, 9.962271, 3039.2734, 69.20628, 160.71887, 149.69936, 5.426963, 55.31006, 7.68416, 2.278111], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0011348666, -0.048665162, 0.10659117, -0.02189657, -0.5088933, -0.021452503, 0.31071746, -0.006079629, 0.09692284, -0.1084378, 0.0027933044, 0.038009994, -0.015095974, 0.05494224, -0.008266716], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 76, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [17.838818, 29.770042, 27.884209, 88.17678, 41.066135, 34.191734, 38.65919, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [564.59, 6.0, 796.20856, 706.9275, 1173.7644, 387.505, 1024.015, -0.006079629, 0.09692284, -0.1084378, 0.0027933044, 0.038009994, -0.015095974, 0.05494224, -0.008266716], "split_indices": [58, 56, 86, 51, 51, 86, 95, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3481.9607, 2416.3708, 1065.5897, 2284.541, 131.82986, 655.38, 410.20975, 2199.1667, 85.37434, 63.131798, 68.69807, 159.35284, 496.02716, 255.04958, 155.16016], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0010901926, -0.006897237, 0.86796904, -0.25169036, 0.013882634, 1.3518425, -0.6665481, -0.061099898, 0.0026990545, 0.0031971398, -0.027817613, 0.16956525, -0.076938495, 0.04512987, -0.09686168], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 77, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [17.551561, 17.576157, 18.041765, 27.08805, 16.836378, 14.345074, 2.5319915, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3779.77, 7.89, 1959.69, 2627.44, 2.0, 803.0, 838.0, -0.061099898, 0.0026990545, 0.0031971398, -0.027817613, 0.16956525, -0.076938495, 0.04512987, -0.09686168], "split_indices": [86, 58, 77, 50, 61, 81, 71, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3475.6667, 3453.582, 22.084734, 269.3575, 3184.2244, 16.880814, 5.20392, 117.13747, 152.22003, 2999.349, 184.87524, 14.699015, 2.1817994, 1.0019691, 4.2019506], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0010727724, 0.0033419393, -0.109480076, -0.02056323, 0.20346399, 0.00046166015, -0.042708717, 0.0071687438, 0.093039595], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 78, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [16.764736, 16.548653, 0.0, 31.633978, 35.29206, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1244.24, 1340.8966, -0.109480076, 1144.82, 2.0, 0.00046166015, -0.042708717, 0.0071687438, 0.093039595], "split_indices": [94, 86, 0, 86, 75, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3470.285, 3457.3267, 12.958172, 3089.2087, 368.1179, 2909.9565, 179.25221, 312.55692, 55.560974], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.001024885, -0.010583653, 0.49120116, 1.4441115, -0.013916011, 0.6594594, -0.106670775, 0.19652717, -0.064251654, -0.007167128, 0.0076453225, -0.032338407, 0.092020854], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 79, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [16.331966, 16.52562, 18.036499, 9.735781, 17.741278, 15.750023, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [87.0, 0.1925, 16.0, 262.71667, 799.00134, 2061.1528, -0.106670775, 0.19652717, -0.064251654, -0.007167128, 0.0076453225, -0.032338407, 0.092020854], "split_indices": [84, 43, 32, 85, 86, 87, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3469.034, 3403.9102, 65.12363, 6.791552, 3397.1187, 59.29619, 5.8274407, 5.4808125, 1.3107395, 2072.6733, 1324.4453, 12.382508, 46.91368], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.0009268267, -0.017580325, 0.26732072, -0.0021058416, -0.60130787, -0.011930141, 1.0405444, 0.0023175755, -0.02845572, 0.004249096, -0.074853875, 0.19646794, -0.012846797, 0.22025967, 0.03128669], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 80, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [15.493694, 29.49553, 43.78733, 22.729853, 8.025042, 34.78905, 44.80598, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2882.21, 2.0, 302.655, 7.0, 499.7243, 55.97, 866.2037, 0.0023175755, -0.02845572, 0.004249096, -0.074853875, 0.19646794, -0.012846797, 0.22025967, 0.03128669], "split_indices": [58, 61, 85, 56, 95, 85, 95, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3466.2249, 3264.4915, 201.73355, 3181.163, 83.32829, 148.93135, 52.8022, 2920.6592, 260.50378, 15.641941, 67.686356, 7.408936, 141.52242, 19.55203, 33.25017], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0008350497, -0.047430534, 0.10292525, 0.04666075, -0.4246088, 0.36150953, -0.09092604, -0.008065204, 0.01888605, -0.06720068, -0.0012433467, -0.0045732474, 0.058396634, -0.053077925, 0.00867972], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 81, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [16.763634, 84.93746, 53.895634, 34.70125, 48.71644, 41.753746, 48.116703, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [5226.2, 2.0, 0.26666668, 1.0, 533.89, 1.5, 851.5494, -0.008065204, 0.01888605, -0.06720068, -0.0012433467, -0.0045732474, 0.058396634, -0.053077925, 0.00867972], "split_indices": [42, 48, 57, 48, 58, 53, 51, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3465.3037, 2391.7754, 1073.5282, 1914.947, 476.82843, 459.5951, 613.9331, 1010.58954, 904.35754, 297.5819, 179.24652, 162.64404, 296.95102, 176.1031, 437.83002], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0008936734, -0.025672015, 0.17822911, 0.0061691906, -0.4153568, 0.07424755, 0.88845795, 0.0033284922, -0.06420371, -0.054444324, 0.053630628, 0.08625152, -0.0060322876, -0.02400017, 0.14260638], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 82, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [15.299301, 37.57576, 30.869484, 49.24252, 28.393631, 38.928047, 32.741177, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [747781.0, 383268.0, 967.7344, 9.0, 1090.664, 8.0, 25.0, 0.0033284922, -0.06420371, -0.054444324, 0.053630628, 0.08625152, -0.0060322876, -0.02400017, 0.14260638], "split_indices": [71, 71, 95, 56, 86, 75, 74, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3445.0264, 3027.1348, 417.89172, 2799.381, 227.7537, 365.48688, 52.40483, 2687.89, 111.49102, 200.92723, 26.826462, 52.50735, 312.97955, 17.088614, 35.31622], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0008092667, -0.05881547, 0.08209964, 0.0105489325, -0.49772134, 0.3108593, -0.049667906, 0.005384629, -0.036011208, -0.0668297, 0.021098515, -0.021381384, 0.042793967, -0.05688904, 0.0077374615], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 83, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [16.560116, 61.684288, 42.741585, 28.111706, 33.567223, 31.92355, 59.427544, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2999.73, 2.0, 3.0, 9174.0, 1029.11, 1.0, 462.66626, 0.005384629, -0.036011208, -0.0668297, 0.021098515, -0.021381384, 0.042793967, -0.05688904, 0.0077374615], "split_indices": [34, 48, 48, 90, 58, 56, 51, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3441.3977, 2024.954, 1416.4437, 1749.4482, 275.50574, 517.19324, 899.25055, 1567.2327, 182.21559, 222.1052, 53.400543, 94.20507, 422.98816, 176.09062, 723.1599], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.00087854575, -0.11562393, 0.038267132, 0.121045284, -0.28821245, 0.018553853, 0.9084755, 0.02168032, -0.11328484, -0.062742084, -0.0030747775, 0.12580912, 0.00085210736, -0.15174578, 0.12134319], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 84, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [15.400882, 35.659466, 43.83959, 44.49193, 44.054436, 31.090746, 43.72213, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [38070.0, 158.67142, 640.98334, 14.0, 318.84726, 73.06, 1.0, 0.02168032, -0.11328484, -0.062742084, -0.0030747775, 0.12580912, 0.00085210736, -0.15174578, 0.12134319], "split_indices": [89, 94, 94, 54, 85, 50, 97, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3426.692, 871.1598, 2555.532, 367.5037, 503.65607, 2499.9214, 55.61069, 342.1977, 25.306011, 216.70778, 286.9483, 19.073044, 2480.8484, 5.766008, 49.84468], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.000986708, -0.17258808, 0.02566928, -0.0038596597, -0.61547, 0.064840585, -0.16203663, -0.017740784, 0.04270193, -0.07103701, 0.073944874, -0.015029772, 0.012519647, 0.06941004, -0.030228883], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 85, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [15.675414, 34.428215, 21.820004, 25.10021, 16.86858, 31.893833, 61.737713, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [700.96, -1.8369701e-16, 1.0, 312.97, 1472.28, 1.0, 1.0, -0.017740784, 0.04270193, -0.07103701, 0.073944874, -0.015029772, 0.012519647, 0.06941004, -0.030228883], "split_indices": [50, 1, 61, 76, 58, 56, 56, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3424.9475, 459.75308, 2965.1946, 333.64883, 126.104256, 2453.7827, 511.41174, 238.26814, 95.380684, 118.29449, 7.809773, 537.2538, 1916.5289, 71.4307, 439.98105], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0010228928, -0.046198193, 0.10075917, -0.014795116, -0.36678404, 0.44352847, 0.04655745, -0.015774611, 0.007981537, -0.10035064, -0.022995567, 0.025698185, 0.16631174, -0.06869138, 0.00955301], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 86, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [15.699418, 23.801582, 19.479467, 29.155983, 18.146091, 32.15041, 32.659573, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [564.59, 0.33333334, 2.0, 1.0, 349.085, 2458.34, 3209.76, -0.015774611, 0.007981537, -0.10035064, -0.022995567, 0.025698185, 0.16631174, -0.06869138, 0.00955301], "split_indices": [58, 25, 83, 48, 35, 58, 79, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3412.3625, 2363.7664, 1048.5961, 2153.8333, 209.9332, 142.19272, 906.4034, 857.51306, 1296.3202, 36.013336, 173.91986, 124.37865, 17.814072, 55.91587, 850.48755], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.001039383, 0.00308604, -0.10906865, -0.0062337443, 0.45495966, 0.0009895087, -0.04071259, 0.065475985, -0.07669543], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 87, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [15.303693, 14.287032, 0.0, 21.498468, 17.239176, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1244.24, 139.0, -0.10906865, 21.0, 681.795, 0.0009895087, -0.04071259, 0.065475985, -0.07669543], "split_indices": [94, 74, 0, 75, 86, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3402.6616, 3390.821, 11.84054, 3323.2656, 67.55534, 3195.6716, 127.59405, 58.4604, 9.094946], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0010010455, -0.22377186, 0.017402356, -0.54785526, 0.020726833, 0.013364897, 1.1415594, 0.07509176, -0.06883364, -0.08642948, 0.017750712, 0.0018047631, -0.0910499, 0.00221669, 0.2620781], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 88, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [13.951907, 20.588127, 14.2522, 20.897633, 20.82486, 13.571518, 18.549646, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [7.89, 2627.44, 4795.37, 78.75, 1.0, 195.0, 682.946, 0.07509176, -0.06883364, -0.08642948, 0.017750712, 0.0018047631, -0.0910499, 0.00221669, 0.2620781], "split_indices": [58, 50, 95, 50, 56, 46, 94, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3401.1694, 258.6832, 3142.4863, 110.70406, 147.97914, 3132.2485, 10.237718, 10.381346, 100.322716, 21.590187, 126.388954, 3117.428, 14.820654, 6.4054303, 3.832287], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.00097090064, 0.05345753, -0.07560295, 0.14234601, -0.03092866, -0.5248743, -0.013742154, 0.007710161, 0.044062063, -0.058146477, -0.00049760804, -0.07605423, -0.013368166, -0.013617562, 0.010182486], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 89, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [13.804555, 14.745253, 39.82426, 18.594727, 14.412822, 15.886856, 17.852661, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [-1.8369701e-16, 10.0, 75.0, 1988.65, 22045.0, 309.19, 296.1436, 0.007710161, 0.044062063, -0.058146477, -0.00049760804, -0.07605423, -0.013368166, -0.013617562, 0.010182486], "split_indices": [1, 73, 51, 70, 89, 58, 51, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3396.3796, 1964.1951, 1432.1846, 956.24457, 1007.95044, 172.42714, 1259.7574, 785.6504, 170.5942, 44.41267, 963.5378, 107.01404, 65.4131, 611.61975, 648.1377], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0010258682, 0.0027463422, -0.107660525, -0.0023588769, 0.7626812, -0.0046120156, 0.008786048, 0.026730618, 0.23070632], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 90, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [13.767797, 13.121721, 0.0, 13.270396, 16.650381, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1244.24, 3779.77, -0.107660525, 4986.32, 249341.0, -0.0046120156, 0.008786048, 0.026730618, 0.23070632], "split_indices": [94, 86, 0, 42, 80, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3391.6013, 3380.742, 10.859298, 3359.172, 21.569918, 2262.3152, 1096.8568, 17.219639, 4.350279], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0009088083, -0.009021327, 0.46144998, -0.004832343, -0.16956984, 0.7169916, -0.6510912, -0.0008337847, 0.16549695, -0.018335525, 0.09801635, 0.11944393, -0.098269194], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 91, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [12.717734, 23.534012, 17.08853, 19.358723, 0.0, 11.557991, 8.177685, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [88.0, 213.0, 8.0, 194.0, -0.16956984, 2061.1528, 90.0, -0.0008337847, 0.16549695, -0.018335525, 0.09801635, 0.11944393, -0.098269194], "split_indices": [84, 93, 24, 93, 0, 87, 84, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3388.4773, 3331.012, 57.46524, 3323.76, 7.2519317, 47.020542, 10.444697, 3317.748, 6.0121126, 10.703999, 36.316544, 1.1945221, 9.250175], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007864807, 0.1283951, -0.029522689, 0.05106469, 1.1544702, -0.3797706, 0.00236553, 0.007986974, -0.13360113, 0.17485039, -0.036135536, -0.0484388, 0.16384168, 0.053791936, -0.0017714071], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 92, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [12.576313, 48.857155, 30.953354, 23.053778, 39.4945, 49.622684, 27.33545, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [130.86194, 717.59, 157.126, 4495.37, 532.705, 1592.24, 167.55847, 0.007986974, -0.13360113, 0.17485039, -0.036135536, -0.0484388, 0.16384168, 0.053791936, -0.0017714071], "split_indices": [85, 77, 94, 87, 95, 77, 94, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3385.8218, 615.4849, 2770.3367, 573.32587, 42.159084, 230.2667, 2540.07, 562.5852, 10.740644, 30.178854, 11.980229, 219.64108, 10.625608, 90.861626, 2449.2085], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007258276, -0.11908706, 0.030431705, 0.004083755, -0.89411396, 0.87027735, 0.017755657, -0.026490828, 0.016931254, -0.09941985, 0.113913596, -0.00821628, 0.17629698, -0.059434235, 0.0034311458], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 93, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [12.48803, 67.36528, 28.522413, 27.150972, 20.825066, 34.015938, 26.791805, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1415.8575, 866.68, 60.0394, 1.0, 3334.0, 1.0, 191.99, -0.026490828, 0.016931254, -0.09941985, 0.113913596, -0.00821628, 0.17629698, -0.059434235, 0.0034311458], "split_indices": [96, 87, 85, 56, 72, 83, 70, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3384.286, 704.64777, 2679.638, 608.877, 95.77078, 38.837276, 2640.8008, 231.45901, 377.41797, 91.76479, 4.005992, 19.229116, 19.608158, 68.62726, 2572.1736], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007607311, 0.06824524, -0.054409593, 0.55904055, 0.043293253, -0.43872666, -0.015628146, 0.022108536, 0.17642497, -0.023231698, 0.009770293, -0.056965556, 0.041448023, 0.009166087, -0.010816172], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 94, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [12.5078745, 18.07332, 28.32338, 28.696562, 21.127586, 19.694946, 17.161333, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [-0.25881904, 12.046667, 700.96, 10133.35, 59.94, 4.0, 2.0, 0.022108536, 0.17642497, -0.023231698, 0.009770293, -0.056965556, 0.041448023, 0.009166087, -0.010816172], "split_indices": [1, 43, 50, 88, 58, 59, 56, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3376.5935, 1476.793, 1899.8004, 70.41206, 1406.3809, 173.1915, 1726.609, 55.915985, 14.496078, 231.32791, 1175.053, 150.43831, 22.753183, 799.5601, 927.0489], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.00076816714, 0.0026281842, -0.10506121, -0.013032151, 0.23362635, 7.666131e-06, -0.060618103, -0.020404035, 0.07660922], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 95, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [12.030294, 12.169804, 0.0, 24.498316, 49.9315, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1244.24, 546.73, -0.10506121, 499.65, 59870.0, 7.666131e-06, -0.060618103, -0.020404035, 0.07660922], "split_indices": [94, 94, 0, 94, 89, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3372.1775, 3362.2976, 9.879876, 3149.709, 212.58842, 3082.5828, 67.126366, 117.01957, 95.568855], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007766021, 0.019903643, -0.16777676, -0.020584768, 0.58646226, -0.6352555, 0.07518144, 0.0020969945, -0.048454676, -0.14185466, 0.06828896, -0.12257619, -0.041234244, 0.10579424, -0.009874771], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 96, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [11.646147, 68.843704, 42.250362, 54.021812, 39.66871, 16.235264, 42.0502, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 858.7783, 1323.0508, 4.0, 1.0, 2.0, 0.6363636, 0.0020969945, -0.048454676, -0.14185466, 0.06828896, -0.12257619, -0.041234244, 0.10579424, -0.009874771], "split_indices": [48, 43, 51, 56, 48, 59, 57, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3370.2048, 2999.6304, 370.57443, 2800.4624, 199.16792, 126.17841, 244.39603, 2571.1338, 229.32858, 8.509948, 190.65797, 33.345608, 92.8328, 35.985077, 208.41095], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0008496959, -0.015862528, 0.23646605, 0.019718697, -0.2543615, -0.016591562, 0.9104622, -0.014723564, 0.008810333, 0.04140321, -0.050087046, 0.14390717, -0.011255351, 0.19041435, 0.025841067], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 97, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [11.981433, 26.84485, 34.141716, 31.44583, 67.87123, 20.619394, 34.907845, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2882.21, 1.0, 302.655, 1.0, 1.0, 1.0, 866.2037, -0.014723564, 0.008810333, 0.04140321, -0.050087046, 0.14390717, -0.011255351, 0.19041435, 0.025841067], "split_indices": [58, 61, 85, 56, 56, 92, 95, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3360.8909, 3161.805, 199.0858, 2752.1362, 409.66882, 145.45053, 53.63526, 799.3787, 1952.7574, 110.19701, 299.47183, 8.129932, 137.3206, 20.489878, 33.145386], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.00076879153, 1.1587071, -0.0037567995, 0.41848066, 2.3998575, 0.006262751, -0.36773667, 0.11326583, -0.07673655, 0.2950052, 0.06470732, -0.0017971507, 0.02867111, -0.10296855, -0.010795979], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 98, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [11.62526, 6.919818, 12.20719, 6.470597, 0.9948635, 22.147736, 15.309088, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.14, 3.0, 3.0, 17.0, 12713.53, 1846.18, 2.0, 0.11326583, -0.07673655, 0.2950052, 0.06470732, -0.0017971507, 0.02867111, -0.10296855, -0.010795979], "split_indices": [42, 91, 61, 89, 88, 58, 24, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3353.2585, 7.625049, 3345.6335, 5.614007, 2.0110419, 3256.9595, 88.67401, 3.531817, 2.08219, 1.0107149, 1.000327, 2998.7646, 258.19498, 24.156372, 64.51764], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.00064086105, 0.03611914, -0.09407317, -0.13319258, 0.06900515, -0.50787705, 0.01904111, 0.081213586, -0.019163879, 0.026111757, 0.0024962833, -0.06974814, 0.004955002, 0.023111459, -0.010278504], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 99, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [11.523023, 13.415399, 44.349613, 21.810722, 17.057686, 21.531216, 19.269014, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.25881904, 47.71, 133.424, 78.88, 387.505, 224.72, 2.0, 0.081213586, -0.019163879, 0.026111757, 0.0024962833, -0.06974814, 0.004955002, 0.023111459, -0.010278504], "split_indices": [1, 58, 51, 42, 86, 2, 56, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3353.0208, 2406.7302, 946.29047, 390.94122, 2015.789, 202.39233, 743.89813, 22.012249, 368.929, 375.02075, 1640.7682, 150.83557, 51.55677, 271.09033, 472.80777], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "9.126152E-2", "boost_from_average": "1", "num_class": "0", "num_feature": "98", "num_target": "1"}, "objective": {"name": "binary:logistic", "reg_loss_param": {"scale_pos_weight": "1"}}}, "version": [3, 0, 1]}