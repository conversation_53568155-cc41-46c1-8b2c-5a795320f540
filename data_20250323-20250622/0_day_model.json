{"learner": {"attributes": {"scikit_learn": "{\"_estimator_type\": \"classifier\"}"}, "feature_names": ["hour_sin", "hour_cos", "cum_1h_up_to_cur_h_cost", "mean_1h_up_to_cur_h_cost", "cum_1h_up_to_cur_h_reg_pv", "mean_1h_up_to_cur_h_reg_pv", "cum_1h_up_to_cur_h_apply_pv", "mean_1h_up_to_cur_h_apply_pv", "cum_1h_up_to_cur_h_credit_pv", "mean_1h_up_to_cur_h_credit_pv", "cum_2h_up_to_cur_h_cost", "mean_2h_up_to_cur_h_cost", "cum_2h_up_to_cur_h_reg_pv", "mean_2h_up_to_cur_h_reg_pv", "cum_2h_up_to_cur_h_apply_pv", "mean_2h_up_to_cur_h_apply_pv", "cum_2h_up_to_cur_h_credit_pv", "mean_2h_up_to_cur_h_credit_pv", "cum_4h_up_to_cur_h_cost", "mean_4h_up_to_cur_h_cost", "cum_4h_up_to_cur_h_reg_pv", "mean_4h_up_to_cur_h_reg_pv", "cum_4h_up_to_cur_h_apply_pv", "mean_4h_up_to_cur_h_apply_pv", "cum_4h_up_to_cur_h_credit_pv", "mean_4h_up_to_cur_h_credit_pv", "cum_6h_up_to_cur_h_cost", "mean_6h_up_to_cur_h_cost", "cum_6h_up_to_cur_h_reg_pv", "mean_6h_up_to_cur_h_reg_pv", "cum_6h_up_to_cur_h_apply_pv", "mean_6h_up_to_cur_h_apply_pv", "cum_6h_up_to_cur_h_credit_pv", "mean_6h_up_to_cur_h_credit_pv", "cum_8h_up_to_cur_h_cost", "mean_8h_up_to_cur_h_cost", "cum_8h_up_to_cur_h_reg_pv", "mean_8h_up_to_cur_h_reg_pv", "cum_8h_up_to_cur_h_apply_pv", "mean_8h_up_to_cur_h_apply_pv", "cum_8h_up_to_cur_h_credit_pv", "mean_8h_up_to_cur_h_credit_pv", "cum_12h_up_to_cur_h_cost", "mean_12h_up_to_cur_h_cost", "cum_12h_up_to_cur_h_reg_pv", "mean_12h_up_to_cur_h_reg_pv", "cum_12h_up_to_cur_h_apply_pv", "mean_12h_up_to_cur_h_apply_pv", "cum_12h_up_to_cur_h_credit_pv", "mean_12h_up_to_cur_h_credit_pv", "cum_16h_up_to_cur_h_cost", "mean_16h_up_to_cur_h_cost", "cum_16h_up_to_cur_h_reg_pv", "mean_16h_up_to_cur_h_reg_pv", "cum_16h_up_to_cur_h_apply_pv", "mean_16h_up_to_cur_h_apply_pv", "cum_16h_up_to_cur_h_credit_pv", "mean_16h_up_to_cur_h_credit_pv", "prev_h_cost", "prev_h_reg_pv", "prev_h_apply_pv", "prev_h_credit_pv", "prev_h_prev_h_cost_squared", "prev_h_prev_h_cost_cubed", "prev_h_prev_h_reg_pv_squared", "prev_h_prev_h_reg_pv_cubed", "prev_h_prev_h_apply_pv_squared", "prev_h_prev_h_apply_pv_cubed", "prev_h_prev_h_credit_pv_squared", "prev_h_prev_h_credit_pv_cubed", "past_1day_cost", "past_1day_view_count", "past_1day_valid_click_count", "past_1day_reg_pv", "past_1day_apply_pv", "past_1day_credit_pv", "past_1day_reg_cost", "past_1day_apply_cost", "past_1day_credit_cost", "past_3day_cost", "past_3day_view_count", "past_3day_valid_click_count", "past_3day_reg_pv", "past_3day_apply_pv", "past_3day_credit_pv", "past_3day_reg_cost", "past_3day_apply_cost", "past_3day_credit_cost", "past_7day_cost", "past_7day_view_count", "past_7day_valid_click_count", "past_7day_reg_pv", "past_7day_apply_pv", "past_7day_credit_pv", "past_7day_reg_cost", "past_7day_apply_cost", "past_7day_credit_cost", "day_of_week"], "feature_types": ["float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float"], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "100"}, "iteration_indptr": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100], "tree_info": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "trees": [{"base_weights": [-1.7644147e-08, 1.0869964, -2.807866, 1.1739384, -0.28543437, -1.5362213, -0.28643948, 0.122654475, 0.004439143, -0.28348798, -0.042800173], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 0, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [52597.42, 4260.49, 341.32422, 722.2754, 0.0, 296.4888, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 249.09, -0.28543437, -0.70710677, -0.28643948, 0.122654475, 0.004439143, -0.28348798, -0.042800173], "split_indices": [56, 61, 48, 58, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [17230.963, 12422.507, 4808.4556, 12155.08, 267.42676, 205.8527, 4602.603, 11614.092, 540.98816, 94.06515, 111.78756], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.0047067115, 1.0169246, -2.3556895, 1.1005322, -0.23935416, -1.3283983, -0.24000144, 0.11571821, 0.013358866, -0.23809667, -0.038056564], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 1, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [40902.465, 3419.0273, 230.76953, 640.4883, 0.0, 213.35446, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 219.04, -0.23935416, -0.70710677, -0.24000144, 0.11571821, 0.013358866, -0.23809667, -0.038056564], "split_indices": [56, 61, 48, 58, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [17117.477, 11980.431, 5137.0454, 11694.419, 286.01126, 213.76341, 4923.282, 11047.016, 647.4036, 100.56924, 113.194176], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.0070826295, 0.9552344, -2.0631962, 1.035288, -0.20963524, -1.1739686, -0.21012564, 0.10819997, -0.007644837, -0.20868206, -0.03391252], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 2, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [32938.5, 2813.752, 175.92578, 582.5244, 0.0, 165.89377, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 276.63, -0.20963524, -0.70710677, -0.21012564, 0.10819997, -0.007644837, -0.20868206, -0.03391252], "split_indices": [56, 61, 48, 58, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [16778.193, 11508.184, 5270.01, 11214.646, 293.5377, 217.6117, 5052.398, 10762.467, 452.17914, 103.23292, 114.378784], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.008230444, 0.89990664, -1.8578649, 0.97630507, -0.18883641, -1.0527568, -0.18925112, 0.10504546, 0.028918985, -0.18803096, -0.030273272], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 3, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [27108.545, 2350.1787, 144.94336, 546.417, 0.0, 135.72299, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 153.81, -0.18883641, -0.70710677, -0.18925112, 0.10504546, 0.028918985, -0.18803096, -0.030273272], "split_indices": [56, 61, 48, 58, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [16289.65, 11023.033, 5266.617, 10729.689, 293.34296, 218.58372, 5048.0337, 9684.107, 1045.5819, 103.20434, 115.379395], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.008675883, 0.84942013, -1.7057409, 0.9221173, -0.17347962, -0.95379156, -0.17385821, 0.10046237, 0.03304302, -0.17274551, -0.027063733], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 4, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [22641.678, 1981.6333, 125.98047, 500.1455, 0.0, 115.020386, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 129.72, -0.17347962, -0.70710677, -0.17385821, 0.10046237, 0.03304302, -0.17274551, -0.027063733], "split_indices": [56, 61, 48, 58, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [15706.262, 10538.648, 5167.6133, 10250.921, 287.72787, 217.50713, 4950.106, 8995.823, 1255.0972, 101.28008, 116.22704], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.008680817, 0.8027814, -1.5886146, 0.8717782, -0.16170284, -0.87052065, -0.1620673, 0.091220655, -0.031891044, -0.16099738, -0.024223248], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 5, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [19111.875, 1681.7065, 113.77344, 471.3545, 0.0, 99.95479, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 334.56, -0.16170284, -0.70710677, -0.1620673, 0.091220655, -0.031891044, -0.16099738, -0.024223248], "split_indices": [56, 61, 48, 58, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [15065.541, 10063.124, 5002.4175, 9784.768, 278.35638, 214.98575, 4787.4316, 9463.674, 321.09335, 98.0385, 116.94724], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.008378448, 0.7592237, -1.495773, 0.82455075, -0.15241243, -0.7987746, -0.15277646, 0.09711393, 0.048517805, -0.17307924, -0.034235764], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 6, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [16259.722, 1433.7139, 105.6709, 464.01514, 0.0, 89.78952, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 48.74, -0.15241243, 1.0, -0.15277646, 0.09711393, 0.048517805, -0.17307924, -0.034235764], "split_indices": [56, 61, 48, 58, 0, 75, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [14394.964, 9602.21, 4792.7544, 9335.751, 266.45886, 211.46935, 4581.285, 6518.353, 2817.398, 68.597984, 142.87137], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.007905613, 0.71794504, -1.4198811, 0.7796204, -0.14492132, -0.72889984, -0.1452944, 0.086948924, 0.022343619, -0.146201, -0.016310712], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 7, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [13911.6, 1226.0972, 103.06445, 445.2505, 0.0, 86.92027, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 123.71, -0.14492132, -0.70710677, -0.1452944, 0.086948924, 0.022343619, -0.146201, -0.016310712], "split_indices": [56, 61, 48, 58, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [13720.433, 9163.776, 4556.6562, 8910.822, 252.95378, 209.12814, 4347.528, 7670.8027, 1240.02, 90.404724, 118.72341], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.007300988, 0.67899346, -1.357652, 0.7371078, -0.13877483, -0.6731889, -0.13916408, 0.09017607, 0.042983275, -0.13969184, -0.014647915], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 8, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [11963.5205, 1051.3928, 99.18115, 429.9995, 0.0, 78.14279, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 39.92, -0.13877483, -0.70710677, -0.13916408, 0.09017607, 0.042983275, -0.13969184, -0.014647915], "split_indices": [56, 61, 48, 58, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [13046.802, 8744.26, 4302.542, 8505.732, 238.5278, 204.53091, 4098.011, 5536.8735, 2968.8586, 85.45656, 119.07436], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.006599445, 0.6415694, -1.305405, 0.6961714, -0.13365944, -0.6229968, -0.13407071, 0.0759178, -0.010790972, -0.15511452, -0.02151659], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 9, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [10325.679, 903.0742, 96.54443, 411.896, 0.0, 75.634766, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 219.04, -0.13365944, 1.0, -0.13407071, 0.0759178, -0.010790972, -0.15511452, -0.02151659], "split_indices": [56, 61, 48, 58, 0, 75, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [12392.526, 8351.276, 4041.2507, 8127.5835, 223.69255, 199.69484, 3841.556, 7537.0474, 590.53595, 60.104538, 139.5903], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.005831253, 0.60597277, -1.2603498, 0.6571851, -0.12935157, -0.57205564, -0.12979013, 0.083794296, 0.03675015, -0.13135223, -0.0092122285], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 10, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [8937.55, 777.0774, 97.012695, 406.5869, 0.0, 70.1448, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 34.9, -0.12935157, -0.70710677, -0.12979013, 0.083794296, 0.03675015, -0.13135223, -0.0092122285], "split_indices": [56, 61, 48, 58, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [11759.668, 7978.5396, 3781.1282, 7769.7134, 208.82605, 196.45648, 3584.6716, 4783.188, 2986.5251, 76.51256, 119.94393], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.0050288197, 0.57143915, -1.2220168, 0.6193235, -0.12568727, -0.5306044, -0.126158, 0.07962964, 0.03308126, -0.14654471, -0.015684167], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 11, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [7756.3867, 669.24756, 95.74414, 379.67822, 0.0, 67.02804, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 37.39, -0.12568727, 1.0, -0.126158, 0.07962964, 0.03308126, -0.14654471, -0.015684167], "split_indices": [56, 61, 48, 58, 0, 75, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [11158.082, 7634.4956, 3523.5867, 7440.292, 194.20354, 191.4902, 3332.0964, 4610.468, 2829.8247, 53.859028, 137.63116], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.0042051384, 0.538189, -1.1880134, 0.5828575, -0.122543395, -0.48801216, -0.12305116, 0.06248598, -0.06388585, -0.12532796, -0.0049914545], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 12, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [6742.8447, 576.98975, 96.867676, 366.5376, 0.0, 63.364452, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 393.81, -0.122543395, -0.70710677, -0.12305116, 0.06248598, -0.06388585, -0.12532796, -0.0049914545], "split_indices": [56, 61, 48, 58, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [10589.537, 7314.1465, 3275.3909, 7134.125, 180.0214, 188.18651, 3087.2043, 6897.4795, 236.64537, 67.83385, 120.35266], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.003316787, 0.5061354, -1.1585654, 0.5797432, -0.4805594, -0.45301414, -0.12037529, 0.08081301, 0.039161082, -0.011608901, -0.11981132, -0.1403356, -0.011005084], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 13, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [5873.554, 509.91394, 96.270996, 280.27295, 127.402214, 59.94261, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 13.23, 1.0, 1.0, -0.12037529, 0.08081301, 0.039161082, -0.011608901, -0.11981132, -0.1403356, -0.011005084], "split_indices": [56, 60, 48, 58, 61, 75, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [10051.732, 7016.026, 3035.706, 6529.3467, 486.6791, 183.38506, 2852.321, 2947.6697, 3581.677, 323.51596, 163.16316, 47.8108, 135.57425], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.002507614, 0.47512966, -1.1317388, 0.58190066, -0.16831167, -0.41686365, -0.11805699, -0.016159205, 0.0647261, 0.0042969137, -0.12632115, -0.1208293, -0.0017098187], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 14, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [5121.823, 463.4298, 97.60693, 281.20557, 222.2749, 57.317635, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 153.81, 1.0, 6.054375, 14364.03, -0.70710677, -0.11805699, -0.016159205, 0.0647261, 0.0042969137, -0.12632115, -0.1208293, -0.0017098187], "split_indices": [56, 58, 48, 51, 79, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [9552.392, 6743.194, 2809.1982, 5783.5776, 959.61633, 180.22885, 2628.9692, 467.2296, 5316.3477, 805.1993, 154.41707, 59.806126, 120.42273], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.001833717, 0.44404364, -1.108147, 0.63490146, 0.094898425, -0.38694364, -0.11603818, 0.0047786976, 0.07539352, 0.019416114, -0.10116422, -0.13562828, -0.007247839], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 15, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [4470.5854, 433.9568, 97.30176, 294.25366, 253.17267, 53.77677, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 50.84, 1.0, 141.15, 18566.11, 1.0, -0.11603818, 0.0047786976, 0.07539352, 0.019416114, -0.10116422, -0.13562828, -0.007247839], "split_indices": [56, 58, 48, 50, 79, 75, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [9105.944, 6512.144, 2593.8005, 4209.9727, 2302.1711, 175.74187, 2418.0586, 709.90106, 3500.0718, 2113.4111, 188.75998, 42.236103, 133.50577], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.0012560687, 0.41426998, -1.086075, 0.45489833, -0.118068114, -0.35603514, -0.11427231, 0.06821851, 0.02342954, -0.117365554, 0.0008323248], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 16, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [3905.2847, 408.9254, 98.59253, 308.0232, 0.0, 51.795235, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 21.3, -0.118068114, -0.70710677, -0.11427231, 0.06821851, 0.02342954, -0.117365554, 0.0008323248], "split_indices": [56, 61, 48, 58, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [8694.132, 6301.266, 2392.866, 6145.4375, 155.82855, 172.83107, 2220.035, 3025.8342, 3119.6035, 52.59245, 120.238625], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.0004636548, 0.36755362, -0.112721644, 0.44142476, -0.5222485, 0.06809779, 0.026814047, -0.02103241, -0.12060392], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 17, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [3431.9226, 411.34106, 0.0, 239.56592, 102.10455, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.112721644, 12.88, 1.0, 0.06809779, 0.026814047, -0.02103241, -0.12060392], "split_indices": [48, 60, 0, 58, 61, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [8288.467, 6253.469, 2034.9972, 5774.5703, 478.89932, 2422.5867, 3351.9834, 329.77405, 149.12526], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0002698628, 0.34199223, -0.11135522, 0.44248244, -0.26392993, 0.021115048, 0.06405008, -0.00085061364, -0.09648266], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 18, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [3020.142, 369.2505, 0.0, 238.08246, 154.49559, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 170.35, -0.11135522, -0.25881904, 1455.125, 0.021115048, 0.06405008, -0.00085061364, -0.09648266], "split_indices": [48, 58, 0, 1, 78, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [7924.168, 6061.38, 1862.7882, 5199.3535, 862.026, 2398.919, 2800.4346, 632.5291, 229.49689], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.00081146153, 0.31690073, -0.11014742, 0.53612745, 0.05445565, -0.013024474, 0.06755584, 0.014407163, -0.09158942], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 19, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [2660.3057, 339.62622, 0.0, 298.9389, 233.8771, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 34.9, -0.11014742, 141.15, 97.0, -0.013024474, 0.06755584, 0.014407163, -0.09158942], "split_indices": [48, 58, 0, 50, 82, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [7605.527, 5902.4614, 1703.0652, 3215.4644, 2686.9973, 556.3944, 2659.0698, 2460.6018, 226.39561], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0012017315, 0.313972, -1.0207868, 0.34966725, -0.11445705, -0.38226244, -0.10907686, 0.0125654405, 0.05509747, -0.13351296, -0.008888173], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 20, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [2350.9963, 291.35724, 76.778076, 245.96326, 0.0, 48.013138, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, -0.25881904, -0.11445705, 1.0, -0.10907686, 0.0125654405, 0.05509747, -0.13351296, -0.008888173], "split_indices": [56, 61, 48, 1, 0, 75, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [7314.0835, 5587.553, 1726.5303, 5454.8164, 132.73672, 171.1743, 1555.356, 2582.5764, 2872.24, 39.46044, 131.71385], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0018715566, 0.27159163, -0.10812557, 0.38501397, -0.20117895, -0.024375396, 0.045618266, -0.00624179, -0.09880778], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 21, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [2073.6914, 300.6476, 0.0, 202.422, 118.43451, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.10812557, 6.82, 533.89, -0.024375396, 0.045618266, -0.00624179, -0.09880778], "split_indices": [48, 59, 0, 43, 58, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [7023.3755, 5604.2744, 1419.101, 4520.0547, 1084.2201, 459.34695, 4060.7075, 922.6076, 161.61249], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0022958654, 0.2504929, -0.10727828, 0.53795546, 0.065221734, -0.019168256, 0.07061968, 0.026551438, -0.02582126], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 22, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [1833.9868, 291.96155, 0.0, 263.8852, 216.11777, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 15.12, -0.10727828, 149.78, 1351.97, -0.019168256, 0.07061968, 0.026551438, -0.02582126], "split_indices": [48, 58, 0, 50, 87, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [6775.3125, 5481.6265, 1293.6858, 2147.5825, 3334.0442, 402.37997, 1745.2025, 2059.0925, 1274.9517], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0024584245, 0.23075353, -0.10652199, 0.2654408, -1.1697396, 0.03176034, -0.054687154, -0.11272712, -0.15884309], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 23, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [1624.4944, 261.42038, 0.0, 222.45145, 0.5226135, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.10652199, 97.0, 1.0, 0.03176034, -0.054687154, -0.11272712, -0.15884309], "split_indices": [48, 61, 0, 82, 56, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [6552.388, 5373.9204, 1178.468, 5244.827, 129.09293, 4928.926, 315.90158, 120.55704, 8.535889], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0030221154, 0.21248989, -0.10584557, 0.5031699, 0.0472404, -0.02177355, 0.06796627, 0.025821367, -0.020158313], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 24, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [1440.387, 252.57843, 0.0, 242.64456, 176.11221, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 13.23, -0.10584557, 149.78, 9.0, -0.02177355, 0.06796627, 0.025821367, -0.020158313], "split_indices": [48, 58, 0, 50, 82, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [6330.535, 5257.7407, 1072.7944, 1904.902, 3352.8389, 374.59415, 1530.3079, 1814.401, 1538.4377], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0030877064, 0.21558028, -0.9563304, 0.052097395, 0.49065775, -0.4010173, -0.105239406, 0.019668164, -0.023912838, 0.07819539, 0.027588293, -0.11807038, -0.0049014557], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 25, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1281.0548, 224.78479, 60.751953, 132.12195, 116.3638, 46.80095, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, -1.8369701e-16, 1.0, 1502.76, 14.76, -0.70710677, -0.105239406, 0.019668164, -0.023912838, 0.07819539, 0.027588293, -0.11807038, -0.0049014557], "split_indices": [56, 1, 48, 87, 58, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [6143.8193, 4998.0674, 1145.752, 3135.6753, 1862.3921, 169.73607, 976.01587, 2095.6013, 1040.0741, 789.2722, 1073.1199, 52.062756, 117.67331], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.0032484333, 0.1792904, -0.104695104, 0.21169344, -0.11523565, 0.0071138837, 0.046029266], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 26, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [1137.1279, 219.45082, 0.0, 173.29057, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.104695104, -1.8369701e-16, -0.11523565, 0.0071138837, 0.046029266], "split_indices": [48, 61, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5966.6807, 5079.183, 887.4977, 4959.3477, 119.83506, 3168.952, 1790.3959], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.003622793, 0.16435884, -0.10420547, 0.27817813, -0.20124957, -0.035526913, 0.03595593, -0.0027418376, -0.07466968], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 27, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [1011.7775, 207.81541, 0.0, 196.42035, 112.41786, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 112.79, -0.10420547, 6.82, 1455.125, -0.035526913, 0.03595593, -0.0027418376, -0.07466968], "split_indices": [48, 58, 0, 43, 78, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5798.2485, 4991.622, 806.6266, 3806.7605, 1184.8616, 433.00552, 3373.755, 899.30725, 285.5543], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0036902255, 0.15054844, -0.10376411, 0.42718276, 0.009197952, -0.022968901, 0.0609351, 0.0047492846, -0.1135657], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 28, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [901.51556, 192.3493, 0.0, 199.19824, 142.83455, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 12.49, -0.10376411, 149.78, 1.0, -0.022968901, 0.0609351, 0.0047492846, -0.1135657], "split_indices": [48, 58, 0, 50, 61, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5651.0347, 4918.2183, 732.81635, 1662.5167, 3255.7014, 360.90073, 1301.616, 3151.252, 104.449486], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.00379722, 0.13796192, -0.103365436, 0.17974137, -0.71976364, 0.00084024575, 0.035675433, -0.04858821, -0.12960231], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 29, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [804.2103, 173.64807, 0.0, 140.05771, 29.708801, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 457.64, -0.103365436, -0.25881904, 0.75, 0.00084024575, 0.035675433, -0.04858821, -0.12960231], "split_indices": [48, 58, 0, 1, 37, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5506.6274, 4841.117, 665.51056, 4617.0146, 224.10234, 2346.641, 2270.3735, 160.71674, 63.385593], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.003919848, 0.12591727, -0.103004515, 0.15295322, -0.11218752, 0.019955888, -0.053409327], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 30, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [717.8478, 161.52432, 0.0, 150.0368, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.103004515, 101.0, -0.11218752, 0.019955888, -0.053409327], "split_indices": [48, 61, 0, 82, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5386.094, 4781.9097, 604.18463, 4681.356, 100.55366, 4384.635, 296.72128], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0041418136, 0.11497105, -0.10267699, 0.38111576, -0.010378578, -0.023825614, 0.056715887, 0.018157983, -0.020290088], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 31, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [641.4052, 157.3479, 0.0, 174.1472, 118.56501, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 11.55, -0.10267699, 149.78, 8.0, -0.023825614, 0.056715887, 0.018157983, -0.020290088], "split_indices": [48, 58, 0, 50, 82, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5263.731, 4715.384, 548.34717, 1509.1299, 3206.254, 348.52963, 1160.6003, 1605.5066, 1600.7473], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.003938048, 0.10498696, -0.10237898, 0.2701977, -0.07619818, -0.03630005, 0.037169278, 0.0111369705, -0.02918381], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 32, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [573.91064, 139.74509, 0.0, 157.0575, 90.11014, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 39.92, -0.10237898, 6.82, 1053.625, -0.03630005, 0.037169278, 0.0111369705, -0.02918381], "split_indices": [48, 58, 0, 43, 87, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5164.3325, 4666.794, 497.53873, 2440.747, 2226.0469, 336.8241, 2103.923, 1190.7727, 1035.2742], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0037871532, 0.11508453, -0.85806495, 0.017463125, 0.45044523, -0.4151063, -0.10210698, 0.015750326, -0.017421558, 0.0746701, 0.026595015, -0.11939146, -0.005371426], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 33, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [515.20166, 145.79942, 44.322906, 92.651024, 54.683563, 47.33049, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 0.25881904, 1.0, 5705.18, 14.34, -0.70710677, -0.10210698, 0.015750326, -0.017421558, 0.0746701, 0.026595015, -0.11939146, -0.005371426], "split_indices": [56, 1, 48, 88, 58, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5071.4346, 4452.711, 618.7238, 3449.6045, 1003.10626, 167.3914, 451.33243, 1993.4047, 1456.1998, 383.78714, 619.31915, 52.325363, 115.06604], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.0036951469, 0.087342925, -0.1018579, 0.11217608, -0.11089893, 0.00016427589, 0.032867797], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 34, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [460.55048, 136.02364, 0.0, 107.263336, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.1018579, -1.8369701e-16, -0.11089893, 0.00016427589, 0.032867797], "split_indices": [48, 61, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4982.7236, 4573.391, 409.33273, 4481.276, 92.11494, 2967.3345, 1513.9417], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0037855674, 0.07942873, -0.101628914, 0.10197063, -0.10967872, 0.013445869, -0.063380554], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 35, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [412.8967, 120.179596, 0.0, 106.31915, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.101628914, 50.0, -0.10967872, 0.013445869, -0.063380554], "split_indices": [48, 61, 0, 83, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4898.6157, 4527.442, 371.17386, 4443.202, 84.2396, 4256.093, 187.10974], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.003881693, 0.072179034, -0.10141748, 0.10800091, -0.62637115, -0.03713398, 0.015680818, -0.04948701, -0.14060806], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 36, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [370.43912, 112.25285, 0.0, 99.848175, 21.741737, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 457.64, -0.10141748, 5.209375, 227.98833, -0.03713398, 0.015680818, -0.04948701, -0.14060806], "split_indices": [48, 58, 0, 51, 51, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4818.735, 4482.2163, 336.5187, 4264.383, 217.83351, 393.4699, 3870.9128, 187.79572, 30.037794], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0038234175, 0.06551094, -0.10122126, 0.30474123, -0.036542796, -0.023958204, 0.04927511, 0.007067233, -0.028756527], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 37, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [332.61215, 108.6868, 0.0, 136.34804, 84.02053, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 10.91, -0.10122126, 149.78, 1999.282, -0.023958204, 0.04927511, 0.007067233, -0.028756527], "split_indices": [48, 58, 0, 50, 87, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4755.3604, 4450.3037, 305.0569, 1330.1749, 3120.1287, 341.42172, 988.7532, 2186.8247, 933.30414], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.003530822, 0.05965851, -0.10103815, 0.08002405, -0.108483545, 0.014787465, -0.022853909], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 38, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [298.97443, 103.15031, 0.0, 91.00968, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.10103815, 1287.6017, -0.108483545, 0.014787465, -0.022853909], "split_indices": [48, 61, 0, 78, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4697.266, 4420.7627, 276.5036, 4344.387, 76.37553, 3561.7148, 782.6723], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.003543375, 0.05412601, -0.100866176, 0.07261636, -0.107452594, -0.0002468107, 0.034934532], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 39, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [268.76358, 91.59247, 0.0, 89.6665, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.100866176, 0.25881904, -0.107452594, -0.0002468107, 0.034934532], "split_indices": [48, 61, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4634.746, 4384.1484, 250.5975, 4314.403, 69.745865, 3394.397, 920.00586], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0035065576, 0.049048867, -0.10070353, 0.12411119, -0.22044542, -0.018118603, 0.020641565, -0.013598549, -0.07149889], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 40, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [241.74217, 88.135155, 0.0, 85.654305, 39.55104, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.10070353, 141.15, 564.59, -0.018118603, 0.020641565, -0.013598549, -0.07149889], "split_indices": [48, 59, 0, 50, 58, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4581.647, 4354.5474, 227.09926, 3406.323, 948.2246, 723.05334, 2683.2695, 810.99493, 137.2297], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0033335055, 0.06221104, -0.74116963, -0.014933616, 0.3546704, -1.0557636, -0.3402265, 0.013326968, -0.014938784, 0.06656059, 0.018075421, -0.11917321, -0.10039002, -0.12752423, -0.0030113214], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 41, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [219.42174, 94.02046, 46.331573, 65.74241, 46.91523, 0.26293945, 47.580025, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 0.25881904, -0.70710677, 3953.73, 14.34, 1.0, 1.0, 0.013326968, -0.014938784, 0.06656059, 0.018075421, -0.11917321, -0.10039002, -0.12752423, -0.0030113214], "split_indices": [56, 1, 0, 88, 58, 48, 75, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4535.197, 4166.0293, 369.16718, 3297.2363, 868.793, 205.94351, 163.22366, 1568.43, 1728.8064, 310.62177, 558.17126, 50.795765, 155.14775, 39.877556, 123.346115], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0031490081, 0.040884275, -0.10041487, 0.057459824, -0.106382765, 0.015924396, -0.010870202], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 42, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [197.98087, 78.84477, 0.0, 71.724, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.10041487, 1351.97, -0.106382765, 0.015924396, -0.010870202], "split_indices": [48, 61, 0, 87, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4489.742, 4301.482, 188.26004, 4238.8286, 62.65314, 2628.6587, 1610.17], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0031050255, 0.03700815, -0.100269854, 0.24212131, -0.04917556, -0.018278299, 0.04517054, -0.0030519215, -0.105255686], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 43, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [178.3206, 75.59592, 0.0, 112.75248, 56.342205, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 11.55, -0.100269854, 163.89, 1.0, -0.018278299, 0.04517054, -0.0030519215, -0.105255686], "split_indices": [48, 58, 0, 42, 61, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4445.3193, 4274.7393, 170.58012, 1264.1981, 3010.541, 417.632, 846.56616, 2956.598, 53.943016], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0029240889, 0.033608664, -0.10012801, 0.13256443, -0.12878452, 0.015681887, -0.117296614, -0.010104436, -0.075837694], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 44, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [160.71892, 68.33076, 0.0, 83.84152, 28.043964, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.70710677, -0.10012801, 1.0, 3.0, 0.015681887, -0.117296614, -0.010104436, -0.075837694], "split_indices": [48, 0, 0, 56, 59, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4404.6323, 4250.0757, 154.55632, 2640.964, 1609.1117, 2593.6584, 47.30569, 1542.3169, 66.79478], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0028637506, 0.030394103, -0.0999879, 0.07255987, -0.33249393, -0.017304424, 0.015042893, 0.0057183937, -0.055256106], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 45, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [144.87178, 64.720276, 0.0, 72.48636, 37.791485, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 1506.75, -0.0999879, 149.78, 2023.9867, -0.017304424, 0.015042893, 0.0057183937, -0.055256106], "split_indices": [48, 42, 0, 50, 96, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4367.1406, 4227.105, 140.03578, 3787.7837, 439.3214, 911.5314, 2876.2522, 158.82555, 280.49588], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0026642429, 0.02756058, -0.09984818, 0.13739304, -0.109032854, -0.014866984, 0.027287422, 0.007897962, -0.023781827], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 46, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [130.66096, 63.225037, 0.0, 90.57041, 45.502113, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 50.84, -0.09984818, 163.89, 1435.755, -0.014866984, 0.027287422, 0.007897962, -0.023781827], "split_indices": [48, 58, 0, 42, 96, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4339.211, 4212.3315, 126.87939, 2334.8845, 1877.4469, 750.3835, 1584.5011, 763.3813, 1114.0656], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.002408648, 0.025075288, -0.09970746, 0.038824227, -0.104726635, 0.01535279, -0.008744919], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 47, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [117.89682, 61.916298, 0.0, 60.04432, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.09970746, 8.0, -0.104726635, 0.01535279, -0.008744919], "split_indices": [48, 61, 0, 82, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4310.752, 4195.7915, 114.96048, 4143.6284, 52.163383, 2171.1726, 1972.4557], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.002355496, 0.022649894, -0.099564396, -0.082669176, 0.1501339, -0.052532908, -0.0051349127, 0.026105175, -0.01690596], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 48, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [106.369095, 56.10184, 0.0, 31.697931, 66.96687, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.25881904, -0.099564396, 1.9841666, 108.46, -0.052532908, -0.0051349127, 0.026105175, -0.01690596], "split_indices": [48, 1, 0, 43, 58, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4280.682, 4176.518, 104.163925, 2287.2725, 1889.2457, 150.0981, 2137.1743, 1402.179, 487.0668], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0021438343, 0.02057462, -0.09941761, 0.03302791, -0.10393687, 0.005815267, -0.048708886], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 49, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [96.02087, 55.01422, 0.0, 53.83911, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.09941761, 50.0, -0.10393687, 0.005815267, -0.048708886], "split_indices": [48, 61, 0, 83, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4258.5967, 4164.212, 94.38503, 4116.812, 47.399643, 3927.9563, 188.85568], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0021276819, 0.018539503, -0.099265754, 0.20276637, -0.048868466, -0.016607491, 0.040800672, -0.003513464, -0.10289111], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 50, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [86.66899, 51.511597, 0.0, 84.18323, 40.841995, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 9.78, -0.099265754, 163.89, 1.0, -0.016607491, 0.040800672, -0.003513464, -0.10289111], "split_indices": [48, 58, 0, 42, 61, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4231.75, 4146.221, 85.52871, 1110.1523, 3036.069, 396.9596, 713.19275, 2995.1326, 40.936436], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0019764656, 0.01682057, -0.099107414, 0.097344965, -0.1293917, -0.038267534, 0.014261666, -0.021920232, 0.027734129], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 51, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [78.26577, 48.652134, 0.0, 57.960052, 53.671066, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 417.825, -0.099107414, 2.53, 1196.6984, -0.038267534, 0.014261666, -0.021920232, 0.027734129], "split_indices": [48, 77, 0, 51, 95, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4207.727, 4130.2188, 77.50861, 2663.6077, 1466.611, 228.91768, 2434.69, 1201.7205, 264.89053], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0017686656, 0.015301483, -0.09894116, 0.037290704, -0.50722104, -0.0019186055, 0.026655976, -0.04039351, -0.16453604], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 52, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [70.69758, 47.38317, 0.0, 51.23827, 18.912735, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 533.89, -0.09894116, 0.25881904, 545.03, -0.0019186055, 0.026655976, -0.04039351, -0.16453604], "split_indices": [48, 58, 0, 1, 85, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4191.511, 4121.265, 70.24631, 3955.7256, 165.53944, 3174.624, 781.1015, 153.00882, 12.530619], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0016785981, 0.015891109, -0.90108854, 0.026630668, -0.10224928, 0.1886739, -1.107016, -0.0021343792, 0.022694381, 0.054562416, -0.118943386, -0.19342698, -0.07206575], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 53, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [66.004684, 45.715523, 18.16874, 38.9825, 0.0, 7.326199, 20.166405, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 1.0, 3157.04, 0.25881904, -0.10224928, 1.5833334, 1687.0, -0.0021343792, 0.022694381, 0.054562416, -0.118943386, -0.19342698, -0.07206575], "split_indices": [46, 61, 88, 1, 0, 47, 90, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4174.979, 4095.9485, 79.03081, 4054.984, 40.964607, 12.573896, 66.45691, 3272.1953, 782.7886, 10.466894, 2.1070025, 19.881378, 46.575535], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.0016394253, 0.013353293, -0.09869907, 0.12356747, -0.08014237, -0.038385957, 0.017190328, -0.07251891, -0.0047896835], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 54, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [61.51162, 42.273624, 0.0, 46.245735, 46.133545, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 3953.73, -0.09869907, 17.09, 5.554167, -0.038385957, 0.017190328, -0.07251891, -0.0047896835], "split_indices": [48, 88, 0, 50, 43, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4161.8677, 4100.4634, 61.40459, 1881.821, 2218.6423, 163.06201, 1718.759, 104.6053, 2114.0369], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0014820676, 0.03431997, -0.3755737, 0.1175644, -0.09492172, -0.28767887, -1.0501279, 0.015236527, -0.11120887, -0.008395531, -0.1278369, -0.022257501, -0.118865445, -0.026403753, -0.13278103], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 55, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [55.59727, 40.771397, 21.195213, 98.79914, 19.16254, 18.632322, 8.690884, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [231.02, -0.70710677, 6.0, 1.0, 1138.0, 1029.11, 1720.06, 0.015236527, -0.11120887, -0.008395531, -0.1278369, -0.022257501, -0.118865445, -0.026403753, -0.13278103], "split_indices": [2, 0, 46, 56, 91, 58, 96, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4149.181, 3787.6013, 361.58008, 2303.81, 1483.7911, 321.15927, 40.420784, 2241.2556, 62.554504, 1471.229, 12.562198, 300.67944, 20.479843, 11.060505, 29.360281], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0015019565, 0.011066512, -0.09833505, -0.02206346, 0.31127363, 0.003721368, -0.020525562, 0.035180226, -0.19678405], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 56, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [50.951824, 40.554306, 0.0, 39.88839, 38.125576, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 1532.8416, -0.09833505, 671.3063, 0.5625, 0.003721368, -0.020525562, 0.035180226, -0.19678405], "split_indices": [48, 95, 0, 77, 53, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4127.004, 4075.8188, 51.185104, 3671.5625, 404.25632, 2774.5674, 896.99506, 398.02405, 6.2322726], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0013878602, 0.010025539, -0.098118216, 0.01940784, -0.10182376, 0.020329606, -0.0028261053], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 57, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [46.047115, 39.288708, 0.0, 35.371708, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.098118216, 5.13, -0.10182376, 0.020329606, -0.0028261053], "split_indices": [48, 61, 0, 58, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4115.8, 4069.386, 46.413578, 4033.5632, 35.82278, 829.68677, 3203.8765], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0013539896, 0.012227858, -0.77835405, 0.021026134, -0.100970484, 0.020607384, -1.0458183, -0.018574715, 0.005996851, 0.04580421, -0.16017313, -0.18872707, -0.064649515], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 58, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [43.29391, 36.274864, 15.082432, 32.209354, 0.0, 13.973976, 16.528603, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 1.0, 17129.0, 8.440833, -0.100970484, 579.03, 105766.0, -0.018574715, 0.005996851, 0.04580421, -0.16017313, -0.18872707, -0.064649515], "split_indices": [46, 61, 80, 43, 0, 86, 89, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4100.5825, 4031.1035, 69.478836, 3997.6653, 33.438267, 17.657078, 51.82176, 632.9746, 3364.6907, 14.472493, 3.1845841, 15.478772, 36.342987], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.0013509414, 0.008782473, -0.09782206, 0.027301488, -0.4501495, 0.0035720635, -0.10765272, -0.07132732, -0.013832182], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 59, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [40.51558, 34.44158, 0.0, 36.234093, 12.798187, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 3.0, -0.09782206, 10244767.0, 3.0, 0.0035720635, -0.10765272, -0.07132732, -0.013832182], "split_indices": [48, 59, 0, 89, 74, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4091.0178, 4049.9966, 41.021034, 3893.8125, 156.18425, 3865.2986, 28.51385, 84.00906, 72.175186], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0013009065, 0.028395552, -0.3130668, -0.13943169, 0.083541065, -0.25100347, -1.0615371, -0.008401259, -0.095009916, 0.041601468, 0.0012553688, -0.018884672, -0.08921965, -0.03597092, -0.12816033], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 60, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [37.794186, 34.506546, 16.225143, 41.3506, 66.220695, 12.949444, 3.6897335, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [231.02, 149.78, 1029.11, 5720.72, 3.89, 6.0, 117.99062, -0.008401259, -0.095009916, 0.041601468, 0.0012553688, -0.018884672, -0.08921965, -0.03597092, -0.12816033], "split_indices": [2, 50, 58, 79, 58, 46, 51, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4080.216, 3726.1958, 354.0203, 921.1829, 2805.013, 328.14572, 25.874557, 863.2713, 57.9116, 492.67584, 2312.337, 300.32678, 27.818935, 6.8054338, 19.069122], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0011315349, 0.007644475, -0.10039693, -0.021852171, 0.28250977, 0.0023441918, -0.022412946, 0.032264538, -0.16032271], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 61, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [35.84684, 32.74366, 0.0, 33.42334, 30.140038, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 1532.8416, -0.10039693, 1465.91, 0.5625, 0.0023441918, -0.022412946, 0.032264538, -0.16032271], "split_indices": [61, 95, 0, 78, 53, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4071.2092, 4036.874, 34.33502, 3646.4795, 390.3945, 2980.0652, 666.41437, 383.07022, 7.324279], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0010509595, 0.007767157, -0.09743547, 0.00997222, -0.31967607, 0.001696075, -0.099777505], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 62, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [34.873028, 28.486076, 0.0, 28.363945, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [1.0, 191.0, -0.09743547, 1.0, -0.31967607, 0.001696075, -0.099777505], "split_indices": [48, 93, 0, 61, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4061.2925, 4025.8108, 35.481762, 4024.0386, 1.7720878, 3997.3013, 26.737328], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0011363577, 0.006877791, -0.09714755, -0.03321649, 0.18073486, -0.0059923865, 0.022217916, 0.02327358, -0.11571578], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 63, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [31.516485, 28.026424, 0.0, 22.299421, 52.68836, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.25881904, -0.09714755, 1532.8416, 1.0, -0.0059923865, 0.022217916, 0.02327358, -0.11571578], "split_indices": [48, 1, 0, 95, 56, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4050.9563, 4018.7578, 32.1985, 3266.3037, 752.4541, 2957.7678, 308.53595, 725.0972, 27.356884], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.001072858, 0.009291924, -0.6945525, 0.09863683, -0.06498149, -0.31946638, -1.1454825, 0.01204793, -0.0709918, -0.05239005, -0.0031308818, -0.24268852, -0.016077468, 0.034653142, -0.12733454], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 64, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [29.04857, 26.43064, 9.699999, 32.016136, 33.59379, 10.938848, 5.5230103, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 3953.73, 173.57895, 1.0, 7.213333, 16.32, 20.21, 0.01204793, -0.0709918, -0.05239005, -0.0031308818, -0.24268852, -0.016077468, 0.034653142, -0.12733454], "split_indices": [59, 88, 94, 56, 43, 26, 18, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4039.494, 3980.9805, 58.513336, 1806.9899, 2173.9905, 32.87565, 25.637686, 1760.2881, 46.701797, 147.61485, 2026.3757, 1.3013045, 31.574345, 1.8886856, 23.749], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0010361618, 0.0060828263, -0.09922471, 0.034739006, -0.22088984, -0.012469927, 0.009035519, -0.012747963, -0.07194504], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 65, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [28.453363, 26.048145, 0.0, 31.540983, 20.816128, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 1506.75, -0.09922471, 149.78, 397.14066, -0.012469927, 0.009035519, -0.012747963, -0.07194504], "split_indices": [61, 42, 0, 50, 94, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4030.4119, 4002.6582, 27.753677, 3554.7092, 447.9489, 918.9779, 2635.7312, 378.32224, 69.62668], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0009564427, 0.005978084, -0.096653365, 0.1358322, -0.041994512, -0.012728511, 0.030435449, 0.00076122483, -0.01978318], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 66, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [26.946121, 24.897049, 0.0, 47.846413, 22.563843, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 10.91, -0.096653365, 163.89, 4.0, -0.012728511, 0.030435449, 0.00076122483, -0.01978318], "split_indices": [48, 58, 0, 42, 44, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4022.4558, 3994.7603, 27.69553, 1077.1788, 2917.5815, 420.65158, 656.5272, 2213.8203, 703.76117], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.00082924304, 0.005568584, -0.098612465, 0.055239953, -0.11797348, -0.029993746, 0.007985715, -0.14152233, -0.009187744], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 67, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [25.30744, 24.484375, 0.0, 24.905277, 38.604317, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 1999.282, -0.098612465, 17.09, 2028.8434, -0.029993746, 0.007985715, -0.14152233, -0.009187744], "split_indices": [61, 87, 0, 50, 87, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4012.7935, 3987.8933, 24.900274, 2844.7036, 1143.1897, 183.6606, 2661.043, 21.4937, 1121.696], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007777633, 0.005472885, -0.09626055, -0.018321846, 0.25013277, -0.0043221824, 0.022455316, 0.03175243, -0.11168976], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 68, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [24.0967, 23.188084, 0.0, 21.964058, 32.879303, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.5, -0.09626055, 1532.8416, 1.0, -0.0043221824, 0.022455316, 0.03175243, -0.11168976], "split_indices": [48, 1, 0, 95, 56, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4006.1929, 3981.3137, 24.87913, 3629.2693, 352.04443, 3292.535, 336.73453, 336.23645, 15.807971], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007297699, 0.005045205, -0.09800324, 0.07782907, -0.06995752, 0.009501634, -0.06545958, -0.10770618, -0.0039208247], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 69, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [22.622387, 21.714647, 0.0, 25.474575, 60.625507, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 8.0, -0.09800324, 1.0, 133.0, 0.009501634, -0.06545958, -0.10770618, -0.0039208247], "split_indices": [61, 82, 0, 56, 71, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3998.2314, 3975.781, 22.450443, 2017.7175, 1958.0636, 1972.3052, 45.41233, 57.004883, 1901.0587], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.00067866244, 0.010081518, -0.5227013, 0.017124794, -0.7950766, 0.6370851, -0.70072985, -0.028553912, 0.0035609182, -0.14530495, 0.019480316, 0.08899482, -0.06935017, -0.057463106, -0.18392198], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 70, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [22.419073, 22.191015, 17.089956, 21.70456, 22.384775, 4.402524, 9.043789, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [884.66, 10244767.0, 11.37, 18.08, 3.0, 81.18, 545.03, -0.028553912, 0.0035609182, -0.14530495, 0.019480316, 0.08899482, -0.06935017, -0.057463106, -0.18392198], "split_indices": [58, 89, 10, 42, 97, 34, 85, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3989.3098, 3909.702, 79.60796, 3876.7678, 32.934044, 10.250619, 69.35734, 222.30421, 3654.4636, 19.5027, 13.431345, 8.891962, 1.3586571, 63.79604, 5.5613003], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0006720109, 0.008443425, -0.58392644, 0.06814703, -0.07991288, -1.2618245, -0.24435349, 0.00912809, -0.10929494, -0.0004860861, -0.01725785, -0.16712575, 0.07374572, 0.0427715, -0.07294258], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 71, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [21.194983, 20.712048, 13.81312, 63.034042, 11.010523, 17.794956, 14.040575, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, -0.70710677, 1.875, 1.0, 1886.68, 3673.41, 331.555, 0.00912809, -0.10929494, -0.0004860861, -0.01725785, -0.16712575, 0.07374572, 0.0427715, -0.07294258], "split_indices": [46, 0, 37, 56, 96, 87, 19, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3984.6257, 3924.2803, 60.34539, 2341.992, 1582.2885, 19.233385, 41.112007, 2297.1445, 44.847275, 874.8112, 707.47723, 16.10028, 3.1331058, 17.283682, 23.828325], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0006781158, 0.11571484, -0.044340264, -0.1101341, 0.26654136, 0.00025758424, -0.17991918, -0.004400571, -0.14182825, 0.039754562, -0.0023451317, 0.0010706857, -0.07818038, -0.062284876, -0.005031673], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 72, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [20.19584, 36.95789, 17.474022, 37.54982, 24.714317, 17.787285, 41.031544, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [11.55, 163.89, 4.0, 985.8725, 89.51, 45184.29, 4425.83, -0.004400571, -0.14182825, 0.039754562, -0.0023451317, 0.0010706857, -0.07818038, -0.062284876, -0.005031673], "split_indices": [58, 42, 44, 77, 10, 79, 88, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3972.0112, 1083.0925, 2888.9187, 433.79456, 649.298, 2174.5967, 714.322, 413.90485, 19.88972, 446.9964, 202.30159, 2146.8977, 27.698807, 160.83755, 553.4845], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.00050019525, 0.004686864, -0.09732318, 0.0066726557, -0.2449491, 0.0038817867, -0.014608282], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 73, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [20.029812, 19.260689, 0.0, 19.384834, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [1.0, 194.0, -0.09732318, 861.825, -0.2449491, 0.0038817867, -0.014608282], "split_indices": [61, 93, 0, 77, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3967.8408, 3947.7847, 20.05625, 3945.5894, 2.195282, 3260.26, 685.3294], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.00054331, -0.02172732, 0.22754808, 0.16868056, -0.05433522, 0.41524968, -0.29152963, 0.019029027, -0.18955906, -0.045697868, -0.0023680408, -0.011944121, 0.061520513, 0.055894285, -0.09933627], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 74, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [19.159147, 22.535654, 32.952385, 24.035614, 38.232925, 26.576534, 54.321297, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1579.9567, 2.0, 80505.0, 1926.0, 133.0, 238.0, 455.104, 0.019029027, -0.18955906, -0.045697868, -0.0023680408, -0.011944121, 0.061520513, 0.055894285, -0.09933627], "split_indices": [95, 72, 80, 90, 71, 71, 85, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3963.1218, 3627.1543, 335.96756, 529.7285, 3097.4258, 246.89052, 89.07706, 525.1389, 4.589572, 218.15154, 2879.2742, 67.30805, 179.58246, 40.365837, 48.71122], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.00034307543, 0.0046515753, -0.09526523, -0.016193597, 0.22829062, -0.0006652225, -0.047818985, 0.028457507, -0.10715224], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 75, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [18.855303, 18.387663, 0.0, 15.90222, 24.908358, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.5, -0.09526523, 884.66, 1.0, -0.0006652225, -0.047818985, 0.028457507, -0.10715224], "split_indices": [48, 1, 0, 58, 56, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3962.2214, 3942.5388, 19.682627, 3607.2393, 335.2996, 3535.242, 71.99715, 322.1317, 13.167872], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.00037382482, 0.004299832, -0.09664012, 0.009565536, -0.78634584, -3.3276916e-05, 0.06399887, -0.1921738, 0.011280288], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 76, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [17.859913, 16.397715, 0.0, 24.410093, 26.81883, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 1521.0, -0.09664012, 8678.0, 2713.14, -3.3276916e-05, 0.06399887, -0.1921738, 0.011280288], "split_indices": [61, 91, 0, 81, 78, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3953.8682, 3935.822, 18.046026, 3910.7644, 25.057806, 3851.2944, 59.46989, 10.571479, 14.486327], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.00039591364, 0.21994969, -0.019815793, 0.25773415, -0.29884803, 0.05536181, -0.07925481, -0.017414099, 0.04677695, 0.0023316105, 0.057901453, -0.04090226, -0.0030141317], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 77, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [16.91199, 39.5772, 16.234423, 28.864168, 0.0, 26.908348, 32.846054, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [0.44, 300.0, 1435.755, 171.52, -0.29884803, 21.0, 573.0, -0.017414099, 0.04677695, 0.0023316105, 0.057901453, -0.04090226, -0.0030141317], "split_indices": [58, 92, 96, 50, 0, 82, 71, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3950.2258, 319.1134, 3631.1125, 316.30804, 2.80537, 1603.3224, 2027.79, 103.55301, 212.75504, 1511.8472, 91.475204, 261.9078, 1765.8823], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.00029787252, 0.0042247996, -0.09476226, -0.027148541, 0.138156, 0.0041240314, -0.014474678, 0.015270889, -0.37459883], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 78, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [16.921741, 16.5208, 0.0, 25.626432, 42.751987, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 1151.46, -0.09476226, 417.825, 28.0, 0.0041240314, -0.014474678, 0.015270889, -0.37459883], "split_indices": [48, 95, 0, 77, 74, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3947.6475, 3929.881, 17.766312, 3184.6704, 745.2107, 2014.0536, 1170.6167, 743.3861, 1.8245925], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.00034959862, 0.00739541, -0.5413059, -0.109331824, 0.043696657, -0.21231687, -0.9710878, -0.0016263642, -0.04609866, 0.036370628, 0.0003030296, -0.21627593, -0.0056597027, -0.15953474, -0.060129035], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 79, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [16.52326, 16.4802, 7.664768, 30.188145, 38.604088, 9.842921, 4.5479603, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 149.78, 173.57895, 68.0, 1.82, 16.32, 823.51, -0.0016263642, -0.04609866, 0.036370628, 0.0003030296, -0.21627593, -0.0056597027, -0.15953474, -0.060129035], "split_indices": [59, 50, 94, 72, 58, 26, 58, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3941.8154, 3887.1484, 54.66696, 921.6278, 2965.5208, 31.810617, 22.856346, 729.584, 192.04385, 333.468, 2632.0527, 1.3989923, 30.411625, 7.2698746, 15.586472], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.00034311996, 0.0038527332, -0.09590748, -0.29533526, 0.01695774, -0.021599052, -0.1895083, 0.017093176, -0.001661527], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 80, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [15.849407, 15.386186, 0.0, 20.493076, 19.435715, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 1.185, -0.09590748, 14.0, 5.13, -0.021599052, -0.1895083, 0.017093176, -0.001661527], "split_indices": [61, 43, 0, 82, 58, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3938.0767, 3921.9087, 16.168104, 163.67451, 3758.2341, 157.02153, 6.652984, 672.0334, 3086.2007], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.00036006558, 0.018843051, -0.20183982, 0.07788806, -0.06636121, -0.31309128, 0.11234929, 0.009856622, -0.10826626, -0.008047964, 0.057705563, -0.011107143, -0.06724573, 0.05904417, -0.03709084], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 81, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [15.2261305, 18.080896, 12.004955, 51.0479, 13.411139, 18.304474, 21.078379, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [231.02, -0.70710677, 633472.0, 1.0, 235.99333, 309.52933, 25.0, 0.009856622, -0.10826626, -0.008047964, 0.057705563, -0.011107143, -0.06724573, 0.05904417, -0.03709084], "split_indices": [2, 0, 89, 56, 51, 94, 73, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3933.3948, 3591.951, 341.44382, 2121.722, 1470.2289, 252.15984, 89.28398, 2085.479, 36.24304, 1439.5167, 30.712252, 162.2558, 89.904045, 44.770355, 44.513626], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0003277048, 0.0074005127, -0.48586032, -0.011088798, 0.21332994, 0.4722858, -0.6592824, -0.0016278595, 0.0694434, 0.026843807, -0.103300385, 0.08703008, -0.11068858, -0.11214538, -0.0012228662], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 82, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [14.73493, 14.719932, 10.534623, 13.002045, 22.1537, 7.2561216, 15.690378, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 0.5, 36.885, 1709.73, 1.0, 2.0, 2332.0, -0.0016278595, 0.0694434, 0.026843807, -0.103300385, 0.08703008, -0.11068858, -0.11214538, -0.0012228662], "split_indices": [38, 1, 94, 85, 56, 47, 90, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3924.9465, 3864.4211, 60.525196, 3546.9092, 317.512, 9.011867, 51.51333, 3521.9785, 24.93061, 304.8187, 12.693307, 7.556269, 1.4555974, 29.622025, 21.891304], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.00030012548, 0.0035223435, -0.09521111, -0.014498926, 0.20323896, -0.0019752712, 0.10104366, 0.038437452, -0.027164951], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 83, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [14.269423, 14.062753, 0.0, 19.319279, 27.921782, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 1579.9567, -0.09521111, 1709.73, 80505.0, -0.0019752712, 0.10104366, 0.038437452, -0.027164951], "split_indices": [61, 95, 0, 85, 80, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3920.1155, 3905.4275, 14.687881, 3583.042, 322.38553, 3565.7449, 17.297163, 233.50925, 88.87627], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.00022267493, 0.0035589405, -0.09372886, -0.09924215, 0.035909683, -0.0063927746, -0.08358442, 0.036688086, 0.00016208821], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 84, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [13.887832, 12.985107, 0.0, 24.258247, 33.702515, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 149.78, -0.09372886, 985.8725, 9.93, -0.0063927746, -0.08358442, 0.036688086, 0.00016208821], "split_indices": [48, 50, 0, 77, 10, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3917.164, 3902.4116, 14.752338, 933.6094, 2968.8022, 891.93475, 41.674633, 277.78595, 2691.0164], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.00018929107, 0.14466663, -0.023207527, 0.23800077, -0.23716779, -0.37757337, 0.0057758563, -0.0011317406, 0.04324996, -0.0514152, 0.017208535, -0.029048031, -0.2535964, -0.0003410342, 0.082694635], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 85, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [13.05558, 19.189835, 34.70305, 20.949715, 12.099333, 47.284557, 23.569736, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.0, 1.0, 133.0, 14.221818, 0.85, 3.0, 1128.315, -0.0011317406, 0.04324996, -0.0514152, 0.017208535, -0.029048031, -0.2535964, -0.0003410342, 0.082694635], "split_indices": [72, 59, 71, 43, 79, 74, 94, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3913.499, 535.8788, 3377.6204, 430.92242, 104.95634, 254.458, 3123.1624, 189.26064, 241.6618, 62.44022, 42.516117, 245.67918, 8.778802, 3089.5957, 33.56657], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-6.566436e-05, 0.0033836318, -0.09442433, 0.0050646556, -0.19292445, -0.00035281497, 0.040219195], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 86, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [12.747042, 12.679454, 0.0, 13.301121, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [1.0, 191.0, -0.09442433, 8054.0, -0.19292445, -0.00035281497, 0.040219195], "split_indices": [61, 93, 0, 81, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3912.0583, 3898.8115, 13.246831, 3896.42, 2.3917744, 3814.8672, 81.55272], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.00012824924, 0.0045290883, -0.6980494, -0.003956898, 0.5758436, -1.6719089, 0.087524444, 0.00020767274, -0.07354339, 0.0752994, -0.1693789, 0.011623391, -0.1967188, -0.16837013, 0.064473554], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 87, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [12.716989, 18.844507, 19.98746, 16.907614, 24.148685, 6.369011, 16.191118, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1521.0, 8678.0, 2713.14, 46.0, 1843.54, 1755.5155, 1.0, 0.00020767274, -0.07354339, 0.0752994, -0.1693789, 0.011623391, -0.1967188, -0.16837013, 0.064473554], "split_indices": [91, 81, 78, 74, 77, 78, 97, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3910.3994, 3885.4648, 24.934805, 3829.5754, 55.8893, 10.629443, 14.305362, 3799.2344, 30.341019, 52.46269, 3.426611, 1.5924274, 9.037016, 2.9395757, 11.365787], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.00020668923, 0.0032238865, -0.09310507, 0.029639194, -0.11235036, -0.0010922621, 0.02776033, 0.007035274, -0.056823757], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 88, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [12.485898, 11.896609, 0.0, 31.90402, 60.422684, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 439.72, -0.09310507, 355.4225, 2.0, -0.0010922621, 0.02776033, 0.007035274, -0.056823757], "split_indices": [48, 94, 0, 94, 84, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3908.0522, 3894.695, 13.357154, 3170.743, 723.9521, 2725.8123, 444.93076, 517.43036, 206.5217], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.00013906593, 0.0064221136, -0.46307036, 0.18553485, -0.011706919, -0.16024649, -0.8708403, -0.00065529434, 0.060384758, -0.047318604, 0.00095946, -0.18899572, -0.001435549, 0.042013053, -0.1005187], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 89, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [11.86809, 12.512644, 6.60326, 28.463112, 34.406216, 8.189481, 4.4191685, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 173.57895, 18.0, 1.0, 16.32, 20.21, -0.00065529434, 0.060384758, -0.047318604, 0.00095946, -0.18899572, -0.001435549, 0.042013053, -0.1005187], "split_indices": [59, 81, 94, 71, 91, 26, 18, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3905.371, 3851.7659, 53.6054, 353.17764, 3498.5881, 31.560486, 22.044912, 242.71024, 110.46739, 153.43066, 3345.1575, 1.5250171, 30.03547, 1.8773915, 20.16752], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.000112590336, -0.02609574, 0.11326344, 0.027434034, -0.16197947, 0.12722464, -0.31135723, -0.0029262626, 0.020525137, -0.08195378, -0.007126283, 0.025354464, -0.0074531077], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 90, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [11.505382, 23.116362, 33.20141, 22.99522, 53.514965, 18.521908, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1151.46, 1999.282, 16.0, 13855.0, 1.0, 480.156, -0.31135723, -0.0029262626, 0.020525137, -0.08195378, -0.007126283, 0.025354464, -0.0074531077], "split_indices": [95, 87, 75, 71, 97, 85, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3903.5984, 3176.4092, 727.1893, 2279.303, 897.10614, 725.01306, 2.1762626, 1728.8868, 550.41626, 107.786095, 789.32007, 445.70117, 279.31186], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-7.6887874e-05, 0.0030224675, -0.09355664, -0.009341868, 0.23629807, 6.781493e-05, -0.08327439, -0.12917931, 0.030506104], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 91, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [11.317806, 11.2239, 0.0, 30.492292, 20.975636, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 727.18, -0.09355664, 677.07, 233.30461, 6.781493e-05, -0.08327439, -0.12917931, 0.030506104], "split_indices": [61, 76, 0, 76, 85, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3901.505, 3889.615, 11.889852, 3694.7422, 194.8729, 3651.3083, 43.433758, 7.6256247, 187.24728], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-9.886486e-05, 0.0029963716, -0.092388056, -0.22804452, 0.015680669, -0.01881144, -0.2520584, 0.007776193, -0.005305273], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 92, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [11.15521, 11.398743, 0.0, 18.056334, 15.736433, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 1.583, -0.092388056, 1030.15, 37.4, -0.01881144, -0.2520584, 0.007776193, -0.005305273], "split_indices": [48, 43, 0, 77, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3899.3787, 3887.3506, 12.028257, 201.42747, 3685.923, 199.04271, 2.3847558, 1936.6101, 1749.3129], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.00016534586, 0.059054825, -0.046656296, 0.016087687, 0.37825054, -0.12889811, 0.10022544, 0.002744166, -0.23010837, -0.30170867, 0.042106193, -0.08330256, -0.008385415, 0.026683947, -0.014021556], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 93, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [10.729715, 23.500616, 26.385899, 39.833313, 30.821766, 44.333977, 31.453596, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3891.16, 356.082, 11.0, 346.76886, 5.0, 144.17, 97.0, 0.002744166, -0.23010837, -0.30170867, 0.042106193, -0.08330256, -0.008385415, 0.026683947, -0.014021556], "split_indices": [88, 76, 83, 76, 90, 94, 82, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3895.1763, 1712.9502, 2182.226, 1510.6509, 202.29927, 1399.0144, 783.21173, 1504.2683, 6.3826475, 1.6539234, 200.64536, 83.064, 1315.9503, 462.56598, 320.64572], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0001176207, 0.025257671, -0.108204514, -0.010788619, 0.2587836, 0.055297, -0.49111757, -0.014254481, 0.0043102666, 0.06897117, 0.015504747, -0.007329689, 0.04398682, -0.08136532, -0.00059845176], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 94, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [10.685876, 26.563343, 46.403942, 19.421413, 18.755722, 25.748333, 34.693974, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [436.725, 355.4225, 2.0, 192.0, 2608.3, 365.77, 1174.5938, -0.014254481, 0.0043102666, 0.06897117, 0.015504747, -0.007329689, 0.04398682, -0.08136532, -0.00059845176], "split_indices": [94, 94, 84, 50, 88, 87, 86, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3894.0671, 3154.3052, 739.762, 2733.3484, 420.95676, 519.0055, 220.75656, 792.98145, 1940.367, 80.57854, 340.37823, 389.55463, 129.45087, 132.19255, 88.564], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [6.584957e-05, -0.010687358, 0.252379, 0.022639016, -0.19454381, 0.07906546, 1.0124638, -0.00096775463, 0.022912923, -0.00053448783, -0.045927178, 0.016677631, -0.33012173, 0.10499529, 0.02198767], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 95, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [10.57099, 22.901476, 20.899925, 21.11833, 28.723408, 39.32353, 0.6452961, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [795.4033, 439.72, 486.93668, 355.4225, 0.071428575, 4.0, 1.0, -0.00096775463, 0.022912923, -0.00053448783, -0.045927178, 0.016677631, -0.33012173, 0.10499529, 0.02198767], "split_indices": [94, 94, 77, 94, 53, 84, 56, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3894.1746, 3735.913, 158.26137, 3163.3853, 572.5279, 129.7744, 28.486965, 2736.1238, 427.26144, 334.49048, 238.03741, 127.41882, 2.3555741, 26.890348, 1.5966167], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.00012299455, 0.02425907, -0.111901894, 0.008222188, 0.61199206, -1.6635671, -0.06749276, 0.0015263791, -0.23005417, 0.07267473, -0.1184374, -0.012069367, -0.36756048, -0.017863177, 0.03476818], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 96, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [10.532962, 30.203575, 47.47094, 50.772892, 18.406698, 59.069416, 31.086224, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [861.825, 2320.0, 874.0687, 1249230.0, 24543.0, 920.27356, 1487.78, 0.0015263791, -0.23005417, 0.07267473, -0.1184374, -0.012069367, -0.36756048, -0.017863177, 0.03476818], "split_indices": [77, 72, 77, 71, 81, 86, 86, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3893.5513, 3204.0198, 689.53156, 3119.904, 84.11571, 18.171047, 671.36053, 3111.4077, 8.496235, 79.62457, 4.4911313, 10.884538, 7.286509, 530.0421, 141.31842], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [8.055063e-05, -0.023476325, 0.11236379, -0.051824, 0.08864594, 0.15247364, -0.10597791, -0.0044176816, -0.12548712, 0.04203069, -0.0038693342, 0.04080419, 0.00499967], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 97, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [10.296776, 10.231994, 31.900333, 23.579556, 27.456844, 17.097866, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.25881904, 11.0, 1.0, 46.0, 431.26, 14.34, -0.10597791, -0.0044176816, -0.12548712, 0.04203069, -0.0038693342, 0.04080419, 0.00499967], "split_indices": [1, 83, 56, 82, 77, 58, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3890.861, 3216.7766, 674.08453, 2568.041, 648.7355, 652.6222, 21.462322, 2552.8506, 15.190513, 179.33879, 469.3967, 185.93509, 466.68713], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [9.57855e-05, 0.0063113496, -0.41878435, -0.020258272, 0.116922624, 0.32620278, -0.69500136, -0.0011922044, -0.12201899, 0.053988572, 0.001080795, 0.0706095, -0.14806034, -0.12270202, 0.00022593046], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 98, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [10.123984, 11.263117, 11.926396, 30.89795, 33.309067, 12.068233, 15.507536, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 11.0, 17129.0, 46.0, 139.56, 579.03, 230740.0, -0.0011922044, -0.12201899, 0.053988572, 0.001080795, 0.0706095, -0.14806034, -0.12270202, 0.00022593046], "split_indices": [46, 83, 80, 82, 76, 86, 80, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3886.476, 3830.6204, 55.855827, 3089.3525, 741.26776, 15.0590315, 40.7968, 3069.041, 20.311466, 147.85278, 593.415, 12.946212, 2.1128192, 22.709436, 18.08736], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [8.417038e-05, 0.12931718, -0.02014957, 0.14253038, -0.20367503, -0.44042695, -0.00045952125, 0.007940972, 0.063770436, -0.03498833, -0.44371024, -0.00090544665, 0.080946706], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 99, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [10.1657505, 15.352952, 27.816265, 16.328262, 0.0, 52.885178, 22.36666, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [2.0, 5494.0, 80.0, 18.0, -0.20367503, 2562.9534, 1128.315, 0.007940972, 0.063770436, -0.03498833, -0.44371024, -0.00090544665, 0.080946706], "split_indices": [72, 90, 71, 81, 0, 86, 94, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [3885.6777, 525.2854, 3360.3923, 523.0291, 2.2563255, 149.43277, 3210.9595, 464.9247, 58.10439, 147.18585, 2.2469137, 3178.221, 32.73848], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "13", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "6.5096194E-1", "boost_from_average": "1", "num_class": "0", "num_feature": "98", "num_target": "1"}, "objective": {"name": "binary:logistic", "reg_loss_param": {"scale_pos_weight": "1"}}}, "version": [3, 0, 1]}