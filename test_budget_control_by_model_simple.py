#!/usr/bin/env python3
"""
Simple test script for budget_control_by_model.py

Tests the main functions that can be tested without complex mocking:
- Function existence checks
- Data processing functions
- Basic functionality tests
"""

import unittest
import pandas as pd
import numpy as np
import os
import sys
from datetime import datetime, timedelta

# Add the huanbei_budget_control directory to the path
sys.path.append('huanbei_budget_control')

try:
    import budget_control_by_model as bcm
    from budget_control_by_model import (
        aggregate_by_adgroup,
        calculate_daily_totals,
    )
    print("✅ Successfully imported budget_control_by_model")
except ImportError as e:
    print(f"❌ Error importing budget_control_by_model: {e}")
    print("Make sure the huanbei_budget_control directory exists and contains budget_control_by_model.py")
    sys.exit(1)


class TestBudgetControlSimple(unittest.TestCase):
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        # Create sample data with recent dates to pass any date filters
        recent_date = (datetime.now() - timedelta(days=1)).strftime('%Y%m%d')
        self.sample_data = pd.DataFrame({
            'account_id': [1001, 1001, 1002, 1002],
            'adgroup_id': [2001, 2001, 2002, 2002],
            'date': [recent_date, recent_date, recent_date, recent_date],
            'hour': [10, 11, 10, 11],
            'dynamic_creative_id': [3001, 3002, 3003, 3004],
            'cost': [1000, 1500, 2000, 2500],  # Will be converted to 10.00, 15.00, 20.00, 25.00
            'view_count': [100, 150, 200, 250],
            'valid_click_count': [10, 15, 20, 25],
            'reg_pv': [1, 2, 3, 4],
            'apply_pv': [0, 1, 1, 2],
            'credit_pv': [0, 0, 1, 1]
        })
    
    def test_function_existence(self):
        """Test that required functions exist in the module."""
        required_functions = [
            'init_model',
            'check_if_over_budget_by_model',
            'load_and_parse_data',
            'aggregate_by_adgroup',
            'calculate_daily_totals',
            'get_predict_data',
            'prepare_data'
        ]
        
        for func_name in required_functions:
            with self.subTest(function=func_name):
                self.assertTrue(hasattr(bcm, func_name), 
                              f"Function '{func_name}' not found in budget_control_by_model")
                self.assertTrue(callable(getattr(bcm, func_name)), 
                              f"'{func_name}' is not callable")
    
    def test_aggregate_by_adgroup(self):
        """Test adgroup aggregation functionality."""
        result = aggregate_by_adgroup(self.sample_data)
        
        # Check aggregation results
        self.assertIsInstance(result, pd.DataFrame)
        self.assertIn('unique_dynamic_creative_count', result.columns)
        self.assertEqual(len(result), 4)  # Should have 4 rows (one per hour per adgroup)
        
        # Check cost conversion (multiplied by 0.01)
        expected_costs = [10.0, 15.0, 20.0, 25.0]
        actual_costs = sorted(result['cost'].tolist())
        self.assertEqual(actual_costs, expected_costs)
        
        # Check unique creative count
        self.assertTrue(all(result['unique_dynamic_creative_count'] >= 1))
    
    def test_calculate_daily_totals(self):
        """Test daily totals calculation."""
        result = calculate_daily_totals(self.sample_data)
        
        # Check structure
        self.assertIsInstance(result, pd.DataFrame)
        self.assertIn('cur_day_cost', result.columns)
        self.assertIn('cur_day_reg_cost', result.columns)
        self.assertIn('cur_day_apply_cost', result.columns)
        self.assertIn('cur_day_credit_cost', result.columns)
        
        # Check that costs are calculated correctly (avoiding division by zero)
        self.assertTrue(all(result['cur_day_reg_cost'] >= 0))
        self.assertTrue(all(result['cur_day_apply_cost'] >= 0))
        self.assertTrue(all(result['cur_day_credit_cost'] >= 0))
        
        # Check that no NaN values exist in cost calculations
        self.assertFalse(result['cur_day_reg_cost'].isna().any())
        self.assertFalse(result['cur_day_apply_cost'].isna().any())
        self.assertFalse(result['cur_day_credit_cost'].isna().any())
    
    def test_data_processing_pipeline(self):
        """Test the complete data processing pipeline."""
        # Step 1: Aggregate by adgroup
        aggregated = aggregate_by_adgroup(self.sample_data)
        self.assertIsInstance(aggregated, pd.DataFrame)
        self.assertGreater(len(aggregated), 0)
        
        # Step 2: Calculate daily totals
        daily_totals = calculate_daily_totals(aggregated)
        self.assertIsInstance(daily_totals, pd.DataFrame)
        self.assertGreater(len(daily_totals), 0)
        
        # Check that the pipeline preserves essential columns
        essential_columns = ['account_id', 'adgroup_id', 'date']
        for col in essential_columns:
            self.assertIn(col, daily_totals.columns)
    
    def test_cost_conversion(self):
        """Test that cost values are properly converted."""
        result = aggregate_by_adgroup(self.sample_data)
        
        # Original costs: [1000, 1500, 2000, 2500]
        # Expected after conversion (÷100): [10.0, 15.0, 20.0, 25.0]
        original_costs = [1000, 1500, 2000, 2500]
        expected_costs = [cost * 0.01 for cost in original_costs]
        actual_costs = sorted(result['cost'].tolist())
        
        self.assertEqual(actual_costs, expected_costs)
    
    def test_division_by_zero_handling(self):
        """Test that division by zero is handled properly in cost calculations."""
        # Create data with zero PV values
        zero_pv_data = self.sample_data.copy()
        zero_pv_data['reg_pv'] = 0
        zero_pv_data['apply_pv'] = 0
        zero_pv_data['credit_pv'] = 0
        
        result = calculate_daily_totals(zero_pv_data)
        
        # Should not raise errors and should handle division by zero
        self.assertFalse(result['cur_day_reg_cost'].isna().any())
        self.assertFalse(result['cur_day_apply_cost'].isna().any())
        self.assertFalse(result['cur_day_credit_cost'].isna().any())
        
        # When PV is 0, cost should be 0 (not infinity or NaN)
        self.assertTrue(all(result['cur_day_reg_cost'] == 0))
        self.assertTrue(all(result['cur_day_apply_cost'] == 0))
        self.assertTrue(all(result['cur_day_credit_cost'] == 0))
    
    def test_data_types(self):
        """Test that output data types are correct."""
        aggregated = aggregate_by_adgroup(self.sample_data)
        daily_totals = calculate_daily_totals(aggregated)
        
        # Check numeric columns are numeric
        numeric_columns = ['cost', 'cur_day_cost', 'cur_day_reg_cost', 'cur_day_apply_cost', 'cur_day_credit_cost']
        for col in numeric_columns:
            if col in daily_totals.columns:
                self.assertTrue(pd.api.types.is_numeric_dtype(daily_totals[col]), 
                              f"Column '{col}' should be numeric")
        
        # Check ID columns are integers
        id_columns = ['account_id', 'adgroup_id']
        for col in id_columns:
            if col in daily_totals.columns:
                self.assertTrue(pd.api.types.is_integer_dtype(daily_totals[col]) or 
                              pd.api.types.is_object_dtype(daily_totals[col]), 
                              f"Column '{col}' should be integer or object type")


def run_tests():
    """Run all tests with detailed output."""
    print("Running Budget Control Model Simple Tests...")
    print("=" * 60)
    
    # Create test suite
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestBudgetControlSimple)
    
    # Run tests with verbose output
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print(f"\n{'=' * 60}")
    print(f"Test Summary:")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.testsRun > 0:
        success_rate = ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100)
        print(f"Success rate: {success_rate:.1f}%")
    
    if result.failures:
        print(f"\nFailures:")
        for test, traceback in result.failures:
            print(f"- {test}")
            print(f"  {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print(f"\nErrors:")
        for test, traceback in result.errors:
            print(f"- {test}")
            print(f"  {traceback.split('Exception:')[-1].strip()}")
    
    return result.wasSuccessful()


if __name__ == '__main__':
    print("Testing Budget Control Model Functions...")
    print("=" * 60)
    
    success = run_tests()
    
    if success:
        print("\n✅ All tests passed!")
        print("The budget_control_by_model functions are working correctly.")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed!")
        print("Please check the budget_control_by_model implementation.")
        sys.exit(1)
