#!/bin/bash

# Budget Control Pipeline Runner
# This script runs the complete budget control pipeline in the correct order

set -e  # Exit on any error

# Default save directory
DEFAULT_SAVE_DIR="data_$(date +%Y%m%d-%H%M%S)/"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Function to check if file exists
check_file() {
    if [ ! -f "$1" ]; then
        print_error "Required file not found: $1"
        exit 1
    fi
}

# Function to run a Python script with error handling and save directory
run_python_script() {
    local script_name=$1
    local description=$2
    local save_dir=$3

    print_status "Starting: $description"
    if [ -n "$save_dir" ]; then
        print_status "Running: python -c \"import $script_name; ${script_name%.py}.main('$save_dir')\""

        if python -c "import ${script_name%.py}; ${script_name%.py}.main('$save_dir')"; then
            print_success "$description completed successfully"
            echo ""
        else
            print_error "$description failed"
            exit 1
        fi
    else
        print_status "Running: python $script_name"

        if python "$script_name"; then
            print_success "$description completed successfully"
            echo ""
        else
            print_error "$description failed"
            exit 1
        fi
    fi
}

# Main pipeline execution
main() {
    local save_dir=$1

    print_status "=== Budget Control Model Pipeline Started ==="
    if [ -n "$save_dir" ]; then
        print_status "Using save directory: $save_dir"
        # Create directory if it doesn't exist
        mkdir -p "$save_dir"
    fi
    echo ""

    # Check if required input files exist
    print_status "Checking required input files..."
    check_file "samples/广告转化数据20250323-20250622.csv"
    print_success "Input data file found"
    echo ""

    # Step 1: Run data aggregation
    run_python_script "aggregate_adgroup_data.py" "Data Aggregation (Step 1/3)" "$save_dir"

    # Check if aggregation output exists
    output_path="${save_dir}aggregated_adgroup_daily_data.csv"
    if [ -z "$save_dir" ]; then
        output_path="aggregated_adgroup_daily_data.csv"
    fi

    if [ -f "$output_path" ]; then
        print_success "Aggregated daily data created successfully at $output_path"
    else
        print_error "Expected output file '$output_path' not found"
        exit 1
    fi

    # Step 2: Run budget control model training
    run_python_script "budget_control_model.py" "Budget Control Model Training (Step 2/3)" "$save_dir"

    # Check if model outputs exist
    expected_files=(
        "0_day_predict_result_train.csv"
        "aggregated_adgroup_hourly_data.csv"
    )

    for file in "${expected_files[@]}"; do
        file_path="${save_dir}${file}"
        if [ -z "$save_dir" ]; then
            file_path="$file"
        fi

        if [ -f "$file_path" ]; then
            print_success "Model output file created: $file_path"
        else
            print_warning "Expected model output file not found: $file_path"
        fi
    done
    echo ""

    # Step 3: Run evaluation on new data
    run_python_script "test_budget_control_effect.py" "Budget Control Effect Evaluation (Step 3/3)" "$save_dir"
    
    print_status "=== Pipeline Completed Successfully ==="
    print_success "All steps completed without errors"
    
    # Show final output files
    echo ""
    print_status "Generated output files:"
    output_files=(
        "aggregated_adgroup_daily_data.csv"
        "aggregated_adgroup_hourly_data.csv"
        "0_day_predict_result_train.csv"
    )

    for file in "${output_files[@]}"; do
        file_path="${save_dir}${file}"
        if [ -z "$save_dir" ]; then
            file_path="$file"
        fi

        if [ -f "$file_path" ]; then
            size=$(ls -lh "$file_path" | awk '{print $5}')
            print_success "✓ $file_path ($size)"
        else
            print_warning "✗ $file_path (not found)"
        fi
    done

    echo ""
    print_status "Pipeline execution completed at $(date)"
}

# Help function
show_help() {
    echo "Budget Control Pipeline Runner"
    echo ""
    echo "Usage: $0 [OPTIONS] [SAVE_DIR]"
    echo ""
    echo "This script runs the complete budget control pipeline:"
    echo "  1. aggregate_adgroup_data.py    - Aggregates raw advertising data"
    echo "  2. budget_control_model.py      - Trains the budget control model"
    echo "  3. test_budget_control_effect.py - Evaluates the model effects"
    echo ""
    echo "Arguments:"
    echo "  SAVE_DIR       Directory to save output files (default: data_YYYYMMDD-HHMMSS/)"
    echo ""
    echo "Options:"
    echo "  -h, --help     Show this help message"
    echo "  -v, --verbose  Enable verbose output"
    echo ""
    echo "Examples:"
    echo "  $0                           # Use default timestamped directory"
    echo "  $0 my_results/               # Save to my_results/ directory"
    echo "  $0 -v data_experiment1/      # Verbose mode with custom directory"
    echo ""
    echo "Requirements:"
    echo "  - Python 3.x with required packages (pandas, numpy, xgboost, etc.)"
    echo "  - Input file: samples/广告转化数据20250323-20250622.csv"
    echo ""
}

# Parse command line arguments
SAVE_DIR=""
VERBOSE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            VERBOSE=true
            set -x  # Enable verbose mode
            shift
            ;;
        -*)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
        *)
            # This is the save directory argument
            SAVE_DIR="$1"
            shift
            ;;
    esac
done

# Use default save directory if none provided
if [ -z "$SAVE_DIR" ]; then
    SAVE_DIR="$DEFAULT_SAVE_DIR"
fi

# Ensure save directory ends with /
if [[ "$SAVE_DIR" != */ ]]; then
    SAVE_DIR="${SAVE_DIR}/"
fi

# Run the main pipeline
main "$SAVE_DIR"
