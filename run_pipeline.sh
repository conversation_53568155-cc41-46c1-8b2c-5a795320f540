#!/bin/bash

# Budget Control Pipeline Runner
# This script runs the complete budget control pipeline in the correct order

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Function to check if file exists
check_file() {
    if [ ! -f "$1" ]; then
        print_error "Required file not found: $1"
        exit 1
    fi
}

# Function to run a Python script with error handling
run_python_script() {
    local script_name=$1
    local description=$2
    
    print_status "Starting: $description"
    print_status "Running: python $script_name"
    
    if python "$script_name"; then
        print_success "$description completed successfully"
        echo ""
    else
        print_error "$description failed"
        exit 1
    fi
}

# Main pipeline execution
main() {
    print_status "=== Budget Control Model Pipeline Started ==="
    echo ""
    
    # Check if required input files exist
    print_status "Checking required input files..."
    check_file "samples/广告转化数据20250323-20250622.csv"
    print_success "Input data file found"
    echo ""
    
    # Step 1: Run data aggregation
    run_python_script "aggregate_adgroup_data.py" "Data Aggregation (Step 1/3)"
    
    # Check if aggregation output exists
    if [ -f "aggregated_adgroup_daily_data.csv" ]; then
        print_success "Aggregated daily data created successfully"
    else
        print_error "Expected output file 'aggregated_adgroup_daily_data.csv' not found"
        exit 1
    fi
    
    # Step 2: Run budget control model training
    run_python_script "budget_control_model.py" "Budget Control Model Training (Step 2/3)"
    
    # Check if model outputs exist
    expected_files=(
        "0_day_predict_result_train.csv"
        "aggregated_adgroup_hourly_data.csv"
    )
    
    for file in "${expected_files[@]}"; do
        if [ -f "$file" ]; then
            print_success "Model output file created: $file"
        else
            print_warning "Expected model output file not found: $file"
        fi
    done
    echo ""
    
    # Step 3: Run evaluation on new data
    run_python_script "test_budget_control_effect.py" "Budget Control Effect Evaluation (Step 3/3)"
    
    print_status "=== Pipeline Completed Successfully ==="
    print_success "All steps completed without errors"
    
    # Show final output files
    echo ""
    print_status "Generated output files:"
    output_files=(
        "aggregated_adgroup_daily_data.csv"
        "aggregated_adgroup_hourly_data.csv"
        "0_day_predict_result_train.csv"
    )
    
    for file in "${output_files[@]}"; do
        if [ -f "$file" ]; then
            size=$(ls -lh "$file" | awk '{print $5}')
            print_success "✓ $file ($size)"
        else
            print_warning "✗ $file (not found)"
        fi
    done
    
    echo ""
    print_status "Pipeline execution completed at $(date)"
}

# Help function
show_help() {
    echo "Budget Control Pipeline Runner"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "This script runs the complete budget control pipeline:"
    echo "  1. aggregate_adgroup_data.py    - Aggregates raw advertising data"
    echo "  2. budget_control_model.py      - Trains the budget control model"
    echo "  3. test_budget_control_effect.py - Evaluates the model effects"
    echo ""
    echo "Options:"
    echo "  -h, --help     Show this help message"
    echo "  -v, --verbose  Enable verbose output"
    echo ""
    echo "Requirements:"
    echo "  - Python 3.x with required packages (pandas, numpy, xgboost, etc.)"
    echo "  - Input file: samples/广告转化数据20250323-20250622.csv"
    echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            set -x  # Enable verbose mode
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Run the main pipeline
main
