#!/bin/bash

echo "=== Starting Ad Group Data Pipeline ==="
echo "$(date): Starting data aggregation..."

# Run data aggregation
python aggregate_adgroup_data.py
if [ $? -ne 0 ]; then
    echo "Error: aggregate_adgroup_data.py failed"
    exit 1
fi

echo "$(date): Data aggregation completed successfully"
echo "$(date): Starting budget control model training..."

# Run budget control model
python budget_control_model.py
if [ $? -ne 0 ]; then
    echo "Error: budget_control_model.py failed"
    exit 1
fi

echo "$(date): Budget control model training completed successfully"
echo "=== Pipeline completed ==="