{"learner": {"attributes": {"scikit_learn": "{\"_estimator_type\": \"classifier\"}"}, "feature_names": ["hour_sin", "hour_cos", "cum_1h_up_to_cur_h_cost", "mean_1h_up_to_cur_h_cost", "cum_1h_up_to_cur_h_reg_pv", "mean_1h_up_to_cur_h_reg_pv", "cum_1h_up_to_cur_h_apply_pv", "mean_1h_up_to_cur_h_apply_pv", "cum_1h_up_to_cur_h_credit_pv", "mean_1h_up_to_cur_h_credit_pv", "cum_2h_up_to_cur_h_cost", "mean_2h_up_to_cur_h_cost", "cum_2h_up_to_cur_h_reg_pv", "mean_2h_up_to_cur_h_reg_pv", "cum_2h_up_to_cur_h_apply_pv", "mean_2h_up_to_cur_h_apply_pv", "cum_2h_up_to_cur_h_credit_pv", "mean_2h_up_to_cur_h_credit_pv", "cum_4h_up_to_cur_h_cost", "mean_4h_up_to_cur_h_cost", "cum_4h_up_to_cur_h_reg_pv", "mean_4h_up_to_cur_h_reg_pv", "cum_4h_up_to_cur_h_apply_pv", "mean_4h_up_to_cur_h_apply_pv", "cum_4h_up_to_cur_h_credit_pv", "mean_4h_up_to_cur_h_credit_pv", "cum_6h_up_to_cur_h_cost", "mean_6h_up_to_cur_h_cost", "cum_6h_up_to_cur_h_reg_pv", "mean_6h_up_to_cur_h_reg_pv", "cum_6h_up_to_cur_h_apply_pv", "mean_6h_up_to_cur_h_apply_pv", "cum_6h_up_to_cur_h_credit_pv", "mean_6h_up_to_cur_h_credit_pv", "cum_8h_up_to_cur_h_cost", "mean_8h_up_to_cur_h_cost", "cum_8h_up_to_cur_h_reg_pv", "mean_8h_up_to_cur_h_reg_pv", "cum_8h_up_to_cur_h_apply_pv", "mean_8h_up_to_cur_h_apply_pv", "cum_8h_up_to_cur_h_credit_pv", "mean_8h_up_to_cur_h_credit_pv", "cum_12h_up_to_cur_h_cost", "mean_12h_up_to_cur_h_cost", "cum_12h_up_to_cur_h_reg_pv", "mean_12h_up_to_cur_h_reg_pv", "cum_12h_up_to_cur_h_apply_pv", "mean_12h_up_to_cur_h_apply_pv", "cum_12h_up_to_cur_h_credit_pv", "mean_12h_up_to_cur_h_credit_pv", "cum_16h_up_to_cur_h_cost", "mean_16h_up_to_cur_h_cost", "cum_16h_up_to_cur_h_reg_pv", "mean_16h_up_to_cur_h_reg_pv", "cum_16h_up_to_cur_h_apply_pv", "mean_16h_up_to_cur_h_apply_pv", "cum_16h_up_to_cur_h_credit_pv", "mean_16h_up_to_cur_h_credit_pv", "prev_h_cost", "prev_h_reg_pv", "prev_h_apply_pv", "prev_h_credit_pv", "prev_h_prev_h_cost_squared", "prev_h_prev_h_cost_cubed", "prev_h_prev_h_reg_pv_squared", "prev_h_prev_h_reg_pv_cubed", "prev_h_prev_h_apply_pv_squared", "prev_h_prev_h_apply_pv_cubed", "prev_h_prev_h_credit_pv_squared", "prev_h_prev_h_credit_pv_cubed", "past_1day_cost", "past_1day_view_count", "past_1day_valid_click_count", "past_1day_reg_pv", "past_1day_apply_pv", "past_1day_credit_pv", "past_1day_reg_cost", "past_1day_apply_cost", "past_1day_credit_cost", "past_3day_cost", "past_3day_view_count", "past_3day_valid_click_count", "past_3day_reg_pv", "past_3day_apply_pv", "past_3day_credit_pv", "past_3day_reg_cost", "past_3day_apply_cost", "past_3day_credit_cost", "past_7day_cost", "past_7day_view_count", "past_7day_valid_click_count", "past_7day_reg_pv", "past_7day_apply_pv", "past_7day_credit_pv", "past_7day_reg_cost", "past_7day_apply_cost", "past_7day_credit_cost", "day_of_week"], "feature_types": ["float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float"], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "100"}, "iteration_indptr": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100], "tree_info": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "trees": [{"base_weights": [7.346561e-08, -0.7967725, 3.640968, -0.9293636, 1.642525, 5.0024233, 2.004903, -0.09784623, 0.053006917, 0.102341704, 0.30982265, 0.11156108, 0.6607379, 0.015458772, 0.29546478], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 0, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [23384.352, 2140.5132, 3213.6406, 450.0298, 304.43182, 4916.125, 1157.6313, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2307.07, 344.49, 0.18181819, 1864.39, 855.01, 1.0, 585.32166, -0.09784623, 0.053006917, 0.102341704, 0.30982265, 0.11156108, 0.6607379, 0.015458772, 0.29546478], "split_indices": [50, 58, 49, 50, 58, 56, 43, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [8058.7246, 6612.465, 1446.26, 6272.153, 340.31177, 788.2546, 658.00543, 6068.3296, 203.82309, 239.96008, 100.35168, 230.86084, 557.39374, 223.58145, 434.42395], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.019041412, -0.80839986, 2.296348, -0.9578858, 0.9505821, 3.077081, 1.622449, -0.101217486, -0.047911722, 0.042279016, 0.19856213, 0.08066746, 0.4177954, 0.0056793126, 0.22902922], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 1, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [14877.066, 1597.0732, 1083.8486, 144.9502, 259.18164, 2390.1895, 1163.4968, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2070.97, 266.74, 2.0, 124.65, 650.3, 1.0, 4925.14, -0.101217486, -0.047911722, 0.042279016, 0.19856213, 0.08066746, 0.4177954, 0.0056793126, 0.22902922], "split_indices": [50, 58, 48, 58, 58, 56, 50, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [8137.883, 6069.3735, 2068.509, 5594.393, 474.98047, 956.64276, 1111.8663, 5023.6333, 570.76, 315.50513, 159.47534, 312.95227, 643.6905, 332.78586, 779.0805], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.026892764, -0.79821736, 1.7202255, -0.94960886, 0.7679145, 2.0599647, 0.7455859, -0.1000286, -0.047697883, 0.029037917, 0.1523336, 0.15694346, 0.3419814, 0.010952762, 0.20837195], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 2, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [10907.219, 1332.0195, 819.2881, 122.16504, 178.2976, 1220.3955, 546.464, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1962.45, 266.74, 0.26666668, 130.38, 611.19, 4999.78, 4655003.0, -0.1000286, -0.047697883, 0.029037917, 0.1523336, 0.15694346, 0.3419814, 0.010952762, 0.20837195], "split_indices": [50, 58, 57, 58, 58, 42, 89, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [8091.82, 5613.9116, 2477.9084, 5119.4307, 494.48077, 1836.5957, 641.3127, 4622.6567, 496.77417, 303.81207, 190.66867, 1351.3029, 485.29288, 435.42355, 205.8891], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.031024685, -0.7876935, 1.3618386, -0.9481346, 0.49960464, 0.338649, 1.5578047, -0.100968674, -0.07313768, 0.0064779245, 0.11863978, -0.036084253, 0.25412908, 0.26074335, 0.09827247], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 3, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [8437.305, 1071.8611, 564.5254, 60.80664, 171.798, 700.79767, 1424.8926, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1660.72, 226.07, 1.0, 64.64, 4237.92, 241.11, 2.0, -0.100968674, -0.07313768, 0.0064779245, 0.11863978, -0.036084253, 0.25412908, 0.26074335, 0.09827247], "split_indices": [42, 58, 56, 58, 79, 58, 56, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [8003.4985, 5186.424, 2817.0747, 4611.889, 574.5347, 453.25284, 2363.822, 3589.0576, 1022.8316, 352.47107, 222.06366, 344.64145, 108.61139, 835.4449, 1528.3771], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.033354547, -0.7811923, 1.1139245, -0.90925026, 0.69040364, 0.39656448, 1.3500974, -0.097704925, -0.043748435, 0.045335438, 0.19166453, -0.050694104, 0.08597439, 0.18061893, 0.06301633], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 4, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [6759.019, 899.36523, 526.14624, 139.91602, 110.273315, 323.24988, 766.66406, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1726.53, 344.49, 2524.15, 130.38, 1426.54, 31.99, 0.2, -0.097704925, -0.043748435, 0.045335438, 0.19166453, -0.050694104, 0.08597439, 0.18061893, 0.06301633], "split_indices": [50, 58, 42, 58, 58, 58, 57, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [7875.826, 4768.1157, 3107.7104, 4386.763, 381.35294, 770.3872, 2337.323, 3834.6084, 552.15466, 320.7226, 60.63035, 261.05777, 509.32947, 1429.9281, 907.39496], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.034426216, -0.75658566, 0.94534665, -0.92516196, 0.34776255, 0.15638147, 1.1007622, -0.0993657, -0.0693524, -0.002996836, 0.090378836, -0.06202174, 0.12825458, 0.1827154, 0.06760975], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 5, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [5474.2964, 829.5095, 402.3733, 60.624023, 124.06556, 474.32214, 844.7532, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1726.53, 226.07, 1.0, 64.64, 546.95, 127.47, 2.0, -0.0993657, -0.0693524, -0.002996836, 0.090378836, -0.06202174, 0.12825458, 0.1827154, 0.06760975], "split_indices": [50, 58, 56, 58, 58, 58, 56, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [7734.9395, 4453.0015, 3281.938, 3863.4211, 589.5804, 540.4346, 2741.5034, 2979.1897, 884.2314, 351.64185, 237.93852, 320.12607, 220.30853, 1010.2176, 1731.2859], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.03482462, -0.75615406, 0.7903565, -0.9202452, 0.26336262, 0.23821987, 1.0127935, -0.098250836, -0.07121023, -0.015963541, 0.07094419, -0.047390398, 0.061964374, 0.1331854, 0.038062517], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 6, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [4509.2197, 676.46826, 433.7246, 44.425537, 106.00011, 276.2681, 507.46313, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1547.43, 226.07, 2599.7, 64.64, 468.47, 39.24, 4.0, -0.098250836, -0.07121023, -0.015963541, 0.07094419, -0.047390398, 0.061964374, 0.1331854, 0.038062517], "split_indices": [50, 58, 42, 58, 58, 58, 48, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [7573.6206, 4041.148, 3532.473, 3480.9814, 560.1664, 1015.0274, 2517.4453, 2676.594, 804.38763, 287.85132, 272.31506, 353.95184, 661.07556, 1672.3103, 845.1351], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.034696028, -0.7490911, 0.6715282, -0.9058859, 0.1610703, 0.22005935, 0.90025014, -0.09703477, -0.068937674, 0.0021098356, 0.14006679, 0.04863068, -0.0798668, 0.12108381, 0.03733057], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 7, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [3744.6118, 526.69385, 385.2135, 43.260742, 94.197174, 341.16336, 404.94385, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1433.8, 226.07, 2979.97, 66.02, 1601.78, 2.0, 4.0, -0.09703477, -0.068937674, 0.0021098356, 0.14006679, 0.04863068, -0.0798668, 0.12108381, 0.03733057], "split_indices": [50, 58, 50, 58, 58, 56, 56, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [7420.0845, 3688.6733, 3731.411, 3146.6077, 542.06573, 1255.3893, 2476.0217, 2422.0142, 724.5934, 487.98123, 54.08448, 995.6863, 259.703, 1557.0048, 919.01697], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.03417042, -0.74626654, 0.5716414, -0.85265225, 0.4377383, -0.090460114, 0.74043965, -0.09282914, -0.044347517, 0.029148942, 0.15307848, -0.06574557, 0.08572125, 0.13173617, 0.039287463], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 8, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [3137.1323, 421.43262, 439.2102, 94.52734, 43.714893, 429.95715, 627.3351, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1182.24, 425.58, 1.0, 130.38, 2066.13, 154.57, 2.0, -0.09282914, -0.044347517, 0.029148942, 0.15307848, -0.06574557, 0.08572125, 0.13173617, 0.039287463], "split_indices": [42, 58, 56, 58, 58, 58, 56, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [7270.055, 3341.7695, 3928.2854, 3066.5154, 275.25403, 798.13025, 3130.1553, 2587.019, 479.49634, 243.88857, 31.365454, 499.55258, 298.57767, 1175.7467, 1954.4086], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.033493523, -0.701576, 0.52367204, -0.87377346, 0.11993131, 0.40451732, 1.2541379, -0.094689004, -0.06411516, -0.008776575, 0.07893516, 0.053605855, -0.01949561, 0.03316952, 0.16044506], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 9, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [2647.1265, 457.5681, 336.99866, 44.86621, 78.105705, 263.11267, 175.04633, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1433.8, 219.89, 741986.0, 64.64, 7069.12, 0.26666668, 2200.4836, -0.094689004, -0.06411516, -0.008776575, 0.07893516, 0.053605855, -0.01949561, 0.03316952, 0.16044506], "split_indices": [50, 58, 71, 58, 79, 57, 96, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [7109.499, 3232.8342, 3876.665, 2672.5688, 560.26526, 3334.3193, 542.3456, 2031.0952, 641.47363, 428.2603, 132.00491, 2734.4175, 599.9018, 149.81085, 392.53473], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.032545503, -0.69909143, 0.44935563, -0.8825028, -0.029927649, -0.04795298, 0.6210997, -0.09581169, -0.0712788, -0.023215836, 0.07081044, -0.07791572, 0.041007, 0.07115794, -0.037744954], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 10, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [2243.6055, 359.69092, 346.2798, 28.83728, 94.425125, 349.1517, 272.52515, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1182.24, 162.55, 2509.22, -0.70710677, 855.01, 58.97, 2.0, -0.09581169, -0.0712788, -0.023215836, 0.07081044, -0.07791572, 0.041007, 0.07115794, -0.037744954], "split_indices": [42, 58, 50, 0, 58, 58, 61, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [6982.894, 2929.9106, 4052.9832, 2299.3594, 630.55115, 1040.5752, 3012.408, 1587.4486, 711.9109, 495.4709, 135.08026, 400.50058, 640.0746, 2762.392, 250.0161], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.031614125, -0.6522425, 0.4140415, -0.8313517, -0.055829518, 0.23837261, 0.83730143, -0.08715478, 0.023219606, -0.022630978, 0.08978547, 0.049514145, -0.011422317, 0.10955372, 0.0046157255], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 11, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1895.3605, 305.9027, 296.36975, 94.36206, 107.9216, 255.38017, 238.87811, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [961.33, 147.32, 27803.34, 2586.1, 76897.0, 2.0, 7.0, -0.08715478, 0.023219606, -0.022630978, 0.08978547, 0.049514145, -0.011422317, 0.10955372, 0.0046157255], "split_indices": [34, 58, 79, 50, 71, 48, 40, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [6850.7095, 2863.0745, 3987.6348, 2201.536, 661.5387, 2819.1458, 1168.489, 2121.5342, 80.001595, 561.863, 99.67573, 1631.0044, 1188.1415, 880.6522, 287.83673], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.030381553, -0.57847565, 0.42049494, -0.7828157, 0.31851155, 0.5648654, -0.21185929, -0.08099302, 0.089619435, 0.0027566303, 0.10431494, 0.008223071, 0.08004119, -0.043340903, 0.05289932], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 12, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1664.4459, 557.4329, 337.48688, 113.45447, 119.03371, 341.98608, 113.088684, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 241.11, 1.0, 1.0, 12478.3, 2524.15, 986967.0, -0.08099302, 0.089619435, 0.0027566303, 0.10431494, 0.008223071, 0.08004119, -0.043340903, 0.05289932], "split_indices": [56, 58, 61, 61, 79, 42, 71, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [6733.317, 3038.895, 3694.4219, 2475.1626, 563.7323, 3007.8235, 686.5983, 2436.3357, 38.82699, 402.97437, 160.75793, 986.9379, 2020.8856, 528.8595, 157.73888], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.029219078, -0.6517281, 0.3159696, -0.77480584, 0.3364642, -0.20552129, 0.45327243, -0.09089848, -0.04953015, 0.043363985, -0.06989245, -0.08120871, 0.018143704, 0.05291645, -0.03986649], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 13, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1420.6771, 287.09302, 304.62393, 78.21167, 26.653397, 208.36339, 217.90497, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1183.28, 445.36, 36.81, 66.02, 2.0, 2509.22, 2.0, -0.09089848, -0.04953015, 0.043363985, -0.06989245, -0.08120871, 0.018143704, 0.05291645, -0.03986649], "split_indices": [50, 58, 58, 58, 40, 50, 61, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [6609.4385, 2357.3442, 4252.0938, 2096.4507, 260.89362, 886.09894, 3365.995, 1414.9415, 681.50916, 239.03987, 21.853735, 344.69037, 541.4085, 3091.0205, 274.97464], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.028105631, -0.5340012, 0.34543726, -0.8189238, 0.07071856, 0.72693306, 0.08863791, 0.03789675, -0.08326986, -0.0117282, 0.11949872, 0.02557822, 0.12003782, -0.06486879, 0.04720324], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 14, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1228.9796, 476.002, 366.44714, 31.299316, 187.32349, 335.40442, 632.7161, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 124.65, 2.0, 2.73, 1.0, 207.4025, 4925.14, 0.03789675, -0.08326986, -0.0117282, 0.11949872, 0.02557822, 0.12003782, -0.06486879, 0.04720324], "split_indices": [56, 58, 56, 50, 61, 43, 50, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [6501.467, 2761.326, 3740.1406, 1876.7251, 884.6008, 1503.9994, 2236.1414, 21.034006, 1855.691, 758.63696, 125.96384, 754.59814, 749.40125, 764.7394, 1471.4019], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.02688724, -0.60741204, 0.26237747, -0.8249986, -0.06492363, -0.040686935, 0.4575475, -0.09210461, -0.060556084, -0.025171125, 0.04940107, 0.008814658, -0.08913378, 0.00860976, 0.06446095], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 15, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1069.3544, 249.92719, 251.41937, 31.298096, 63.532494, 182.6436, 179.59125, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1183.28, 150.62, 2118.3237, -0.70710677, 49.0, 5.0, 200.6475, -0.09210461, -0.060556084, -0.025171125, 0.04940107, 0.008814658, -0.08913378, 0.00860976, 0.06446095], "split_indices": [50, 58, 96, 0, 91, 40, 51, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [6366.085, 2116.7944, 4249.2905, 1510.4482, 606.34607, 1664.8558, 2584.4346, 1048.378, 462.07025, 454.88116, 151.46492, 1446.6526, 218.20332, 866.092, 1718.3427], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.025605913, -0.6164701, 0.2213394, -0.7303641, 0.20983109, -0.30070764, 0.3370748, -0.083169684, -0.031874996, 0.009573749, 0.11421587, -0.08440077, 0.008764093, 0.05392883, 0.0071263625], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 16, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [915.40076, 174.17944, 267.4318, 67.5296, 23.801306, 169.59106, 194.65424, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [996.74, 468.47, 28.65, 4156.48, 2421.57, 2509.22, 0.13333334, -0.083169684, -0.031874996, 0.009573749, 0.11421587, -0.08440077, 0.008764093, 0.05392883, 0.0071263625], "split_indices": [50, 58, 58, 79, 58, 50, 57, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [6271.7505, 1848.1646, 4423.586, 1624.3826, 223.78206, 802.4374, 3621.1484, 1302.6665, 321.71606, 200.36507, 23.416986, 333.99902, 468.43835, 2056.0205, 1565.128], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.024368046, -0.47538224, 0.2719182, -0.77121437, 0.052012414, 0.6332059, 0.036695436, 0.054210585, -0.078747, -0.011523367, 0.097520225, 0.023702106, 0.10447182, -0.060471542, 0.038394164], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 17, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [825.8824, 382.3454, 316.97012, 33.94165, 136.21416, 239.54797, 503.65225, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 124.65, 2.0, 2.73, 1.0, 207.4025, 5135.39, 0.054210585, -0.078747, -0.011523367, 0.097520225, 0.023702106, 0.10447182, -0.060471542, 0.038394164], "split_indices": [56, 58, 56, 50, 61, 43, 50, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [6178.4316, 2449.3564, 3729.075, 1568.8674, 880.489, 1469.8239, 2259.251, 18.785717, 1550.0818, 746.1846, 134.30444, 749.661, 720.1629, 793.2568, 1465.9943], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.023135114, -0.5724857, 0.18613513, -0.762281, -0.08185273, -0.17556721, 0.31557125, -0.056810226, -0.09336683, -0.0193042, 0.065724716, -0.067771845, 0.012243159, 0.039240476, -0.04763518], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 18, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [696.75806, 155.62274, 205.53317, 39.58551, 38.543236, 173.2638, 196.87906, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [996.74, 181.22, 64.64, -0.25881904, 1426.54, 2509.22, 2.0, -0.056810226, -0.09336683, -0.0193042, 0.065724716, -0.067771845, 0.012243159, 0.039240476, -0.04763518], "split_indices": [50, 58, 58, 1, 58, 50, 61, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [6058.803, 1670.8795, 4387.9233, 1204.4133, 466.4662, 1156.3129, 3231.6106, 566.68463, 637.7287, 406.1103, 60.355907, 430.16946, 726.1434, 2946.2644, 285.3463], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.021987593, -0.4323499, 0.22827919, -0.7309301, 0.037270416, 0.5724434, 0.009099808, -0.07488083, 0.07027728, -0.01847494, 0.06267392, 0.06912268, -0.056618184, -0.060952462, 0.03029266], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 19, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [614.15875, 317.6852, 280.21008, 36.025757, 115.47704, 195.86334, 412.88193, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 124.65, 2.0, 1.0, 12478.3, 1.0, 4925.14, -0.07488083, 0.07027728, -0.01847494, 0.06267392, 0.06912268, -0.056618184, -0.060952462, 0.03029266], "split_indices": [56, 58, 56, 61, 79, 61, 50, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5978.1597, 2264.4365, 3713.723, 1383.9669, 880.46967, 1444.2664, 2269.4565, 1367.3933, 16.57362, 640.0757, 240.39401, 1308.1869, 136.07944, 730.4625, 1538.994], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.020945959, -0.5278805, 0.15605043, -0.8126575, -0.20821823, -0.099582955, 0.3209118, 0.08361604, -0.08441525, -0.042469606, 0.03382808, 0.001630625, -0.08931724, 0.022395095, 0.08618349], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 20, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [528.61957, 138.62543, 184.07187, 42.560486, 85.285614, 157.54568, 139.0596, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [996.74, 66.02, 2118.3237, 2.73, 4237.92, 5.0, 986967.0, 0.08361604, -0.08441525, -0.042469606, 0.03382808, 0.001630625, -0.08931724, 0.022395095, 0.08618349], "split_indices": [50, 58, 96, 50, 79, 40, 71, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5889.596, 1523.6719, 4365.924, 804.9898, 718.6821, 1711.8821, 2654.042, 14.609463, 790.3803, 514.9316, 203.75053, 1494.6023, 217.27983, 2251.8164, 402.22562], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.01974289, -0.39314258, 0.1935101, -0.5564729, 0.41508195, 0.4389606, -0.06825403, -0.060432162, 0.07763634, 0.005994852, 0.07090894, 0.018295819, 0.09412982, -0.062389374, 0.027321467], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 21, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [464.16632, 279.94473, 238.41489, 112.91211, 37.210556, 246.08752, 340.99454, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 404.07, 0.13333334, 1.0, 683.96, 285.9075, 466.935, -0.060432162, 0.07763634, 0.005994852, 0.07090894, 0.018295819, 0.09412982, -0.062389374, 0.027321467], "split_indices": [56, 58, 57, 61, 34, 43, 51, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5827.185, 2117.922, 3709.263, 1762.1326, 355.7896, 1913.9329, 1795.3301, 1701.5922, 60.540447, 161.6887, 194.1009, 1268.7261, 645.2068, 683.0463, 1112.2838], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.01858878, -0.48080525, 0.13802923, -0.77656823, 0.044035714, -0.10438325, 0.3129222, 0.0879815, -0.080141656, 0.055422116, -0.058416802, -0.0011942191, -0.0819953, 0.005474992, 0.049897876], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 22, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [415.12085, 225.31467, 181.62396, 38.910828, 168.27213, 118.7814, 119.49992, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [38.5, 2509.22, 2118.3237, 2.73, 2.0, 4.0, 11193.92, 0.0879815, -0.080141656, 0.055422116, -0.058416802, -0.0011942191, -0.0819953, 0.005474992, 0.049897876], "split_indices": [58, 50, 96, 50, 56, 32, 79, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5732.476, 1450.2826, 4282.1934, 927.26337, 523.01917, 1794.8387, 2487.3547, 13.196245, 914.06714, 288.6856, 234.33354, 1590.3993, 204.43945, 1042.322, 1445.0327], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.017536772, -0.39535227, 0.15419927, -0.6178416, -0.09563523, 0.22278386, -0.40165386, -0.068447314, 0.028612753, -0.037875447, 0.012716918, 0.01481951, 0.1498851, -0.08000126, 0.015383469], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 23, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [368.1799, 118.18768, 148.81485, 61.48639, 47.780033, 330.37317, 95.01883, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [428.21, 3686.4, 9.0, 3.0, 763.069, 970.6786, 2503.5752, -0.068447314, 0.028612753, -0.037875447, 0.012716918, 0.01481951, 0.1498851, -0.08000126, 0.015383469], "split_indices": [18, 79, 48, 59, 95, 51, 87, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5672.4463, 1772.2449, 3900.2017, 1016.5596, 755.6853, 3472.3596, 427.842, 946.9991, 69.560486, 332.489, 423.19626, 3281.6562, 190.70345, 248.90443, 178.93759], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.016456552, -0.3812199, 0.14392845, -0.71460533, 0.04618646, 0.19969967, -0.50769264, -0.073479205, 0.13291633, 0.043672185, -0.047347948, 0.0053204508, 0.055658776, -0.07746673, -0.013583685], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 24, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [329.4013, 245.06154, 142.23375, 40.835938, 153.32803, 188.36322, 30.534111, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [66.02, 2374.81, 2.0, 10.0, 2.0, 497030.0, 2503.5752, -0.073479205, 0.13291633, 0.043672185, -0.047347948, 0.0053204508, 0.055658776, -0.07746673, -0.013583685], "split_indices": [58, 50, 61, 75, 56, 80, 87, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5628.6064, 1718.6064, 3910.0002, 965.121, 753.4855, 3602.3726, 307.62775, 956.3157, 8.805255, 430.28247, 323.203, 2554.8208, 1047.5519, 178.436, 129.19174], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.015506081, -0.33685184, 0.15349959, -0.49906152, 0.37629628, 0.27703354, -0.36810833, -0.07370224, -0.028424446, 0.016885247, 0.0922388, 0.0578654, 0.0050441893, -0.05379765, 0.016043194], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 25, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [302.64148, 222.36607, 235.42142, 79.7984, 40.227142, 201.80963, 62.9088, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 50.23, 284.025, 2.0, 986967.0, -0.07370224, -0.028424446, 0.016885247, 0.0922388, 0.0578654, 0.0050441893, -0.05379765, 0.016043194], "split_indices": [56, 60, 61, 58, 11, 56, 71, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5570.6064, 1919.6359, 3650.9705, 1564.1589, 355.47684, 2952.2493, 698.7211, 740.9502, 823.20874, 258.56497, 96.911865, 1265.7852, 1686.4641, 528.76337, 169.95772], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.014595225, -0.41407138, 0.10376803, -0.7121762, 0.027595164, -0.10014637, 0.24773736, 0.019541916, -0.07785998, 0.04551085, -0.050325077, -0.0019607805, -0.07824544, 0.0055698883, 0.042242896], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 26, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [260.5775, 165.86833, 124.83583, 45.492188, 115.64371, 96.70793, 83.586685, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [38.5, 2509.22, 2118.3237, 75.82, 2.0, 5.0, 42.0, 0.019541916, -0.07785998, 0.04551085, -0.050325077, -0.0019607805, -0.07824544, 0.0055698883, 0.042242896], "split_indices": [58, 50, 96, 50, 56, 40, 92, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5509.076, 1258.646, 4250.43, 751.08514, 507.5608, 1759.1377, 2491.2922, 51.088116, 699.997, 281.22263, 226.33817, 1574.3345, 184.80324, 1187.3534, 1303.9388], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.01373083, -0.30700153, 0.13425511, -0.5515358, 0.10640217, 0.24795468, -0.35080564, -0.06853705, -0.023352947, -0.0055558523, 0.069971584, 0.051975917, 0.004437921, -0.05635451, 0.005400017], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 27, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [237.09387, 185.2715, 200.33643, 48.841766, 65.55377, 162.76709, 59.442917, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 3.0, 1.0, 2.0, 797.77, -0.06853705, -0.023352947, -0.0055558523, 0.069971584, 0.051975917, 0.004437921, -0.05635451, 0.005400017], "split_indices": [56, 59, 61, 84, 61, 56, 86, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5461.0503, 1831.133, 3629.917, 1150.3502, 680.7829, 2941.0222, 688.8949, 808.805, 341.54517, 535.5081, 145.27478, 1258.7905, 1682.2317, 451.31903, 237.57587], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.012909696, -0.2928182, 0.13374649, -0.49772662, -0.042086195, 0.20763609, -0.33444887, -0.055264354, 0.06773597, -0.024098914, 0.023191404, 0.0071424455, 0.0749043, -0.0683154, 0.0068338043], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 28, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [222.07559, 95.536804, 122.87933, 66.41641, 45.700855, 226.10246, 68.06349, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [229.64, 9557.69, 4.0, 4.0, 898.15, 534.3575, 2481.9946, -0.055264354, 0.06773597, -0.024098914, 0.023191404, 0.0071424455, 0.0749043, -0.0683154, 0.0068338043], "split_indices": [10, 88, 24, 59, 86, 51, 87, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5407.885, 1858.9292, 3548.9553, 1022.39844, 836.5308, 3065.691, 483.26434, 977.25665, 45.141766, 484.75598, 351.77484, 2450.3447, 615.3461, 258.64835, 224.616], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.012052384, -0.31920874, 0.10709604, -0.6188078, 0.041466787, 0.14864154, -0.49637267, 0.02525341, -0.06809007, 0.040592566, -0.039919168, 0.005163893, 0.060532767, -0.10061171, -0.008843981], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 29, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [196.65068, 162.30757, 97.14136, 44.56018, 109.7534, 160.4685, 51.814034, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [66.02, 2509.22, 9.0, 75.82, 2.0, 724.275, 2446.5625, 0.02525341, -0.06809007, 0.040592566, -0.039919168, 0.005163893, 0.060532767, -0.10061171, -0.008843981], "split_indices": [58, 50, 32, 50, 56, 43, 96, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5371.4473, 1500.8029, 3870.6445, 819.42377, 681.379, 3622.0405, 248.6039, 54.304695, 765.1191, 372.9769, 308.40213, 2988.4011, 633.6395, 109.859436, 138.74445], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.011356269, -0.41375926, 0.07089817, -0.5548173, 0.11212366, 0.17664717, -0.13091086, -0.06565828, 0.022085434, 0.43112049, 0.0043167155, 0.0027988637, 0.07685273, -0.07124885, 0.013205722], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 30, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [176.54315, 67.21123, 94.53472, 56.500397, 55.49088, 255.70103, 233.05978, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [701.73, 10940.78, 3.0, 650.3, 2.63, 360.86835, 7404.5, -0.06565828, 0.022085434, 0.43112049, 0.0043167155, 0.0027988637, 0.07685273, -0.07124885, 0.013205722], "split_indices": [50, 88, 56, 58, 70, 43, 50, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5331.8354, 904.2173, 4427.618, 712.93195, 191.28537, 2905.333, 1522.2853, 630.38153, 82.55038, 2.0965424, 189.18883, 2322.9424, 582.3905, 473.4651, 1048.8202], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.010590846, -0.42961448, 0.06347669, -0.7136603, -0.03008449, -0.10992509, 0.1851471, 0.040728427, -0.080989294, -0.09478597, 0.01929238, -0.004792202, -0.081715085, 0.01179438, 0.0567014], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 31, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [163.92694, 90.01544, 94.71944, 50.346054, 67.87139, 81.14708, 67.63436, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [18.26, 2509.22, 2118.3237, 75.82, 1.0, 0.44444445, 986967.0, 0.040728427, -0.080989294, -0.09478597, 0.01929238, -0.004792202, -0.081715085, 0.01179438, 0.0567014], "split_indices": [58, 50, 96, 50, 56, 57, 71, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5279.9453, 792.3829, 4487.5625, 462.66498, 329.7179, 1850.4467, 2637.116, 36.323723, 426.34125, 63.82015, 265.89774, 1702.2745, 148.17207, 2243.5847, 393.53113], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.009972019, -0.22459754, 0.11507847, -0.51606613, 0.01381195, 0.17180054, -0.47423324, -0.023529116, -0.076565355, 0.028284675, -0.035785034, 0.0030522475, 0.039617494, -0.05148976, 0.14730123], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 32, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [140.83821, 134.24841, 110.9158, 60.733215, 106.452156, 95.88831, 23.831512, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [124.65, 1951.81, 2.0, -0.25881904, 2.0, 85703.0, 3046.13, -0.023529116, -0.076565355, 0.028284675, -0.035785034, 0.0030522475, 0.039617494, -0.05148976, 0.14730123], "split_indices": [58, 42, 61, 1, 56, 71, 77, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5245.5527, 1930.8381, 3314.7146, 868.22327, 1062.6149, 3024.3274, 290.3872, 409.49823, 458.725, 616.55145, 446.06345, 1856.5074, 1167.82, 285.16763, 5.2195845], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.00937146, -0.22830945, 0.10599869, -0.44126055, 0.08967754, 0.39428133, -0.07791545, -0.057133336, -0.019744506, 0.0006604816, 0.07533032, 0.050083626, -0.057125866, -0.062294137, 0.012778117], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 33, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [131.51051, 121.70811, 180.81505, 34.019897, 39.76976, 136.93849, 233.55498, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 2.0, 3.0, 1426.54, 1.0, 376.7075, -0.057133336, -0.019744506, 0.0006604816, 0.07533032, 0.050083626, -0.057125866, -0.062294137, 0.012778117], "split_indices": [48, 59, 48, 84, 58, 61, 43, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5204.542, 1795.756, 3408.7861, 1075.2738, 720.4823, 1327.2297, 2081.5564, 700.3184, 374.95532, 641.22375, 79.258514, 1195.7505, 131.47922, 569.7869, 1511.7695], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.008868045, -0.36418205, 0.0563898, -0.6387392, -0.024740556, 0.10466258, -0.35384005, 0.045573466, -0.074018344, -0.08835607, 0.01743861, 0.0029956691, 0.09335486, -0.09578514, -0.008754989], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 34, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [119.602295, 74.57461, 86.336464, 49.45926, 61.536312, 241.46214, 73.7499, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [22.63, 2509.22, 8.0, 75.82, 1.0, 867.062, 21997.84, 0.045573466, -0.074018344, -0.08835607, 0.01743861, 0.0029956691, 0.09335486, -0.09578514, -0.008754989], "split_indices": [58, 50, 48, 50, 56, 43, 50, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5156.27, 799.3596, 4356.9106, 441.4285, 357.93112, 3898.866, 458.04453, 37.148075, 404.28043, 66.72282, 291.2083, 3577.4714, 321.39447, 139.35538, 318.68915], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.008374256, -0.34427163, 0.05431949, -0.14969543, -0.7483009, 0.13771935, -0.23119695, -0.034112245, 0.044150118, -0.08192643, 0.059821196, 0.0020896692, 0.084212095, -0.074755915, 0.007946016], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 35, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [108.03876, 63.325493, 102.98406, 61.9304, 25.529404, 275.38712, 156.90717, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [701.73, -1.8369701e-16, 0.26666668, 4491.75, 1426.54, 6112.28, 868.15125, -0.034112245, 0.044150118, -0.08192643, 0.059821196, 0.0020896692, 0.084212095, -0.074755915, 0.007946016], "split_indices": [50, 1, 57, 79, 58, 42, 51, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5128.4824, 805.9442, 4322.5386, 544.89777, 261.04645, 3345.7551, 976.7833, 411.93716, 132.96056, 248.34962, 12.69684, 2870.6904, 475.06476, 366.38538, 610.39795], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0078733675, -0.35917407, 0.047149867, -0.63263595, -0.034285065, 0.07766784, -0.5073272, 0.036737572, -0.076185845, -0.08897758, 0.015372132, 0.0045019463, 0.104684584, -0.099494725, 0.0027566224], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 36, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [98.43248, 61.24398, 74.54204, 48.65712, 50.995872, 132.02742, 59.97003, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [18.26, 2509.22, 1.1428572, 8.5925, 1.0, 21997.84, 2481.9946, 0.036737572, -0.076185845, -0.08897758, 0.015372132, 0.0045019463, 0.104684584, -0.099494725, 0.0027566224], "split_indices": [58, 50, 49, 51, 56, 50, 87, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5090.4155, 688.58105, 4401.8345, 373.36734, 315.21375, 4173.015, 228.81947, 42.514904, 330.85242, 56.119392, 259.09433, 4038.0334, 134.98145, 119.24927, 109.570206], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0074330503, -0.1839308, 0.09278436, -0.34378, -0.009312162, 0.187058, -0.23007466, -0.014601062, -0.06658413, 0.014292116, -0.057832837, 0.011502822, 0.14092593, -0.078608565, 0.0014101658], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 37, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [89.70796, 51.266003, 98.50413, 60.979652, 76.21238, 220.3085, 99.32973, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [126.21, 1.0, 0.33333334, -1.8369701e-16, 0.14285715, 717.1119, 970.6786, -0.014601062, -0.06658413, 0.014292116, -0.057832837, 0.011502822, 0.14092593, -0.078608565, 0.0014101658], "split_indices": [2, 56, 57, 1, 57, 51, 51, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5069.6777, 1835.7732, 3233.9045, 957.9129, 877.86035, 2503.355, 730.54974, 594.3758, 363.53714, 693.1621, 184.69823, 2365.0652, 138.28975, 222.24925, 508.30048], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0069700372, -0.15824819, 0.10491936, 0.46788254, -0.22739607, 0.07548283, 0.7485433, 0.18199854, 0.0379058, -0.016525557, -0.07364582, 0.01638587, -0.018707618, 0.10773565, -0.07117481], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 38, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [85.28862, 92.85829, 54.82098, 24.93771, 60.927788, 64.327324, 61.539963, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2118.3237, 29.0, 68704.98, 1.2075, 0.33333334, 0.21428572, 18.0, 0.18199854, 0.0379058, -0.016525557, -0.07364582, 0.01638587, -0.018707618, 0.10773565, -0.07117481], "split_indices": [96, 89, 70, 43, 57, 57, 40, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5036.8057, 2141.293, 2895.513, 212.38557, 1928.9073, 2769.945, 125.5678, 11.890877, 200.4947, 1720.2441, 208.66315, 2072.6716, 697.2735, 102.704185, 22.863619], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0064147236, -0.12376448, 0.13494903, -0.038708188, -0.71894944, 0.47603664, -0.095805295, -0.019624468, 0.020132734, -0.09337448, 0.03418504, -0.016132018, 0.06533571, -0.07151318, 0.006320798], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 39, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [83.36541, 138.97974, 179.4564, 90.92963, 78.537796, 104.08, 134.02573, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [276.15833, 2.0, 3.0, 1.0, 1082.91, 1.0, 441.59937, -0.019624468, 0.020132734, -0.09337448, 0.03418504, -0.016132018, 0.06533571, -0.07151318, 0.006320798], "split_indices": [43, 48, 56, 48, 58, 56, 51, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5023.335, 2744.8704, 2278.4644, 2402.5881, 342.28244, 918.99567, 1359.4689, 1450.6813, 951.9068, 284.74564, 57.53678, 200.04578, 718.9499, 277.02228, 1082.4465], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.006054447, -0.18329665, 0.08962762, -0.40904978, 0.007867999, 0.13948101, -0.4385024, -0.062256623, 0.0070212707, 0.0287383, -0.033203553, 0.008364658, 0.051294673, -0.07216426, -0.016926428], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 40, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [84.35457, 75.25865, 85.11169, 81.86543, 89.89859, 61.521748, 21.136456, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [124.65, 2374.81, 2.0, -0.70710677, 2.0, 741986.0, 2460.612, -0.062256623, 0.0070212707, 0.0287383, -0.033203553, 0.008364658, 0.051294673, -0.07216426, -0.016926428], "split_indices": [58, 50, 61, 0, 56, 71, 96, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4972.0894, 1742.7992, 3229.2903, 798.5836, 944.2155, 2951.4236, 277.86676, 552.25104, 246.33261, 518.2194, 425.9961, 2568.6287, 382.79492, 134.61775, 143.24901], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0057406877, -0.21556175, 0.06183397, -0.49238887, -0.012262566, 0.10539251, -0.37759528, 0.03574805, -0.05993224, 0.01009002, -0.0764671, 0.00674212, 0.13610429, -0.072667666, 0.0020240853], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 41, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [70.08087, 67.75699, 71.60235, 46.545395, 59.274185, 162.1268, 46.871506, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [64.64, 166.77, 10.0, 81.19, 4.0, 18068.91, 2503.5752, 0.03574805, -0.05993224, 0.01009002, -0.0764671, 0.00674212, 0.13610429, -0.072667666, 0.0020240853], "split_indices": [58, 43, 48, 42, 56, 42, 87, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4940.7866, 1203.061, 3737.7256, 508.80826, 694.2528, 3401.3284, 336.39737, 56.603203, 452.20505, 604.2405, 90.0123, 3302.5127, 98.81545, 178.73753, 157.65984], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0054419897, -0.1954721, 0.06734539, 0.21645954, -0.37901685, 0.11306768, -0.3174594, -0.006454458, 0.048760206, -0.04673634, 0.021024399, 0.0073270253, 0.11722522, -0.08145333, -0.0058277487], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 42, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [68.04798, 103.09773, 62.62233, 32.07805, 49.184692, 133.96786, 48.656754, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [38100.0, 474.33, 0.7, 1.0, 25183.18, 1333.8558, 1683.2175, -0.006454458, 0.048760206, -0.04673634, 0.021024399, 0.0073270253, 0.11722522, -0.08145333, -0.0058277487], "split_indices": [89, 88, 49, 48, 88, 43, 51, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4917.7275, 1361.4991, 3556.2283, 419.60202, 941.8971, 3179.1853, 377.04285, 206.42802, 213.174, 819.2723, 122.624794, 3065.088, 114.09735, 128.48476, 248.5581], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0050751846, -0.26981643, 0.041040834, -0.09180156, -0.68743974, 0.12034811, -0.18572314, -0.03304307, 0.03185306, -0.07674094, 0.053724702, 0.0027175264, 0.086090736, -0.07743642, 0.0020032697], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 43, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [59.838818, 53.992393, 75.09248, 50.136604, 21.679703, 213.4118, 131.06374, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [701.73, -1.8369701e-16, 4.0, 3.0, 1426.54, 6112.28, 9213.87, -0.03304307, 0.03185306, -0.07674094, 0.053724702, 0.0027175264, 0.086090736, -0.07743642, 0.0020032697], "split_indices": [50, 1, 56, 93, 58, 42, 50, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4899.3735, 726.1079, 4173.2656, 509.95557, 216.15231, 3092.263, 1081.0029, 322.56326, 187.3923, 203.25363, 12.898685, 2747.611, 344.6517, 279.2736, 801.7293], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0047995574, -0.15609588, 0.07403595, -0.39105037, -0.002993392, 0.11755506, -0.4004954, -0.058509666, 0.002454087, 0.027579485, -0.033420842, 0.014472067, -0.051025566, -0.07641412, -0.018507786], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 44, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [58.077473, 60.000854, 66.15593, 53.084892, 93.44036, 50.096905, 20.94236, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [124.65, 1.0, 2.0, -0.70710677, 0.11111111, 10.0, 2421.57, -0.058509666, 0.002454087, 0.027579485, -0.033420842, 0.014472067, -0.051025566, -0.07641412, -0.018507786], "split_indices": [58, 56, 61, 0, 57, 32, 58, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4867.2295, 1667.0151, 3200.214, 657.0845, 1009.9307, 2932.0676, 268.1466, 447.65787, 209.42659, 548.4555, 461.4752, 2811.1956, 120.87204, 98.80571, 169.3409], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.004591789, -0.090472184, 0.12834741, 0.013489009, -0.5810073, 0.42985246, -0.13336287, -0.014367362, 0.023265824, -0.076007836, 0.0339057, 0.027101262, 0.09730041, -0.06563109, 0.0045082676], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 45, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [55.334995, 150.16858, 150.15686, 83.75541, 85.09752, 76.01567, 95.1281, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [285.95187, 2.0, 4.0, 1.0, 1082.91, 627.2975, 9998.09, -0.014367362, 0.023265824, -0.076007836, 0.0339057, 0.027101262, 0.09730041, -0.06563109, 0.0045082676], "split_indices": [51, 56, 56, 48, 58, 43, 50, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4844.7573, 2943.5237, 1901.2339, 2429.584, 513.9396, 883.1512, 1018.08264, 1415.1487, 1014.4353, 430.35416, 83.58542, 684.48004, 198.67114, 258.3349, 759.74774], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.004340219, -0.12752797, 0.084665984, 0.40384573, -0.189295, 0.06655372, 0.98564416, 0.035934582, 0.2932527, -0.013840163, -0.076195344, 0.0016158158, 0.03812712, 0.004736887, 0.16140445], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 46, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [52.723896, 66.276855, 45.490414, 22.617142, 52.577087, 43.41467, 32.317787, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2118.3237, 29.0, 1078.32, 25.0, 0.4, 986967.0, 14.0, 0.035934582, 0.2932527, -0.013840163, -0.076195344, 0.0016158158, 0.03812712, 0.004736887, 0.16140445], "split_indices": [96, 89, 76, 80, 57, 71, 92, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4806.6274, 2015.9911, 2790.6362, 209.35959, 1806.6315, 2736.6946, 53.94159, 206.86134, 2.4982514, 1660.3173, 146.31422, 2359.8623, 376.8323, 22.067543, 31.874046], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0039251666, -0.28053352, 0.034814492, -0.53552324, -0.015350247, 0.053952597, -0.5698399, 0.046848778, -0.06895361, -0.08498601, 0.01651904, 0.003702185, 0.10227712, -0.07035585, 0.2521391], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 47, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [51.39525, 39.833244, 48.715446, 46.76573, 43.81137, 66.86879, 55.16386, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [18.26, 2509.22, 1.2666667, 8.5925, 1.0, 87.0, 2029.38, 0.046848778, -0.06895361, -0.08498601, 0.01651904, 0.003702185, 0.10227712, -0.07035585, 0.2521391], "split_indices": [58, 50, 57, 51, 56, 84, 77, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4794.3394, 588.205, 4206.135, 299.34616, 288.85883, 4077.9722, 128.1624, 39.54036, 259.80582, 50.71811, 238.14072, 4008.9514, 69.02087, 123.58868, 4.573721], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0036870763, -0.08198937, 0.12518878, -0.2657867, 0.023420433, 0.20445509, -0.36780605, -0.032427467, 0.07506068, 0.025601864, -0.02230309, 0.026409104, -0.034375988, -0.06714358, -0.0119680455], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 48, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [48.238674, 57.615578, 70.67663, 64.67601, 108.43773, 50.99678, 18.80009, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [404.07, 1.0, 2.0, 1.0, 2.0, 10.0, 2438.3, -0.032427467, 0.07506068, 0.025601864, -0.02230309, 0.026409104, -0.034375988, -0.06714358, -0.0119680455], "split_indices": [58, 56, 61, 61, 56, 40, 96, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4778.2188, 2972.5332, 1805.6854, 1082.8701, 1889.6631, 1556.0765, 249.6089, 1024.5913, 58.278767, 972.13416, 917.52893, 1403.878, 152.19847, 111.48247, 138.12643], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0035320474, -0.15294735, 0.064643666, 0.19793212, -0.2856677, 0.040816978, 1.3563951, -0.05214559, 0.030426268, -0.042804852, 0.010748289, -0.008451471, 0.019338833, 0.24980615, 0.04325453], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 49, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [48.425934, 69.42073, 100.4189, 31.472738, 60.564293, 61.328705, 61.245842, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [55762.0, 474.33, 689.6256, 1.0, 52.0, 797.77, 443.0, -0.05214559, 0.030426268, -0.042804852, 0.010748289, -0.008451471, 0.019338833, 0.24980615, 0.04325453], "split_indices": [89, 88, 94, 97, 91, 86, 81, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4751.9644, 1488.4979, 3263.4663, 408.37222, 1080.1257, 3205.374, 58.09235, 52.08471, 356.2875, 792.88934, 287.23636, 1760.025, 1445.349, 25.221006, 32.871346], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0033220863, -0.08317776, 0.11321581, 0.009169098, -0.5532856, 0.361484, -0.1167337, -0.011460576, 0.017801866, -0.07438388, 0.015095288, 0.015535704, 0.07344911, -0.055413853, 0.007442785], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 50, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [44.156105, 122.23389, 110.184105, 49.221848, 62.147736, 71.23231, 83.85326, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [275.72437, 2.0, 4.0, 1.0, 920.03, 6112.28, 867.062, -0.011460576, 0.017801866, -0.07438388, 0.015095288, 0.015535704, 0.07344911, -0.055413853, 0.007442785], "split_indices": [51, 56, 56, 48, 58, 42, 43, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4742.787, 2814.4902, 1928.2969, 2353.2114, 461.27878, 926.9406, 1001.35626, 1358.0308, 995.1807, 363.0014, 98.27735, 597.9338, 329.00684, 303.95807, 697.3982], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.003154204, -0.11904433, 0.07175641, -0.30770156, 0.0034332422, 0.15017153, -0.2032494, 0.011632446, -0.043293815, -0.0037941397, 0.13884, 0.021020588, -0.012494098, 0.09380108, -0.033469256], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 51, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [40.900394, 42.74047, 61.739693, 38.71705, 64.377686, 36.812046, 95.62184, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [339.74, 56717.0, 1.0, 192.89, 689.6256, 0.33333334, 1.0, 0.011632446, -0.043293815, -0.0037941397, 0.13884, 0.021020588, -0.012494098, 0.09380108, -0.033469256], "split_indices": [10, 89, 61, 88, 94, 57, 56, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4709.2827, 1848.6885, 2860.5945, 727.13715, 1121.5514, 2226.2544, 634.33997, 165.80977, 561.3274, 1089.9623, 31.589039, 1827.6626, 398.5919, 64.88007, 569.45984], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0029688154, -0.036763415, 0.24847151, -0.0035820936, -0.5335655, -0.0387538, 0.4778993, -0.0056225834, 0.056138314, -0.09838491, -0.020447481, 0.01254589, -0.0953437, -0.010352104, 0.062215447], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 52, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [39.909573, 68.24707, 36.688396, 115.48883, 38.20852, 37.37489, 26.017616, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [741986.0, 8.0, 2407.0393, 724.275, 2175.0525, 10.0, 159.41667, -0.0056225834, 0.056138314, -0.09838491, -0.020447481, 0.01254589, -0.0953437, -0.010352104, 0.062215447], "split_indices": [71, 56, 87, 43, 35, 32, 76, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4694.647, 4139.178, 555.46857, 3880.9758, 258.20242, 247.03343, 308.4351, 3550.993, 329.9828, 108.18586, 150.01657, 210.16537, 36.86806, 61.369156, 247.06595], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.002795085, -0.07427484, 0.11348175, -0.087792814, 1.047723, 0.24163744, -0.12274947, -0.0048985993, -0.05362187, 0.1874446, 0.03923172, 0.027197903, -0.07019321, 0.053848412, -0.024434995], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 53, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [38.90917, 44.063736, 53.98583, 49.840088, 18.007256, 33.279556, 50.58497, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [404.07, 268.0, 1.0, 0.33333334, 2326.024, 1.6, 1.0, -0.0048985993, -0.05362187, 0.1874446, 0.03923172, 0.027197903, -0.07019321, 0.053848412, -0.024434995], "split_indices": [58, 83, 61, 25, 78, 49, 56, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4679.3853, 2898.1465, 1781.2386, 2864.5557, 33.59078, 1154.7582, 626.48047, 2637.422, 227.13354, 14.031543, 19.559237, 1119.472, 35.286175, 96.78096, 529.69946], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0025588342, -0.015816055, 0.6089624, -0.09439873, 0.10242466, 0.79396, -0.12674122, -0.0038506878, -0.0479677, 0.008264164, 0.18285033, -0.018223446, 0.09844936], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 54, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [37.88548, 42.50783, 35.42709, 59.16111, 62.22146, 17.082066, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [135.0, 797.77, 13.5, 6.0, 117.0, 2118.3237, -0.12674122, -0.0038506878, -0.0479677, 0.008264164, 0.18285033, -0.018223446, 0.09844936], "split_indices": [74, 86, 47, 56, 20, 96, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4670.9507, 4572.791, 98.15979, 2747.2036, 1825.5874, 89.87555, 8.284244, 2400.1208, 347.08273, 1805.937, 19.65042, 14.684321, 75.19122], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.0023512447, -0.085642114, 0.09738, -0.014682141, -0.6195884, 0.34899056, -0.07327746, 0.009007826, -0.019288167, -0.08302649, 0.02153924, -0.024067482, 0.05254456, -0.0606686, 0.008242042], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 55, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [38.766273, 96.34308, 91.22154, 41.9413, 52.65152, 89.45413, 105.219154, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [276.15833, 2.0, 3.0, -1.8369701e-16, 1082.91, 1.0, 466.935, 0.009007826, -0.019288167, -0.08302649, 0.02153924, -0.024067482, 0.05254456, -0.0606686, 0.008242042], "split_indices": [43, 48, 56, 1, 58, 56, 51, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4664.8633, 2542.0247, 2122.8386, 2244.7332, 297.2913, 857.5121, 1265.3267, 1413.976, 830.7573, 237.39825, 59.89304, 197.42076, 660.0913, 285.23563, 980.09106], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0022966412, -0.24132848, 0.029703962, -0.48205343, -0.0027273311, 0.053376347, -0.32922554, 0.048782606, -0.06462082, -0.08071866, 0.016142804, -0.001429666, 0.020054407, -0.06958196, -0.010788739], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 56, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [35.46198, 31.438404, 34.7585, 43.69517, 36.565414, 38.212936, 20.463316, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [18.26, 2509.22, 2.0, 8.5925, 1.0, 546.95, 2421.57, 0.048782606, -0.06462082, -0.08071866, 0.016142804, -0.001429666, 0.020054407, -0.06958196, -0.010788739], "split_indices": [58, 50, 61, 51, 56, 58, 58, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4634.1226, 546.3751, 4087.7473, 271.46924, 274.9059, 3835.6284, 252.11893, 39.010555, 232.45866, 45.925648, 228.98027, 2628.0566, 1207.5719, 94.10787, 158.01105], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0022640876, -0.2110719, 0.032810308, -0.04770055, -0.6351747, 0.10539675, -0.10746894, -0.027130624, 0.03095052, -0.07841373, -0.0028518175, 0.0035467537, 0.052746534, -0.058106776, 0.006730869], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 57, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [33.85366, 46.0298, 40.309967, 38.502335, 16.676788, 76.97496, 111.78898, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [701.73, -1.8369701e-16, 3.0, 3.0, 611.19, 4785.16, 466.935, -0.027130624, 0.03095052, -0.07841373, -0.0028518175, 0.0035467537, 0.052746534, -0.058106776, 0.006730869], "split_indices": [50, 1, 56, 93, 58, 42, 51, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4620.4673, 663.7799, 3956.6873, 479.9917, 183.7882, 2607.6348, 1349.0524, 295.34833, 184.64336, 147.32133, 36.466854, 2237.9333, 369.70145, 363.02658, 986.0258], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0022333043, -0.07309767, 0.09186375, -0.03647935, -0.4522089, 0.18884403, -0.15467015, -0.016284546, 0.009839608, -0.07539671, -0.007142684, 0.010969177, 0.09885722, -0.054071136, 0.0070722536], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 58, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [30.648954, 36.380665, 47.237656, 40.784016, 26.471447, 89.621056, 48.58943, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [323.76, 0.2, 5.0, 1.0, 579.00165, 7502.68, 12172.79, -0.016284546, 0.009839608, -0.07539671, -0.007142684, 0.010969177, 0.09885722, -0.054071136, 0.0070722536], "split_indices": [2, 33, 40, 48, 27, 26, 26, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4594.329, 2620.8179, 1973.5112, 2390.9705, 229.84737, 1416.5212, 556.99005, 1234.3182, 1156.6522, 127.68269, 102.16467, 1289.9835, 126.53761, 204.80737, 352.18268], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0021137309, -0.27086052, 0.02180452, -0.5278063, -0.017233998, -0.04989081, 0.13108572, 0.047349162, -0.07287293, -0.08906438, 0.014696441, -0.0024391161, -0.08716891, 0.009568401, 0.09968983], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 59, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [29.406002, 24.355389, 32.920525, 37.70211, 27.262274, 53.151688, 50.923717, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [7.71, 2586.1, 797.77, 9.695455, 1.0, 18.0, 44.0, 0.047349162, -0.07287293, -0.08906438, 0.014696441, -0.0024391161, -0.08716891, 0.009568401, 0.09968983], "split_indices": [58, 50, 86, 43, 56, 40, 74, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4572.7876, 372.87045, 4199.9175, 184.68622, 188.18423, 2536.414, 1663.5033, 30.639252, 154.04697, 29.079477, 159.10475, 2461.0786, 75.33533, 1599.2241, 64.27926], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0020578343, -0.013911399, 0.54670835, 0.016216526, -0.30877277, 0.7096516, -0.119994044, -0.0011474744, 0.10719693, -0.07671846, -0.0003820655, 0.056307673, 0.20747545], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 60, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [29.683767, 39.685066, 28.311869, 118.518456, 57.889946, 16.615974, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [135.0, 0.46666667, 13.5, 970.6786, 1426.715, 365.0, -0.119994044, -0.0011474744, 0.10719693, -0.07671846, -0.0003820655, 0.056307673, 0.20747545], "split_indices": [74, 57, 47, 51, 51, 93, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4561.1694, 4465.6836, 95.48588, 4052.5527, 413.13095, 87.881226, 7.604652, 3949.9392, 102.61356, 164.43393, 248.69704, 80.635635, 7.2455897], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.0018649489, -0.097789705, 0.06037027, -0.060675606, -0.5387183, 0.12976752, -0.14155513, -0.022172617, 0.010886541, -0.09444667, -0.0010508011, -0.008885433, 0.021354092, 0.049840633, -0.029107878], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 61, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [27.18722, 29.29264, 38.723457, 45.18583, 29.780392, 37.676327, 67.79209, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [166.67, 3.0, 1.0, 2306.24, 1055.9225, 1.0, 1.0, -0.022172617, 0.010886541, -0.09444667, -0.0010508011, -0.008885433, 0.021354092, 0.049840633, -0.029107878], "split_indices": [58, 40, 61, 42, 35, 56, 56, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4552.0615, 1790.9902, 2761.071, 1652.9913, 137.99895, 2055.1272, 705.944, 847.5644, 805.42694, 77.6011, 60.397858, 569.32086, 1485.8063, 133.25955, 572.6844], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.001821761, 0.06533566, -0.091296785, 0.4985965, 0.046142638, -0.38970187, 0.01737113, 0.17743854, 0.03443606, -0.04387877, 0.008406061, -0.06775304, -0.0038302324, 0.025423512, -0.011801584], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 62, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [27.243355, 21.513754, 63.04191, 20.987177, 45.659477, 52.463844, 45.75741, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [-1.8369701e-16, 29.0, 2130.51, 6.07, 845.0, 241.11, 2.0, 0.17743854, 0.03443606, -0.04387877, 0.008406061, -0.06775304, -0.0038302324, 0.025423512, -0.011801584], "split_indices": [1, 89, 50, 18, 80, 58, 56, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4531.832, 2588.9268, 1942.9054, 108.76233, 2480.1643, 517.9671, 1424.9382, 10.597653, 98.16467, 179.0996, 2301.0647, 284.22922, 233.73792, 517.92804, 907.01013], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0018137874, -0.09192043, 0.061757784, 0.19793826, -0.16867699, -1.0979009, 0.073835686, -0.048062485, 0.029450154, -0.011661253, -0.06517886, -0.12299668, 0.117961764, 0.108252704, 0.005906551], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 63, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [25.879667, 41.613876, 37.183968, 25.810326, 37.104134, 9.682808, 39.009865, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2118.3237, 474.33, 820.0, 1.0, 6.0, 2.3333333, 59.0025, -0.048062485, 0.029450154, -0.011661253, -0.06517886, -0.12299668, 0.117961764, 0.108252704, 0.005906551], "split_indices": [96, 88, 89, 97, 56, 31, 85, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4515.933, 1867.9071, 2648.0261, 390.74466, 1477.1625, 26.368362, 2621.6577, 48.182495, 342.56216, 1334.5778, 142.58476, 25.357893, 1.0104676, 36.791412, 2584.8662], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0017455145, -0.012729687, 0.50836486, 0.021683035, -0.23016295, 0.65524167, -0.11531345, -0.003474509, 0.059450354, -0.07058874, 2.1197196e-05, 0.13552597, 0.040053144], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 64, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [25.285303, 33.058235, 23.969124, 123.32496, 66.1617, 15.154007, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [135.0, 0.33333334, 13.5, 534.3575, 970.6786, 42.0, -0.11531345, -0.003474509, 0.059450354, -0.07058874, 2.1197196e-05, 0.13552597, 0.040053144], "split_indices": [74, 57, 47, 51, 51, 75, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4510.5474, 4416.424, 94.12352, 3813.7327, 602.69147, 87.03522, 7.0882964, 3472.5896, 341.1429, 195.9632, 406.72827, 22.067236, 64.96799], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.0016085654, -0.061868403, 0.09050253, 0.119881645, -0.1484087, -0.10178533, 0.24659702, 0.006013999, 0.118375935, -0.016176885, 0.13746789, -0.027594486, 0.025137229, 0.33948314, 0.022911936], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 65, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [24.964281, 42.78189, 53.413414, 55.7042, 37.710823, 49.13072, 53.258034, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [797.77, 133.5922, 3.0, 716.542, 6843.69, 256.68, 57.0, 0.006013999, 0.118375935, -0.016176885, 0.13746789, -0.027594486, 0.025137229, 0.33948314, 0.022911936], "split_indices": [86, 85, 84, 77, 87, 43, 90, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4495.574, 2717.8577, 1777.7166, 876.5535, 1841.3041, 796.6709, 981.0457, 830.9484, 45.605072, 1826.1791, 15.125, 533.697, 262.97382, 4.3494253, 976.6963], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0014116495, -0.050886117, 0.10694362, -0.018652456, -0.37225157, 0.23112279, -0.08343447, -0.006104828, 0.037343238, -0.059085667, 0.0047010253, 0.026313916, -0.08009743, -0.027814994, 0.023153232], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 66, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [24.062029, 31.913462, 33.282207, 46.59361, 25.768581, 28.358269, 34.1745, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [546.95, 0.33333334, 1.0, 585.32166, 1254.0067, 19.0, 795.38, -0.006104828, 0.037343238, -0.059085667, 0.0047010253, 0.026313916, -0.08009743, -0.027814994, 0.023153232], "split_indices": [58, 25, 61, 43, 27, 40, 86, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4486.4834, 3080.4814, 1406.0018, 2800.6301, 279.85132, 850.81866, 555.1831, 2528.1116, 272.51843, 183.67369, 96.17762, 825.94244, 24.876274, 343.15707, 212.02606], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0013598726, 0.060913898, -0.085370675, 0.14687918, -0.026324425, -0.35753393, 0.010637208, 0.0061732684, 0.039985176, -0.049239844, 0.00027820579, -0.070013836, -0.008183311, 0.022896145, -0.011532635], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 67, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [23.428022, 19.29086, 49.81613, 27.879908, 17.328766, 46.901165, 38.80805, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [-1.8369701e-16, 10.0, 2130.51, 302391.0, 1443.46, 176.13, 2.0, 0.0061732684, 0.039985176, -0.049239844, 0.00027820579, -0.070013836, -0.008183311, 0.022896145, -0.011532635], "split_indices": [1, 73, 50, 89, 96, 58, 56, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4476.1226, 2570.782, 1905.3405, 1294.4941, 1276.2877, 496.14478, 1409.1957, 969.4402, 325.05396, 74.08401, 1202.2037, 220.5433, 275.60147, 515.2791, 893.91656], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0013817092, -0.011691162, 0.48105517, 0.031335972, -0.17891698, 0.61143905, -0.11011006, -0.0017563358, 0.056011446, -0.061794735, 0.0010413522, 0.047247827, 0.18965651], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 68, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [22.214817, 31.46979, 19.950827, 89.96266, 74.40396, 14.63665, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [135.0, 4.0, 13.5, 6112.28, 786.99835, 365.0, -0.11011006, -0.0017563358, 0.056011446, -0.061794735, 0.0010413522, 0.047247827, 0.18965651], "split_indices": [74, 56, 47, 42, 43, 93, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4464.362, 4371.9116, 92.44997, 3477.8699, 894.04193, 85.978134, 6.471832, 3184.3596, 293.51016, 268.70035, 625.34155, 78.822685, 7.155452], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.0012558515, 0.009862746, -0.42426953, -0.0056411964, 0.87076694, -0.5562348, 0.20718344, 0.0012749014, -0.062105067, 0.1813064, 0.049028534, -0.10482966, -0.015987655], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 69, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [20.93305, 57.89022, 38.699665, 48.23289, 26.619473, 21.101067, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2666667, 2057.1743, 2029.38, 0.6666667, 14.0, 986967.0, 0.20718344, 0.0012749014, -0.062105067, 0.1813064, 0.049028534, -0.10482966, -0.015987655], "split_indices": [57, 51, 77, 57, 56, 71, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4448.807, 4335.8203, 112.98711, 4260.094, 75.72619, 108.051735, 4.9353743, 4137.4346, 122.65921, 20.698729, 55.027462, 47.472202, 60.57953], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.0011464673, -0.02512389, 0.20366536, -0.012405336, -0.7084892, 1.2007586, 0.117137216, -0.0020014716, 0.10473087, 0.04004692, -0.080647655, 0.12961553, -0.08441935, -0.017289426, 0.052441757], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 70, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [21.836285, 34.587273, 40.064507, 31.54038, 8.224129, 8.332249, 50.872257, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1800.99, 2.0, 1.0, 246764.95, 1.0, 969.33, 815.08, -0.0020014716, 0.10473087, 0.04004692, -0.080647655, 0.12961553, -0.08441935, -0.017289426, 0.052441757], "split_indices": [58, 61, 83, 79, 92, 94, 86, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4444.487, 3979.4841, 465.00287, 3907.7722, 71.71193, 36.102657, 428.9002, 3880.8862, 26.885963, 5.5714025, 66.14053, 34.843388, 1.259269, 250.84476, 178.05545], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0010443748, -0.11474269, 0.040006544, 0.16950312, -0.25536537, 0.023733763, 0.7613629, 0.02454536, -0.05507061, -0.041544084, 0.0059588607, -0.032088596, 0.0052600284, -0.06403064, 0.1204012], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 71, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [20.734524, 47.14754, 38.295876, 21.503448, 39.793488, 31.780643, 45.426968, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [38100.0, 474.33, 689.6256, 1.0, 387.05957, 1425.0, 3065.95, 0.02454536, -0.05507061, -0.041544084, 0.0059588607, -0.032088596, 0.0052600284, -0.06403064, 0.1204012], "split_indices": [89, 88, 94, 92, 85, 96, 88, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4440.4116, 1177.4487, 3262.963, 389.6432, 787.8055, 3191.989, 70.97386, 353.0691, 36.57411, 522.1192, 265.68628, 245.92497, 2946.064, 16.927044, 54.046814], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0010652265, -0.23121555, 0.018482195, 0.5249584, -0.2931075, 0.03128946, -0.39992493, 0.024340948, 0.356049, -0.07130284, -0.006972991, 0.00028725108, 0.031146003, -0.07741645, -0.0049919765], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 72, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [19.95845, 16.407719, 21.92459, 21.89568, 30.122547, 31.595339, 15.878851, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [7.71, 75.82, 3.0, 5418.0, 166.77, 1800.99, 2503.5752, 0.024340948, 0.356049, -0.07130284, -0.006972991, 0.00028725108, 0.031146003, -0.07741645, -0.0049919765], "split_indices": [58, 50, 61, 90, 43, 58, 87, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4434.4, 346.29633, 4088.1035, 25.633507, 320.6628, 3967.5825, 120.52079, 24.446274, 1.1872319, 110.58513, 210.0777, 3603.1365, 364.4462, 57.658325, 62.86247], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0010112992, 0.07479751, -0.059454013, 0.17225341, -0.02392523, -0.4528369, -0.017945688, 0.005802554, 0.035705652, 0.027528096, -0.0068234326, -0.06399422, 0.00029332546, 0.009893637, -0.012459295], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 73, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [19.62829, 18.559519, 40.840916, 20.469488, 12.73221, 20.360226, 28.23274, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [-0.25881904, 10.0, 693.07, 10740.9, 93.94909, 2.0, 2.0, 0.005802554, 0.035705652, 0.027528096, -0.0068234326, -0.06399422, 0.00029332546, 0.009893637, -0.012459295], "split_indices": [1, 73, 50, 88, 51, 59, 56, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4428.289, 1927.6122, 2500.6768, 969.6551, 957.9571, 237.73204, 2262.9448, 600.0652, 369.58987, 122.89188, 835.0652, 168.25497, 69.47706, 1079.6998, 1183.245], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0010176807, -0.09695735, 0.04596714, -0.16553707, 0.43993795, 0.12590483, -0.29399198, -0.014000298, -0.10770319, 0.048654873, -0.14102058, 0.031599145, -0.002703754, -0.054239273, 0.007465196], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 74, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [19.957335, 53.66263, 80.80119, 29.90369, 14.926064, 69.98152, 51.87746, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 668.9963, 66.0, 2.0, 2421.57, -0.014000298, -0.10770319, 0.048654873, -0.14102058, 0.031599145, -0.002703754, -0.054239273, 0.007465196], "split_indices": [48, 61, 61, 35, 74, 48, 58, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4425.391, 1454.4481, 2970.943, 1290.3219, 164.12617, 2405.8608, 565.08234, 1256.2827, 34.03919, 160.81186, 3.3143222, 1072.198, 1333.6627, 337.31824, 227.7641], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.001043135, -0.044418897, 0.10534765, -0.013866393, -0.38283187, 0.18957947, -0.20216995, -0.005050096, 0.05859982, -0.08483459, 0.009888386, 0.013792087, 0.13255356, -0.084578946, 0.0012271417], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 75, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [20.36656, 32.409782, 33.147324, 63.24394, 58.290882, 58.727184, 38.009148, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [576.66, 4.0, 10.0, 6112.28, 867.062, 21997.84, 20876.38, -0.005050096, 0.05859982, -0.08483459, 0.009888386, 0.013792087, 0.13255356, -0.084578946, 0.0012271417], "split_indices": [2, 56, 48, 42, 43, 50, 34, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4411.3174, 3134.1145, 1277.2031, 2875.5461, 258.56827, 1002.8869, 274.3162, 2710.9033, 164.64285, 131.1084, 127.45988, 960.3362, 42.55072, 67.81966, 206.49654], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.00099221, 0.00404217, -0.86185676, 0.028338725, -0.16238026, -1.086884, 0.69389266, -0.00089949585, 0.07689094, -0.06557735, 0.0036857356, 0.017794093, -0.119588055, -0.030202916, 0.115445904], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 76, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [19.061735, 17.686352, 9.706488, 105.53082, 54.80137, 3.3463917, 2.1108255, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1213.7, 6.0, 4.0, 867.062, 1333.8558, 100.0, 0.083333336, -0.00089949585, 0.07689094, -0.06557735, 0.0036857356, 0.017794093, -0.119588055, -0.030202916, 0.115445904], "split_indices": [94, 48, 91, 43, 43, 89, 55, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4396.463, 4371.891, 24.57174, 3815.6604, 556.231, 21.730034, 2.8417068, 3633.477, 182.18318, 159.3426, 396.8884, 1.6738373, 20.056196, 1.007445, 1.8342617], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.00093999517, -0.14881966, 0.027922308, 0.093499385, -0.48956648, 0.47932976, -0.00095154485, 0.0067034997, 0.39620537, -0.1059044, -0.020268723, 0.033815354, 0.19641596, 0.472035, -0.00044419258], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 77, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [18.735023, 59.232582, 47.870117, 42.707825, 48.480408, 45.620583, 56.921978, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [8900.0, 194.77744, 1131.0, 4338.24, 320.83566, 1650.17, 152.48, 0.0067034997, 0.39620537, -0.1059044, -0.020268723, 0.033815354, 0.19641596, 0.472035, -0.00044419258], "split_indices": [89, 94, 71, 87, 85, 78, 88, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4387.5244, 715.81244, 3671.7117, 418.7487, 297.06375, 219.80035, 3451.9114, 416.91397, 1.8347383, 98.61458, 198.44917, 201.83757, 17.96279, 1.5517355, 3450.3596], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007873138, -0.023056101, 0.18833695, -0.00060751755, -0.47333297, 0.38803485, -0.27248815, -0.0015408748, 0.06322168, -0.022128655, -0.078380845, 0.025423992, 0.07596754, -0.07718685, 0.008867483], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 78, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [18.473085, 39.665607, 42.62752, 35.029778, 14.409103, 15.856613, 25.369852, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1785.69, 2.0, 8.0, 21997.84, 5.0, 3050248.0, 2481.9946, -0.0015408748, 0.06322168, -0.022128655, -0.078380845, 0.025423992, 0.07596754, -0.07718685, 0.008867483], "split_indices": [2, 16, 24, 50, 48, 80, 87, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4384.241, 3923.1777, 461.06372, 3737.8293, 185.34837, 321.77914, 139.28458, 3653.356, 84.47329, 103.24556, 82.10281, 237.8386, 83.94055, 57.979332, 81.305244], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007754595, 0.040218446, -0.10384069, -0.123019174, 0.075298026, -0.48243845, 0.008701865, 0.046792623, -0.018987259, 0.013218954, -0.006613869, -0.06191652, 0.007944274, 0.020367445, -0.010889514], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 79, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [18.505747, 17.953419, 53.110703, 22.002293, 20.768507, 21.991837, 22.07372, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.25881904, 51.44, 134.91455, 115.11, 3.0, 271.86, 2.0, 0.046792623, -0.018987259, 0.013218954, -0.006613869, -0.06191652, 0.007944274, 0.020367445, -0.010889514], "split_indices": [1, 58, 51, 42, 48, 2, 56, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4378.022, 3132.635, 1245.387, 553.6765, 2578.9585, 284.62158, 960.7654, 55.661533, 498.01498, 1839.217, 739.7414, 228.8354, 55.78618, 361.19016, 599.57526], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.000815259, -0.043242298, 0.091620535, 0.014206689, -0.18436833, 0.28874764, 0.0037935295, -0.014811081, 0.020782448, -0.06765701, 0.0108020585, 0.021462854, 0.10264893, -0.014791702, 0.02249293], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 80, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [17.143728, 24.294317, 23.808283, 66.98082, 124.83039, 22.95618, 31.984104, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [546.95, 2.0, 2.0, 1.0, 410.7525, 3369.28, 14932.0, -0.014811081, 0.020782448, -0.06765701, 0.0108020585, 0.021462854, 0.10264893, -0.014791702, 0.02249293], "split_indices": [58, 48, 74, 48, 43, 58, 70, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4369.4077, 2995.183, 1374.2247, 2129.297, 865.8859, 422.851, 951.3738, 1158.3997, 970.89746, 322.19513, 543.6908, 385.41974, 37.431255, 564.45776, 386.91602], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0008579182, 0.014320041, -0.26935744, -0.007685815, 0.29598057, -0.54521286, -0.011206733, -0.0015285456, 0.08565427, 0.05462497, -0.014851503, -0.067696504, 0.020040933, -0.070564725, 0.029222596], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 81, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [17.735842, 25.534197, 16.57593, 25.11351, 33.29139, 11.19186, 25.767715, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.0, 1800.99, 3369.28, 91.0, 9.0, 26140.17, 170.91072, -0.0015285456, 0.08565427, 0.05462497, -0.014851503, -0.067696504, 0.020040933, -0.070564725, 0.029222596], "split_indices": [61, 58, 58, 84, 56, 50, 85, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4350.114, 4118.2603, 231.85367, 3820.7236, 297.5366, 111.545784, 120.30789, 3788.3926, 32.331127, 190.21109, 107.325516, 94.87361, 16.672173, 36.18008, 84.127815], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.00079813035, 0.009175626, -0.39065158, -0.004908911, 0.765393, -0.5123232, 0.17321174, 0.0017265523, -0.04294226, 0.15728065, 0.033687122, -0.101002455, -0.013460489], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 82, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [16.902325, 45.14906, 28.894266, 39.186733, 26.28712, 19.288986, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2666667, 25446.93, 2029.38, 8.0, 14.0, 986967.0, 0.17321174, 0.0017265523, -0.04294226, 0.15728065, 0.033687122, -0.101002455, -0.013460489], "split_indices": [57, 34, 77, 48, 48, 71, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4345.0444, 4237.609, 107.43574, 4161.1016, 76.50694, 102.329155, 5.1065884, 3955.4487, 205.65317, 25.60054, 50.906403, 43.42979, 58.89936], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.00072983647, 0.0037869227, -0.8334493, -0.023307856, 0.14050707, -1.0586898, 0.6326365, 0.00025655143, -0.04884877, 0.057634033, 0.0008642071, 0.017034277, -0.117554545, 0.11867591, -0.017959325], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 83, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [16.335344, 16.005768, 8.407032, 43.404858, 41.06628, 3.1708984, 1.8992419, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1213.7, 422.50375, 4.0, 488.09332, 468.6111, 100.0, 4905.2, 0.00025655143, -0.04884877, 0.057634033, 0.0008642071, 0.017034277, -0.117554545, 0.11867591, -0.017959325], "split_indices": [94, 85, 91, 76, 85, 89, 88, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4341.289, 4318.859, 22.430498, 3605.2173, 713.6414, 19.684214, 2.7462838, 3416.2021, 189.01518, 164.98174, 548.65967, 1.6693233, 18.01489, 1.3584226, 1.387861], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007105756, -0.17111078, 0.020788008, 0.20768571, -0.5207403, 0.5885894, 0.0039435048, 0.10954962, 0.012101441, -0.06549622, 0.048408996, 0.20603684, 0.033348046, -0.021516075, 0.0032381762], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 84, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [15.905188, 64.526566, 36.871204, 17.886906, 34.469513, 41.015892, 23.34338, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [845.0, 2.0, 1.0, 2.3966668, 2663.7, 198.0, 88.805, 0.10954962, 0.012101441, -0.06549622, 0.048408996, 0.20603684, 0.033348046, -0.021516075, 0.0032381762], "split_indices": [80, 90, 82, 51, 70, 71, 76, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4339.719, 485.4028, 3854.3164, 233.17824, 252.22456, 110.070564, 3744.2458, 19.703844, 213.4744, 222.81047, 29.414091, 15.215072, 94.85549, 429.39334, 3314.8525], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.00072161376, -0.022736868, 0.16939731, -0.004076103, -0.5032813, 0.43597895, -0.056212265, -0.0033723381, 0.019214554, -0.07534345, 0.03357126, 0.065780625, -0.07540833, -0.037696056, 0.05601419], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 85, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [16.245481, 34.43802, 29.917095, 21.519705, 30.382397, 60.66904, 53.605286, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1363.3075, 1232.56, 2712.0098, 741986.0, 66.0, 6.0, 11193.92, -0.0033723381, 0.019214554, -0.07534345, 0.03357126, 0.065780625, -0.07540833, -0.037696056, 0.05601419], "split_indices": [86, 86, 87, 71, 92, 97, 79, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4335.6387, 3839.6167, 496.02216, 3697.059, 142.55774, 226.93819, 269.08398, 3212.5068, 484.55203, 109.89391, 32.663826, 191.66005, 35.278126, 177.23828, 91.845695], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.000742339, 0.009471184, -0.37014043, -0.008826855, 0.3120718, -0.7926192, -0.091823526, 0.0035346665, -0.0143356, 0.13688509, 0.01926698, 0.07174533, -0.08745239, -0.08214358, 0.02692667], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 86, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [16.346523, 23.347652, 13.634005, 23.637266, 30.075163, 6.3371487, 19.033678, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3.0, 2421.57, 2438.3, 3.0, 4.0, 33.600834, 178.52042, 0.0035346665, -0.0143356, 0.13688509, 0.01926698, 0.07174533, -0.08745239, -0.08214358, 0.02692667], "split_indices": [61, 58, 96, 56, 82, 43, 85, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4330.737, 4215.1655, 115.57115, 3975.727, 239.43849, 45.164524, 70.406624, 2993.522, 982.2052, 23.244707, 216.19379, 1.9244221, 43.2401, 22.886898, 47.519726], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0006967307, 0.0118614705, -0.28348538, -0.011009518, 0.7344657, -0.78867096, 0.07582156, 0.0010302153, -0.07002577, 0.15912707, 0.048007667, -0.08948412, 0.040875256, -0.09845242, 0.04817748], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 87, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [15.380702, 68.5423, 33.486862, 59.06659, 26.969559, 10.107437, 47.189816, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.1428572, 21997.84, 2427.91, 8.0, 83.0, 267.0, 1333771.0, 0.0010302153, -0.07002577, 0.15912707, 0.048007667, -0.08948412, 0.040875256, -0.09845242, 0.04817748], "split_indices": [49, 50, 87, 56, 92, 74, 80, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4329.039, 4145.885, 183.15419, 4019.6445, 126.24013, 75.627266, 107.52692, 3900.0398, 119.60483, 27.69743, 98.5427, 69.69998, 5.9272885, 29.37486, 78.152054], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.00073163904, -0.0472327, 0.07667717, 0.029477168, -0.42125574, 0.23717026, -0.10393263, -0.013990082, 0.008992161, -0.057475597, 0.022440355, -0.03439811, 0.036641028, -0.05135442, 0.00962513], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 88, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [15.567399, 77.52911, 47.070175, 22.975079, 45.706635, 64.69396, 62.723557, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [296.0407, 2.0, 0.26666668, 701.73, 1082.91, 1.0, 868.15125, -0.013990082, 0.008992161, -0.057475597, 0.022440355, -0.03439811, 0.036641028, -0.05135442, 0.00962513], "split_indices": [51, 56, 57, 50, 58, 48, 51, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4322.7617, 2700.7534, 1622.008, 2241.879, 458.87463, 858.6676, 763.34045, 589.2796, 1652.5994, 370.82404, 88.05058, 155.91331, 702.7543, 250.07585, 513.2646], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.00080065266, 0.03692105, -0.0969136, 0.024294516, 0.39566207, -0.4416028, 0.013243481, 0.003328093, -0.054152478, 0.01099947, 0.18568884, -0.06638431, -0.004123395, -0.036163256, 0.0075311996], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 89, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [15.597563, 13.985108, 46.04693, 15.198849, 43.617214, 26.105034, 21.4308, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.25881904, 2314.08, 2130.51, 864.4, 102.0, 331.78, 1.0, 0.003328093, -0.054152478, 0.01099947, 0.18568884, -0.06638431, -0.004123395, -0.036163256, 0.0075311996], "split_indices": [1, 86, 50, 85, 91, 10, 56, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4300.143, 3088.577, 1211.5662, 2984.5964, 103.98053, 292.69482, 918.87134, 2938.8623, 45.734215, 87.87616, 16.10437, 187.79424, 104.90059, 129.84122, 789.0301], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.00081733963, 0.012045964, -0.27340594, -0.0036399593, 1.0128846, -0.73414874, 0.059726413, 0.0013613517, -0.051503, 0.15489839, 0.049091678, -0.08309101, 0.065763704, -0.09439581, 0.040182363], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 90, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [15.051846, 64.35907, 29.73481, 35.62693, 16.941338, 11.488262, 39.244835, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [14.0, 26140.17, 2427.91, 8.0, 625.5047, 2663.74, 1333771.0, 0.0013613517, -0.051503, 0.15489839, 0.049091678, -0.08309101, 0.065763704, -0.09439581, 0.040182363], "split_indices": [56, 50, 87, 48, 95, 96, 80, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4290.751, 4098.3096, 192.44113, 4036.05, 62.259758, 80.24857, 112.19256, 3905.2659, 130.78401, 29.743462, 32.516293, 75.408905, 4.839673, 28.072086, 84.12047], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007882838, -0.020751582, 0.17158239, 0.02747877, -0.19321793, 0.41398746, -0.14034581, -0.002273754, 0.029914562, -0.05855256, 0.0022677244, 0.0302769, 0.116333686, -0.054305565, 0.014417822], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 91, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [14.750731, 31.960846, 33.700813, 40.97415, 71.13135, 20.534897, 22.457285, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1785.69, 3.0, 10.0, 346.29, 466.935, 1532.9109, 2481.9946, -0.002273754, 0.029914562, -0.05855256, 0.0022677244, 0.0302769, 0.116333686, -0.054305565, 0.014417822], "split_indices": [2, 56, 48, 43, 51, 43, 87, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4284.614, 3840.681, 443.93307, 3002.0078, 838.67316, 249.62093, 194.31215, 2534.4443, 467.56354, 297.09805, 541.5751, 218.58289, 31.038033, 80.07164, 114.24051], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.00076543854, 0.012925193, -0.2484099, -0.036387138, 0.12422164, -0.6722377, -0.08865218, -0.0014934788, -0.044702344, 0.015998144, -0.03164932, 0.0887815, -0.08090719, -0.06342466, 0.00946382], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 92, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [14.477563, 22.212893, 15.087176, 24.701683, 19.634487, 13.758417, 16.421846, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.0, 491.9, 1092.26, 6.0, 10.0, 13.5075, 170.91072, -0.0014934788, -0.044702344, 0.015998144, -0.03164932, 0.0887815, -0.08090719, -0.06342466, 0.00946382], "split_indices": [61, 58, 10, 56, 32, 43, 85, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4268.205, 4045.504, 222.70146, 2803.8635, 1241.6403, 60.08682, 162.61464, 2665.642, 138.22139, 1149.0433, 92.596886, 4.402739, 55.68408, 40.273502, 122.34113], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.00076372264, -0.008984168, 0.40202245, 0.0074526486, -0.49533132, 0.77317077, -0.17246439, -0.0011050679, 0.055835303, -0.073169254, 0.04845206, 0.13503528, 0.0014904983, -0.06859724, 0.16437], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 93, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [14.125789, 33.42498, 18.326883, 41.239906, 32.01221, 22.746859, 33.12321, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [135.0, 21.0, 176.0, 15.0, 889.20624, 49.0, 365.0, -0.0011050679, 0.055835303, -0.073169254, 0.04845206, 0.13503528, 0.0014904983, -0.06859724, 0.16437], "split_indices": [74, 75, 93, 75, 95, 75, 93, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4264.125, 4179.7974, 84.32761, 4044.1055, 135.69182, 51.020115, 33.307487, 3913.6365, 130.46906, 109.525246, 26.166573, 28.525805, 22.494312, 26.450958, 6.8565307], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.000626849, 0.024384508, -0.12794623, -0.15233561, 0.107195154, 0.469866, -0.29692024, -0.004873419, -0.04718757, 0.024521125, -0.00011199289, 0.016426856, 0.08190628, -0.0571057, -0.00015004507], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 94, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [13.572398, 52.14859, 70.82964, 37.61968, 36.270927, 16.363358, 44.233425, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 2445.92, 2.0, 1165.28, 1800.99, -0.004873419, -0.04718757, 0.024521125, -0.00011199289, 0.016426856, 0.08190628, -0.0571057, -0.00015004507], "split_indices": [61, 56, 56, 42, 56, 50, 58, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4260.1323, 3561.332, 698.8002, 1136.076, 2425.2559, 153.59975, 545.20044, 858.79095, 277.28506, 1065.8613, 1359.3945, 82.69774, 70.90201, 282.30267, 262.89777], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0006304657, -0.031302106, 0.113135345, 0.017935418, -0.40136164, 0.45635504, -0.11837076, -0.002615797, 0.024991408, -0.060406186, 0.01663915, -0.06645131, 0.056556053, -0.06618044, 0.0032856227], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 95, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [14.833035, 61.014095, 71.77953, 30.242447, 45.353413, 44.909203, 44.389305, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [534.3575, 3.0, 6.0, 397.95374, 1082.91, 1.0, 970.6786, -0.002615797, 0.024991408, -0.060406186, 0.01663915, -0.06645131, 0.056556053, -0.06618044, 0.0032856227], "split_indices": [51, 56, 56, 35, 58, 48, 51, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4248.8887, 3347.1978, 901.6908, 2954.9805, 392.2174, 362.8201, 538.87067, 2483.7654, 471.21494, 288.981, 103.23643, 31.759586, 331.06052, 116.57665, 422.29404], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.00066238985, -0.08329674, 0.038828027, -0.016222063, -0.34252357, 0.086823516, -0.1193009, -0.0086050965, 0.044718843, -0.07353412, -0.013496366, 0.0032004192, 0.028574571, 0.03643883, -0.02492935], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 96, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [13.814065, 23.795425, 21.749355, 35.262363, 22.838657, 23.95936, 42.05301, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [124.65, 2680.0, 1.0, 310316.0, 690.731, 48.0, 1.0, -0.0086050965, 0.044718843, -0.07353412, -0.013496366, 0.0032004192, 0.028574571, 0.03643883, -0.02492935], "split_indices": [58, 81, 61, 80, 95, 83, 56, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4231.213, 1367.8516, 2863.3613, 1087.5195, 280.332, 2196.9817, 666.37964, 945.8141, 141.70547, 96.03607, 184.29591, 1723.2471, 473.73453, 140.77332, 525.6063], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0006414017, -0.019823326, 0.1660921, -0.0017035954, -0.40267974, 0.37291664, -0.10430668, -0.0013902728, 0.05231703, -0.016873708, -0.07013855, 0.027212048, 0.10565963, -0.054667708, 0.015743436], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 97, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [13.5197735, 26.298727, 24.430386, 23.190838, 11.852751, 16.757408, 22.062721, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1785.69, 2.0, 10.0, 21997.84, 5.0, 1532.9109, 16266.25, -0.0013902728, 0.05231703, -0.016873708, -0.07013855, 0.027212048, 0.10565963, -0.054667708, 0.015743436], "split_indices": [2, 16, 48, 50, 48, 43, 70, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4225.186, 3790.0405, 435.1457, 3619.7312, 170.30927, 246.3424, 188.80327, 3538.4639, 81.267555, 96.38591, 73.923355, 215.90862, 30.433786, 69.779625, 119.02365], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0006006559, 0.003354286, -0.7969871, -0.021822067, 0.12948686, -1.109519, 0.35160393, 0.0001634154, -0.045138817, 0.026059046, -0.022407036, -0.117011644, -0.010123464, 0.09793927, -0.06568752], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 98, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [13.295963, 13.342344, 7.8783607, 35.292793, 32.46341, 0.8981762, 3.949034, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1213.7, 422.50375, 6.0, 488.09332, 1286.205, 4.0, 8.0, 0.0001634154, -0.045138817, 0.026059046, -0.022407036, -0.117011644, -0.010123464, 0.09793927, -0.06568752], "split_indices": [94, 85, 97, 76, 77, 91, 72, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4219.545, 4199.6846, 19.860302, 3501.5867, 698.0978, 15.638956, 4.221345, 3321.229, 180.35768, 509.45004, 188.64774, 14.600943, 1.0380138, 2.619513, 1.601832], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0006236891, -0.076073855, 0.041159704, -0.0005452567, -0.572999, 0.44804198, -0.0067101023, -0.0027770693, 0.11145605, -0.06290241, 0.25492153, 0.035118993, 0.19224958, -0.06211698, 0.0011479662], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 99, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [13.309324, 56.47329, 52.924534, 39.715115, 36.32972, 40.17101, 27.189825, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [35.0, 26.0, 17.0, 310316.0, 694131.0, 9173.62, 1443.46, -0.0027770693, 0.11145605, -0.06290241, 0.25492153, 0.035118993, 0.19224958, -0.06211698, 0.0011479662], "split_indices": [91, 91, 92, 80, 80, 70, 96, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [4219.753, 1503.6761, 2716.077, 1306.1527, 197.52353, 285.02982, 2431.047, 1275.9747, 30.177975, 194.8261, 2.6974325, 268.62302, 16.406786, 68.943695, 2362.1033], "tree_param": {"num_deleted": "0", "num_feature": "98", "num_nodes": "15", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "9.584592E-2", "boost_from_average": "1", "num_class": "0", "num_feature": "98", "num_target": "1"}, "objective": {"name": "binary:logistic", "reg_loss_param": {"scale_pos_weight": "1"}}}, "version": [3, 0, 1]}