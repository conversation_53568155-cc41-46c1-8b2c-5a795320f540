import pandas as pd
import os
import json
json_path = './json_data/adgroup_report_data.json'

def json_to_hourly_dataframe(json_file_path: str, include_metadata: bool = True) -> pd.DataFrame:
    # Load JSON data
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except FileNotFoundError:
        raise FileNotFoundError(f"JSON file not found: {json_file_path}")
    except json.JSONDecodeError as e:
        raise ValueError(f"Invalid JSON format: {e}")
    
    rows = []
    
    # Iterate through accounts
    for account_id, account_data in data.items():
        # Iterate through adgroups
        for adgroup_id, adgroup_data in account_data.items():
            
            # Extract metadata
            ds = adgroup_data.get('ds', '')
            adgroup_name = adgroup_data.get('adgroup_name', '')
            
            # Extract today's hourly report
            hourly_report = adgroup_data.get('today_hourly_report', {})
            
            # Get hourly data arrays
            hours = hourly_report.get('hour', [])
            view_counts = hourly_report.get('view_count', [])
            valid_click_counts = hourly_report.get('valid_click_count', [])
            costs = hourly_report.get('cost', [])
            reg_pvs = hourly_report.get('reg_pv', [])
            apply_pvs = hourly_report.get('apply_pv', [])
            credit_pvs = hourly_report.get('credit_pv', [])
            acquisition_costs = hourly_report.get('acquisition_cost', [])
            
            # Check if all arrays have the same length
            arrays = [hours, view_counts, valid_click_counts, costs, reg_pvs, apply_pvs, credit_pvs, acquisition_costs]
            array_lengths = [len(arr) for arr in arrays]
            
            if len(set(array_lengths)) > 1:
                print(f"Warning: Inconsistent array lengths for account {account_id}, adgroup {adgroup_id}: {array_lengths}")
                # Use the minimum length to avoid index errors
                min_length = min(array_lengths) if array_lengths else 0
            else:
                min_length = array_lengths[0] if array_lengths else 0
            
            # Create rows for each hour
            for i in range(min_length):
                row = {
                    'account_id': int(account_id),
                    'adgroup_id': int(adgroup_id),
                    'hour': hours[i] if i < len(hours) else None,
                    # 'date': ds,
                    'view_count': view_counts[i] if i < len(view_counts) else 0,
                    'valid_click_count': valid_click_counts[i] if i < len(valid_click_counts) else 0,
                    'cost': costs[i] if i < len(costs) else 0.0,
                    'reg_pv': reg_pvs[i] if i < len(reg_pvs) else 0,
                    'apply_pv': apply_pvs[i] if i < len(apply_pvs) else 0,
                    'credit_pv': credit_pvs[i] if i < len(credit_pvs) else 0,
                    'acquisition_cost': acquisition_costs[i] if i < len(acquisition_costs) else 0.0
                }
                
                # Add metadata if requested
                if include_metadata:
                    row['life_long_days'] = adgroup_data.get('life_long_days', 0)
                    row['ds'] = adgroup_data.get('ds', '')
                    row['system_status'] = adgroup_data.get('system_status', '')
                
                rows.append(row)
    
    # Create DataFrame
    df = pd.DataFrame(rows)
    
    # Convert data types
    if not df.empty:
        # Convert date column to datetime
        if 'date' in df.columns:
            df['date'] = pd.to_datetime(df['date'], errors='coerce')
        
        # Ensure numeric columns are numeric
        numeric_columns = ['view_count', 'valid_click_count', 'cost', 'reg_pv', 'apply_pv', 'credit_pv', 'acquisition_cost']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
        
        # Sort by account_id, adgroup_id, date, hour
        sort_columns = ['account_id', 'adgroup_id']
        if 'date' in df.columns:
            sort_columns.append('date')
        if 'hour' in df.columns:
            sort_columns.append('hour')
        
        df = df.sort_values(sort_columns).reset_index(drop=True)
    
    return df

def main():
    """Example usage of the functions."""
    json_file = 'json_data/adgroup_report_data.json'
    
    try:
        # Convert to hourly DataFrame
        print("Converting JSON to hourly DataFrame...")
        hourly_df = json_to_hourly_dataframe(json_file)
        print(f"Hourly DataFrame shape: {hourly_df.shape}")
        print(f"Columns: {list(hourly_df.columns)}")
        print("\nFirst 5 rows of hourly data:")
        print(hourly_df.head())
        
    except Exception as e:
        print(f"Error: {e}")


if __name__ == '__main__':
    main()
