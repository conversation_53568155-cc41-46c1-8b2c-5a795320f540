import pymysql
import time
import numpy as np
import pandas as pd
import json
import datetime

database_ip = '************'
database_port = 3306
database_name = 'aimadb_huanbei'
user = 'app'
password = 'App1234.'

def get_video_ad_data():
    try:
        db = pymysql.connect(host=database_ip, port=int(database_port), user=user, passwd=password, db=database_name, charset="utf8", connect_timeout = 3600)
        cursor = db.cursor()
        table_name = 'aiams_media_account_material_info'
        try:
            db.ping()
        except:
            print ('--- 重新连接数据库...')
            db = pymysql.connect(host=database_ip, port=int(database_port), user=user, passwd=password, db=database_name, charset="utf8", connect_timeout = 3600)
            cursor = db.cursor()
        feature_list = ['account_id' ,'signature' ,'big_category' ,'small_category', 'used', 'date']
        sql_cmd = 'SELECT %s from %s' % (','.join(feature_list), table_name)
            # sql_cmd = 'DESCRIBE %s;' % table_name
        print ('--- sql_cmd: ', sql_cmd)
        t = time.time()
        cursor.execute(sql_cmd)
        results = cursor.fetchall()
        print ('--- get results length: ', len(results))
        print ('--- get data time: ', time.time() - t)
    except:
        print ('--- 拉取数据失败 ...')
        exit()

    video_data = {}
    for f in feature_list:
        video_data[f] = []
    # video_data = {'video_id':[], 'signature':[], 'date':[], 'ctr':[]}
    for row in results:
        # print (row)
        for i in range(len(feature_list)):
            video_data[feature_list[i]].append(row[i])
        # cursor.execute('SELECT description from tencent_video_material where signature in (%s)' % signature)
        # results = cursor.fetchall()
        # print (row)
        # break

    video_data = pd.DataFrame(video_data)
    # video_data.to_csv('label_data.csv', encoding='utf8')

    print(f"-- video cateogry distribution info per account")
    for account_id in video_data['account_id'].unique():
        account_data = video_data[video_data['account_id'] == account_id]
        print(f"account_id: {account_id}")
        print(account_data['big_category'].value_counts() / len(account_data))
        print(account_data['small_category'].value_counts() / len(account_data))


    print(f"-- filter for unused ones")
    print(f"before filtering length: {len(video_data)}")
    video_data = video_data[video_data['used'] == 0]
    print(f"after filtering length: {len(video_data)}")
    for account_id in video_data['account_id'].unique():
        account_data = video_data[video_data['account_id'] == account_id]
        print(f"account_id: {account_id}")
        print(f"total materials: {len(account_data)}")
        print(account_data['big_category'].value_counts() / len(account_data))
        print(account_data['small_category'].value_counts() / len(account_data))
    
    print(f"-- random selection experiment")
    # random select 8 videos for 1000 times, check distribution of each categories
    video_data = video_data[video_data['account_id'].astype(int) == ********]
    big_category_count = {k: 0 for k in video_data['big_category'].unique()}
    small_category_count = {k: 0 for k in video_data['small_category'].unique()}
    all_different_small_categoris_count = 0
    all_different_big_categoris_count = 0
    for i in range(1000):
        selected_videos = video_data.sample(8, replace=False)
        for big_category in selected_videos['big_category'].unique():
            big_category_count[big_category] += 1
        for small_category in selected_videos['small_category'].unique():
            small_category_count[small_category] += 1
        if len(selected_videos['small_category'].unique()) == 8:
            all_different_small_categoris_count += 1
        if len(selected_videos['big_category'].unique()) == 8:
            all_different_big_categoris_count += 1
    print({k: v/1000 for k, v in big_category_count.items()})
    print({k: v/1000 for k, v in small_category_count.items()})
    print(f"all different big categories: {all_different_big_categoris_count/1000}")
    print(f"all different small categories: {all_different_small_categoris_count/1000}")
    
    # try shuffle till all small different, record number of tries
    for i in range(10000):
        selected_videos = video_data.sample(8, replace=False)
        if (len(selected_videos['small_category'].unique()) == 8) and (len(selected_videos['big_category'].unique()) > 4):
            print(f"all small different and 4 different big: {i}")
            print(f"big categories: {selected_videos['big_category'].value_counts()}, small categories: {selected_videos['small_category'].value_counts()}")
            break


if __name__ == '__main__':
    get_video_ad_data()