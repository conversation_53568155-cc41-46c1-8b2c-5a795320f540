import pandas as pd 
import numpy as np
import os 
import json
import xgboost as xgb
from aggregate_adgroup_data import load_and_parse_data, aggregate_by_adgroup, calculate_daily_totals, calculate_historical_totals
from budget_control_model import hourly_feature_engineering, daily_feature_engineering, prepare_data, evaluate_model
from test_budget_control_effect import filter_stopped_adgroups_hourly_entry, calculate_overall_metrics


def main():
    source_dir = 'data_20250323-20250622'
    # source_dir = 'data_20250323-20250622-full'
    save_dir = 'data_20250623-20250709'
    score_thres = 0.7
    restart_hour = 5
    print(f"===Config: score threshold: {score_thres}, restart hour: {restart_hour}")
    csv_file = 'test_samples/广告转化数据20250623-20250709.csv'
    
    import os

    df = load_and_parse_data(csv_file)
    
    # Convert date to datetime
    df['date'] = pd.to_datetime(df['date'])
    
    # Aggregate by adgroup
    print("Aggregating data by adgroup...")
    hourly_data = aggregate_by_adgroup(df)


    print("Calculating daily totals...")
    daily_totals = calculate_daily_totals(df)
    
    print("Calculating historical totals...")
    past_1day = calculate_historical_totals(hourly_data, 1)
    past_3day = calculate_historical_totals(hourly_data, 3)
    past_7day = calculate_historical_totals(hourly_data, 7)
    
    # Merge all data together
    print("Merging all data...")
    daily_data = daily_totals.copy()
    
    daily_data = daily_data.merge(past_1day, on=['account_id', 'adgroup_id', 'date'], how='left')
    daily_data = daily_data.merge(past_3day, on=['account_id', 'adgroup_id', 'date'], how='left')
    daily_data = daily_data.merge(past_7day, on=['account_id', 'adgroup_id', 'date'], how='left')

    print(f"Columns after merge: {list(daily_data.columns)}")
    
    daily_data = daily_data.fillna(0)
    
    # Sort by account_id, adgroup_id, date, hour (if hour column exists)
    sort_columns = ['account_id', 'adgroup_id', 'date']
    if 'hour' in daily_data.columns:
        sort_columns.append('hour')
    daily_data = daily_data.sort_values(sort_columns)

    hourly_data = hourly_feature_engineering(hourly_data)
    daily_data = daily_feature_engineering(daily_data)
    n = 0 # train for next n days, 0 = same day prediction
    print(f"===Preparing data for next {n} day...")
    full_data = prepare_data(hourly_data, daily_data, n)
    full_data.to_csv(os.path.join(save_dir, f'{n}_day_merged_data.csv'), index=False)
    label_col = f'{n}_day_over_credit_cost_thres'
    target = full_data[label_col]
    target = target.dropna()
    features = full_data.drop([label_col], axis=1)
    features = features.loc[target.index]

    model = xgb.XGBClassifier()
    model.load_model(os.path.join(source_dir, f'{n}_day_model.json'))
    res_df = evaluate_model(model, features, target, 'New Data')
    res_df.to_csv(os.path.join(save_dir, f'{n}_day_predict_result_new.csv'))

    hourly_data['ds'] = pd.to_datetime(hourly_data['date']) + pd.to_timedelta(hourly_data['hour'], unit='h')
    filtered_data = filter_stopped_adgroups_hourly_entry(hourly_data, res_df, score_thres, restart_hour, use_budget_control=True)
    org_metrics = calculate_overall_metrics(hourly_data)

    print(f"\nOverall metrics for original data:")
    print(json.dumps(org_metrics))

    filtered_metrics = calculate_overall_metrics(filtered_data)
    print(f"\nOverall metrics for filtered data:")
    print(json.dumps(filtered_metrics))

    cost_diff = (filtered_metrics['cost'] - org_metrics['cost']) / org_metrics['cost']
    credit_pv_diff = (filtered_metrics['credit_pv'] - org_metrics['credit_pv']) / org_metrics['credit_pv']
    credit_cost_diff = (filtered_metrics['credit_cost'] - org_metrics['credit_cost']) / org_metrics['credit_cost']
    print(f"\nCost diff ratio: {cost_diff * 100:.2f}%") # percentage
    print(f"Credit PV diff ratio: {credit_pv_diff * 100:.2f}%")
    print(f"Credit cost diff ratio: {credit_cost_diff * 100:.2f}%")

if __name__ == "__main__":
    main()