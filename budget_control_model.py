import xgboost as xgb
import pandas as pd
import numpy as np
from sklearn.metrics import roc_auc_score, f1_score
from scipy.stats import ks_2samp

# cost per pv thresholds
reg_cost_thres = 220
apply_cost_thres = 701
credit_cost_thres =  2588


# Feature Engineering
def daily_feature_engineering(data):
    data['date'] = pd.to_datetime(data['date'])
    data['day_of_week'] = data['date'].dt.dayofweek

    return data


"""def create_label_for_next_n_day(daily_data, n_day, threshold):
    result_data = daily_data.copy()
    result_data['date'] = pd.to_datetime(result_data['date'])
    result_data = result_data.sort_values(['account_id', 'adgroup_id', 'date'])
    label_col = f'{n_day}_day_over_credit_cost_thres'
    result_data[label_col] = np.nan
    grouped = result_data.groupby(['account_id', 'adgroup_id'])

    rows_to_keep = []

    for (account_id, adgroup_id), group in grouped:
        # Keep original indices before reset
        original_indices = group.index.tolist()
        group = group.reset_index(drop=True)

        for i in range(len(group)):
            current_date = group.loc[i, 'date']
            target_date = current_date + pd.Timedelta(days=n_day)
            future_row = group[group['date'] == target_date]

            if not future_row.empty:
                future_credit_cost = future_row.iloc[0]['cur_day_credit_cost']

                if pd.isna(future_credit_cost):
                    future_credit_cost = 0

                label_value = 1 if future_credit_cost > threshold else 0
                # Use original index to update result_data
                original_idx = original_indices[i]
                result_data.loc[original_idx, label_col] = label_value

                rows_to_keep.append(original_idx)

    # result_data = result_data.loc[rows_to_keep]

    result_data = result_data.reset_index(drop=True)

    return result_data"""


def create_label_for_next_n_day(daily_data, n_day, threshold):
    result_data = daily_data.copy()
    result_data['date'] = pd.to_datetime(result_data['date'])
    result_data = result_data.sort_values(['account_id', 'adgroup_id', 'date'])
    label_col = f'{n_day}_day_no_credit'
    result_data[label_col] = np.nan
    grouped = result_data.groupby(['account_id', 'adgroup_id'])

    rows_to_keep = []

    for (account_id, adgroup_id), group in grouped:
        # Keep original indices before reset
        original_indices = group.index.tolist()
        group = group.reset_index(drop=True)

        for i in range(len(group)):
            current_date = group.loc[i, 'date']
            target_date = current_date + pd.Timedelta(days=n_day)
            future_row = group[group['date'] == target_date]

            if not future_row.empty:
                # future_credit_cost = future_row.iloc[0]['cur_day_credit_cost']
                future_credit_pv = future_row.iloc[0]['cur_day_credit_pv']

                if pd.isna(future_credit_pv):
                    future_credit_pv = 0

                label_value = 1 if future_credit_pv == 0 else 0
                # Use original index to update result_data
                original_idx = original_indices[i]
                result_data.loc[original_idx, label_col] = label_value

                rows_to_keep.append(original_idx)

    # result_data = result_data.loc[rows_to_keep]

    result_data = result_data.reset_index(drop=True)

    return result_data


def hourly_feature_engineering(hourly_data):
    """
    Ultra-efficient hourly feature engineering using pandas time-based rolling.
    """
    result_data = hourly_data.copy()
    # result_data['is_auto_acquisition'] = (result_data['acquisition_cost'] * 0.01 > 100).astype(int)
    if 'acquisition_cost' in result_data.columns:
        result_data.drop(['acquisition_cost'], axis=1, inplace=True)
    # Create cyclic features
    result_data['hour_sin'] = np.sin(2 * np.pi * result_data['hour'] / 24)
    result_data['hour_cos'] = np.cos(2 * np.pi * result_data['hour'] / 24)

    result_data['date'] = pd.to_datetime(result_data['date'])
    result_data['ds'] = result_data['date'] + pd.to_timedelta(result_data['hour'], unit='h')
    result_data = result_data.sort_values(['account_id', 'adgroup_id', 'ds']).reset_index(drop=True)
    
    metrics = ['cost', 'reg_pv', 'apply_pv', 'credit_pv']
    hour_windows = [1, 2, 4, 6, 8, 12, 16]

    """
    def calculate_group_features(group):
        group = group.sort_values('ds')
        # group['ds'] = pd.to_datetime(group['ds'])
        for window in hour_windows:
            for metric in metrics:
                col_name = f'cum_{window}h_up_to_cur_h_{metric}'
                group[col_name] = 0

                for i, current_time in enumerate(group['ds']):
                    time_window_start = current_time - pd.Timedelta(hours=window)
                    group[col_name].iloc[i] = group.loc[(group['ds'] >= time_window_start) & (group['ds'] < current_time), metric].sum()

        return group
    """

    def calculate_group_features(group):
        group = group.set_index('ds').sort_index()

        for window in hour_windows:
            for metric in metrics:
                # calculate cumulative sum
                col_name = f'cum_{window}h_up_to_cur_h_{metric}'
                group[col_name] = group[metric].rolling(
                    window=f'{window}h',
                    min_periods=1,
                    closed='left'
                ).sum().fillna(0)
                # calculate mean
                col_name = f'mean_{window}h_up_to_cur_h_{metric}'
                group[col_name] = group[metric].rolling(
                    window=f'{window}h',
                    min_periods=1,
                    closed='left'
                ).mean().fillna(0)

        return group.reset_index()

    # Process each adgroup separately with vectorized operations
    result_list = []
    for (account_id, adgroup_id), group in result_data.groupby(['account_id', 'adgroup_id']):
        processed_group = calculate_group_features(group)
        result_list.append(processed_group)

    # Concatenate all results
    result_data = pd.concat(result_list, ignore_index=True)
    # print(f"Total rows after cumulative sum: {result_data.shape[0]}")

    # Create features for metrics of previous hour
    for metric in metrics:
        sub_data = result_data.sort_values(by=['account_id', 'adgroup_id', 'ds'])
        merged = pd.merge(
                sub_data,
                sub_data,
                left_on=['account_id', 'adgroup_id', 'ds'],
                right_on=['account_id', 'adgroup_id', sub_data['ds'] - pd.Timedelta(hours=1)],
                suffixes=('', '_prev'),
                how='left'
            )
        merged = merged.fillna(0)
        result_data[f'prev_h_{metric}'] = merged[f"{metric}_prev"]
    
    # print(f"Total rows after adding previous hour features: {result_data.shape[0]}")

    # Create squared, cubed features for metrics of previous hour
    for metric in [f"prev_h_{m}" for m in metrics]:
        result_data[f'prev_h_{metric}_squared'] = result_data[metric] ** 2
        result_data[f'prev_h_{metric}_cubed'] = result_data[metric] ** 3
    
    return result_data


def split_data(data, label_col, n_weeks=2):
    # Remove rows with NaN in target variable
    data = data[data[label_col].notna()]
    
    data['date'] = pd.to_datetime(data['date'])
    
    
    if n_weeks == 0:
        test_start_date = data['date'].max() + pd.DateOffset(days=2)
    
    else:
        test_start_date = data['date'].max() - pd.DateOffset(weeks=n_weeks)
        print(f"Test start date: {test_start_date}") # 2025-06-23 00:00:00
        # print(f"Test start date: {test_start_date}") # 2025-06-08 00:00:00
        # test_start_date = data['date'].max() - pd.DateOffset(weeks=1)
        # print(f"Test start date: {test_start_date}") # 2025-06-15 00:00:00
    test_data = data[data['date'] >= test_start_date]
    train_data = data[data['date'] < test_start_date]

    # Split the data into features and target
    # train_features = train_data.drop(['account_id', 'adgroup_id', 'date', 'hour', 'ds', label_col], axis=1)
    train_target = train_data[label_col]
    train_data = train_data.drop([label_col], axis=1)
    # test_features = test_data.drop(['account_id', 'adgroup_id', 'date', 'hour', 'ds', label_col], axis=1)
    test_target = test_data[label_col]
    test_data = test_data.drop([label_col], axis=1)
    print(f"label col: {label_col} \ntrain 1:0 counts: {train_target.sum()} vs {train_target.shape[0] - train_target.sum()} \n"
          f"test 1:0 counts: {test_target.sum()} vs {test_target.shape[0] - test_target.sum()}")

    return train_data, train_target, test_data, test_target


def prepare_data(hourly_data, daily_data, nday=1):
    hourly_data = hourly_data.copy()
    daily_data = daily_data.copy()
    # drop low cost entries
    daily_data = daily_data[daily_data['cur_day_cost'] > reg_cost_thres]
    # create label
    daily_data = create_label_for_next_n_day(daily_data, nday, credit_cost_thres)
    print(f"Total rows in daily data before filtering: {daily_data.shape[0]}")

    """
    print("cur_day_credit_cost vs label comparison:")
    comparison_df = daily_data[daily_data['cur_day_credit_cost'] > 0]
    comparison_df = comparison_df[['cur_day_credit_cost', f'{nday}_day_over_credit_cost_thres']].head(40)
    print(comparison_df.to_string())
    """
    # daily_data = daily_data[daily_data[f'{nday}_day_over_credit_cost_thres'].notna()]
    daily_data = daily_data[daily_data[f'{nday}_day_no_credit'].notna()]
    print(f"Total rows in daily data after filtering: {daily_data.shape[0]}")
    # search for cols names containing 'cur_day_' and drop them
    cur_day_cols = [col for col in daily_data.columns if 'cur_day_' in col]
    daily_data.drop(cur_day_cols, axis=1, inplace=True)
    # daily_data.to_csv(f'aggregated_adgroup_data_{nday}_day.csv', index=False)
    hourly_data = hourly_data.sort_values(['account_id', 'adgroup_id', 'ds']).reset_index(drop=True)
    print(f"Total rows in hourly data: {hourly_data.shape[0]}")
    target_hours = [14, 15, 16, 17, 18, 19, 20]
    hourly_data = hourly_data[hourly_data['hour'].isin(target_hours)]
    print(f"Total rows in hourly data after filtering: {hourly_data.shape[0]}")
    # hourly_data.drop(['cost', 'acquisition_cost', 'view_count', 'valid_click_count', 'reg_pv', 'apply_pv', 'credit_pv', 'unique_dynamic_creative_count'], axis=1, inplace=True)
    hourly_data.drop(['cost', 'view_count', 'valid_click_count', 'reg_pv', 'apply_pv', 'credit_pv', 'unique_dynamic_creative_count'], axis=1, inplace=True)
    merged_data = hourly_data.merge(daily_data, on=['account_id', 'adgroup_id', 'date'], how='left')
    merged_data.to_csv(f'{nday}_day_merged_data.csv', index=False)
    print(f"Total rows in merged data: {merged_data.shape[0]}")
    return merged_data


def train_model(train_data, train_target):
    """ # exclude hours after 23:00 and before 6:00
    data = data[data['hour'] < 23]
    data = data[data['hour'] >= 6]
    """
    model = xgb.XGBClassifier(objective='binary:logistic', random_state=42,
                              n_estimators=100, max_depth=3, learning_rate=0.1)
    train_features = train_data.drop(['account_id', 'adgroup_id', 'date', 'hour', 'ds'], axis=1)
    model.fit(train_features, train_target)

    return model


def evaluate_model(model, data, target, data_type):
    if len(data) == 0:
        print(f"No data for {data_type}")
        return pd.DataFrame()
    # feature importance
    feature_importance = model.feature_importances_
    features = data.drop(['account_id', 'adgroup_id', 'date', 'hour', 'ds'], axis=1)
    importance_df = pd.DataFrame({
    'Feature': features.columns,
    'Importance': feature_importance}).sort_values(by='Importance', ascending=False)
    print(importance_df)

    y_pred = model.predict_proba(features)[:, 1]
    auc = roc_auc_score(target, y_pred)
    print(f"{data_type} AUC: {auc}")

    class0 = y_pred[target == 0]
    class1 = y_pred[target == 1]
    ks = ks_2samp(class0, class1)
    print(f"{data_type} KS: {ks.statistic:.4f} p-value: {ks.pvalue}")

    f1 = f1_score(target, (y_pred > 0.5).astype(int))
    print(f"{data_type} F1: {f1:.4f}")
    
    res_df = data.copy()
    res_df['predict_score'] = y_pred
    
    return res_df
    

def main(save_dir='data_20250323-********-full/'):
    print("===Loading data...")
    source_dir = 'data_20250323-********/'
    save_dir = 'data_20250323-********/'
    # save_dir = 'data_20250323-********-full/'

    import os

    hourly_data = pd.read_csv(os.path.join(source_dir, 'aggregated_adgroup_hourly_data.csv'))
    daily_data = pd.read_csv(os.path.join(source_dir, 'aggregated_adgroup_daily_data.csv'))
    print("===Feature engineering...")
    hourly_data = hourly_feature_engineering(hourly_data)
    hourly_data.to_csv(os.path.join(source_dir, 'feature_aggregated_adgroup_hourly_data.csv'), index=False)
    daily_data = daily_feature_engineering(daily_data)
    daily_data.to_csv(os.path.join(source_dir, 'feature_aggregated_adgroup_daily_data.csv'), index=False)
    n = 0 # train for next n days, 0 = same day prediction
    print(f"===Preparing data for next {n} day...")
    full_data = prepare_data(hourly_data, daily_data, n)
    # train_data, train_target, test_data, test_target = split_data(full_data, f'{n}_day_over_credit_cost_thres', n_weeks=2)
    train_data, train_target, test_data, test_target = split_data(full_data, 
                                                                  # f'{n}_day_over_credit_cost_thres', 
                                                                  f'{n}_day_no_credit',
                                                                  n_weeks=2)

    print(f"===Training for next {n} day...\n")
    model = train_model(train_data, train_target)
    model.save_model(os.path.join(save_dir, f'{n}_day_model.json'))
    # use previously saved model
    # model = xgb.Booster()
    # model.load_model(os.path.join(save_dir, f'{n}_day_model.json'))
    res_df = evaluate_model(model, train_data, train_target, 'Train')
    res_df.to_csv(os.path.join(save_dir, f'{n}_day_predict_result_train.csv'))
    if len(test_data) == 0:
        return
    res_df = evaluate_model(model, test_data, test_target, 'Test')
    res_df.to_csv(os.path.join(save_dir, f'{n}_day_predict_result_test.csv'))


if __name__ == "__main__":
    main()