"""
{
    "account_id": {
        "adgroup_id": {
            "ds": "2025-07-18",
            "adgroup_name": "...",
            "today_hourly_report": {
                "hour": [9, 10, 11, ...],
                "view_count": [4, 3, 1, ...],
                "valid_click_count": [0, 0, 0, ...],
                "cost": [0.0, 0.0, 0.0, ...],
                "reg_pv": [0, 0, 0, ...],
                "apply_pv": [0, 0, 0, ...],
                "credit_pv": [0, 0, 0, ...],
                "acquisition_cost": [0.0, 0.0, 0.0, ...]
            },
            ... other fields ...
        }
    }
}
"""

import json
import pandas as pd


def json_to_hourly_dataframe(json_file_path: str, include_metadata: bool = True) -> pd.DataFrame:
    """
    Convert adgroup report JSON data to pandas DataFrame with hourly data.
    
    Parameters:
    json_file_path (str): Path to the JSON file
    include_metadata (bool): Whether to include metadata columns like adgroup_name, ds, etc.
    
    Returns:
    pd.DataFrame: DataFrame with columns:
        - account_id: Account ID
        - adgroup_id: Adgroup ID  
        - hour: Hour of the day
        - date: Date (from ds field)
        - adgroup_name: Adgroup name (if include_metadata=True)
        - view_count: View count for that hour
        - valid_click_count: Valid click count for that hour
        - cost: Cost for that hour
        - reg_pv: Registration PV for that hour
        - apply_pv: Application PV for that hour
        - credit_pv: Credit PV for that hour
        - acquisition_cost: Acquisition cost for that hour
    """
    
    # Load JSON data
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except FileNotFoundError:
        raise FileNotFoundError(f"JSON file not found: {json_file_path}")
    except json.JSONDecodeError as e:
        raise ValueError(f"Invalid JSON format: {e}")
    
    # List to store all rows
    rows = []
    
    # Iterate through accounts
    for account_id, account_data in data.items():
        # Iterate through adgroups
        for adgroup_id, adgroup_data in account_data.items():
            
            # Extract metadata
            ds = adgroup_data.get('ds', '')
            adgroup_name = adgroup_data.get('adgroup_name', '')
            
            # Extract today's hourly report
            hourly_report = adgroup_data.get('today_hourly_report', {})
            
            # Get hourly data arrays
            hours = hourly_report.get('hour', [])
            view_counts = hourly_report.get('view_count', [])
            valid_click_counts = hourly_report.get('valid_click_count', [])
            costs = hourly_report.get('cost', [])
            reg_pvs = hourly_report.get('reg_pv', [])
            apply_pvs = hourly_report.get('apply_pv', [])
            credit_pvs = hourly_report.get('credit_pv', [])
            acquisition_costs = hourly_report.get('acquisition_cost', [])
            
            # Check if all arrays have the same length
            arrays = [hours, view_counts, valid_click_counts, costs, reg_pvs, apply_pvs, credit_pvs, acquisition_costs]
            array_lengths = [len(arr) for arr in arrays]
            
            if len(set(array_lengths)) > 1:
                print(f"Warning: Inconsistent array lengths for account {account_id}, adgroup {adgroup_id}: {array_lengths}")
                # Use the minimum length to avoid index errors
                min_length = min(array_lengths) if array_lengths else 0
            else:
                min_length = array_lengths[0] if array_lengths else 0
            
            # Create rows for each hour
            for i in range(min_length):
                row = {
                    'account_id': int(account_id),
                    'adgroup_id': int(adgroup_id),
                    'hour': hours[i] if i < len(hours) else None,
                    'date': ds,
                    'view_count': view_counts[i] if i < len(view_counts) else 0,
                    'valid_click_count': valid_click_counts[i] if i < len(valid_click_counts) else 0,
                    'cost': costs[i] if i < len(costs) else 0.0,
                    'reg_pv': reg_pvs[i] if i < len(reg_pvs) else 0,
                    'apply_pv': apply_pvs[i] if i < len(apply_pvs) else 0,
                    'credit_pv': credit_pvs[i] if i < len(credit_pvs) else 0,
                    'acquisition_cost': acquisition_costs[i] if i < len(acquisition_costs) else 0.0
                }
                
                # Add metadata if requested
                if include_metadata:
                    row['adgroup_name'] = adgroup_name
                    row['created_time'] = adgroup_data.get('created_time', '')
                    row['system_status'] = adgroup_data.get('system_status', '')
                    row['first_day_begin_time'] = adgroup_data.get('first_day_begin_time', '')
                
                rows.append(row)
    
    # Create DataFrame
    df = pd.DataFrame(rows)
    
    # Convert data types
    if not df.empty:
        # Convert date column to datetime
        if 'date' in df.columns:
            df['date'] = pd.to_datetime(df['date'], errors='coerce')
        
        # Ensure numeric columns are numeric
        numeric_columns = ['view_count', 'valid_click_count', 'cost', 'reg_pv', 'apply_pv', 'credit_pv', 'acquisition_cost']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
        
        # Sort by account_id, adgroup_id, date, hour
        sort_columns = ['account_id', 'adgroup_id']
        if 'date' in df.columns:
            sort_columns.append('date')
        if 'hour' in df.columns:
            sort_columns.append('hour')
        
        df = df.sort_values(sort_columns).reset_index(drop=True)
    
    return df

