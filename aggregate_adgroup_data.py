import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from collections import defaultdict
import sys

def load_and_parse_data(csv_file):
    try:
        df = pd.read_csv(csv_file)
        print(f"Loaded {len(df)} rows from {csv_file}")
        print(f"Columns: {list(df.columns)}")
        return df
    except Exception as e:
        print(f"Error loading CSV file: {e}")
        sys.exit(1)

def aggregate_by_adgroup(df):
    df['cost'] = df['cost'] * 0.01
    grouped = df.groupby(['account_id', 'adgroup_id', 'date', 'hour'])    
    aggregated = grouped.agg({
        'dynamic_creative_id': 'nunique',  # Count of unique dynamic_creative_ids
        'cost': 'sum',
        # 'acquisition_cost': 'sum',
        'view_count': 'sum',
        'valid_click_count': 'sum',
        'reg_pv': 'sum',
        'apply_pv': 'sum',
        'credit_pv': 'sum'
    }).reset_index()
    
    # Rename col name
    aggregated.rename(columns={'dynamic_creative_id': 'unique_dynamic_creative_count'}, inplace=True)
    
    return aggregated

def calculate_daily_totals(df):
    daily_totals = df.groupby(['account_id', 'adgroup_id', 'date']).agg({
        'cost': 'sum',
        # 'acquisition_cost': 'sum',
        'view_count': 'sum',
        'valid_click_count': 'sum',
        'reg_pv': 'sum',
        'apply_pv': 'sum',
        'credit_pv': 'sum'
    }).reset_index()
    
    # avoid zero division 
    daily_totals['reg_cost'] = np.where(daily_totals['reg_pv'] == 0, 0, daily_totals['cost'] / daily_totals['reg_pv'])
    daily_totals['apply_cost'] = np.where(daily_totals['apply_pv'] == 0, 0, daily_totals['cost'] / daily_totals['apply_pv'])
    daily_totals['credit_cost'] = np.where(daily_totals['credit_pv'] == 0, 0, daily_totals['cost'] / daily_totals['credit_pv'])
    #daily_totals['reg_cost'] = daily_totals['cost'].div(daily_totals['reg_pv'], 0)
    #daily_totals['apply_cost'] = daily_totals['cost'].div(daily_totals['apply_pv'], 0)
    #daily_totals['credit_cost'] = daily_totals['cost'].div(daily_totals['credit_pv'], 0)
    
    # metric_columns = ['cost', 'acquisition_cost', 'view_count', 'valid_click_count', 'reg_pv', 'apply_pv', 'credit_pv', 'reg_cost', 'apply_cost', 'credit_cost']
    metric_columns = ['cost', 'view_count', 'valid_click_count', 'reg_pv', 'apply_pv', 'credit_pv', 'reg_cost', 'apply_cost', 'credit_cost']

    rename_dict = {col: f'cur_day_{col}' for col in metric_columns}
    daily_totals = daily_totals.rename(columns=rename_dict)

    return daily_totals

def calculate_historical_totals(df, days_back):
    df['date'] = pd.to_datetime(df['date'])

    historical_data = []

    grouped_data = df.groupby(['account_id', 'adgroup_id'])

    for (account_id, adgroup_id), group_data in grouped_data:
        dates_for_group = sorted(group_data['date'].unique())

        for current_date in dates_for_group:
            start_date = current_date - timedelta(days=days_back)
            end_date = current_date - timedelta(days=1)  # Exclude current day
            historical_period_data = group_data[
                (group_data['date'] >= start_date) &
                (group_data['date'] <= end_date)
            ]

            if len(historical_period_data) > 0:
                totals = historical_period_data.agg({
                    'cost': 'sum',
                    # 'acquisition_cost': 'sum',
                    'view_count': 'sum',
                    'valid_click_count': 'sum',
                    'reg_pv': 'sum',
                    'apply_pv': 'sum',
                    'credit_pv': 'sum'
                })

            else:
                # If no historical data, set all values to 0
                totals = pd.Series({
                    'cost': 0,
                    # 'acquisition_cost': 0,
                    'view_count': 0,
                    'valid_click_count': 0,
                    'reg_pv': 0,
                    'apply_pv': 0,
                    'credit_pv': 0,
                    
                })
            # calculate cost per pv
            if totals['reg_pv'] != 0:
                totals['reg_cost'] = totals['cost'] / totals['reg_pv']
            else:
                totals['reg_cost'] = 0
            if totals['apply_pv'] != 0:
                totals['apply_cost'] = totals['cost'] / totals['apply_pv']
            else:
                totals['apply_cost'] = 0
            if totals['credit_pv'] != 0:
                totals['credit_cost'] = totals['cost'] / totals['credit_pv']
            else:
                totals['credit_cost'] = 0
            
            historical_data.append({
                'account_id': account_id,
                'adgroup_id': adgroup_id,
                'date': current_date,
                **{f'past_{days_back}day_{col}': totals[col] for col in totals.index}
            })

    return pd.DataFrame(historical_data)

def main(save_dir='data_********-********/'):
    # csv_file = 'samples/广告转化数据********-********.csv'
    # save_dir = 'data_********-********/'
    csv_file = 'test_samples/广告转化数据********-********.csv'
    # save_dir = 'data_********-********/'
    import os
    
    # Load the data
    print("Loading data...")
    df = load_and_parse_data(csv_file)
    
    # Convert date to datetime
    df['date'] = pd.to_datetime(df['date'])
    
    # Aggregate by adgroup
    print("Aggregating data by adgroup...")
    aggregated = aggregate_by_adgroup(df)

    # write aggregated hourly report to csv
    aggregated.to_csv(os.path.join(save_dir, 'aggregated_adgroup_hourly_data.csv'), index=False)
    
    print("Calculating daily totals...")
    daily_totals = calculate_daily_totals(df)
    
    print("Calculating historical totals...")
    past_1day = calculate_historical_totals(aggregated, 1)
    past_3day = calculate_historical_totals(aggregated, 3)
    past_7day = calculate_historical_totals(aggregated, 7)
    
    # Merge all data together
    print("Merging all data...")
    result = daily_totals.copy()
    
    # Merge daily totals
    # result = result.merge(daily_totals, on=['account_id', 'adgroup_id', 'date'], how='left')
    
    result = result.merge(past_1day, on=['account_id', 'adgroup_id', 'date'], how='left')
    result = result.merge(past_3day, on=['account_id', 'adgroup_id', 'date'], how='left')
    result = result.merge(past_7day, on=['account_id', 'adgroup_id', 'date'], how='left')

    print(f"Columns after merge: {list(result.columns)}")
    
    result = result.fillna(0)
    
    # Sort by account_id, adgroup_id, date, hour (if hour column exists)
    sort_columns = ['account_id', 'adgroup_id', 'date']
    if 'hour' in result.columns:
        sort_columns.append('hour')
    result = result.sort_values(sort_columns)
    
    output_file = os.path.join(save_dir, 'aggregated_adgroup_daily_data.csv')
    result.to_csv(output_file, index=False)
    
    print(f"Aggregated data saved to {output_file}")
    print(f"Total rows in output: {len(result)}")
    print(f"Columns in output: {list(result.columns)}")
    
    print("\nSample of aggregated data:")
    print(result.head(10).to_string())

if __name__ == "__main__":
    main()
