import pandas as pd
import numpy as np
import datetime
from datetime import timedelta
import sys
import os 
import json

reg_cost_thres = 220
MODEL_PATH = '/data2/yuwu/huanbei/budget_control/0_day_model.json'
HOURLY_PREV_DAY_DATA_PATH = '/data2/AI-Marketing/auto_bid/huanbei/data_dynamic_creative_level/hourly_reports_REQUEST_TIME'
DAILY_DATA_SAVE_PATH = '/data2/yuwu/huanbei/budget_control/daily_data.csv'
HOURLY_REPORT_PATH = '/data2/AI-Marketing/auto_bid/huanbei/adgroup_report_data.json'

"""def load_and_parse_data(csv_file):
    try:
        df = pd.read_csv(csv_file, index_col=0)
        print(f"Loaded {len(df)} rows from {csv_file}")
        # print(f"Columns: {list(df.columns)}")
        # filter data up to 7 days ago 
        # df = df[df['ds'] >= (datetime.now() - timedelta(days=8)).strftime('%Y%m%d')]
        return df
    except Exception as e:
        print(f"Error loading CSV file: {e}")
        sys.exit(1)"""


def aggregate_by_adgroup(df):
    # df['cost'] = df['cost'] * 0.01
    grouped = df.groupby(['account_id', 'adgroup_id', 'ds', 'hour'])    
    aggregated = grouped.agg({
        # 'dynamic_creative_id': 'nunique',  # Count of unique dynamic_creative_ids
        'cost': 'sum',
        # 'acquisition_cost': 'sum',
        'view_count': 'sum',
        'valid_click_count': 'sum',
        'reg_pv': 'sum',
        'apply_pv': 'sum',
        'credit_pv': 'sum'
    }).reset_index()
    
    # Rename col name
    # aggregated.rename(columns={'dynamic_creative_id': 'unique_dynamic_creative_count'}, inplace=True)
    
    return aggregated


def calculate_daily_totals(df):
    daily_totals = df.groupby(['account_id', 'adgroup_id', 'ds']).agg({
        'cost': 'sum',
        # 'acquisition_cost': 'sum',
        'view_count': 'sum',
        'valid_click_count': 'sum',
        'reg_pv': 'sum',
        'apply_pv': 'sum',
        'credit_pv': 'sum'
    }).reset_index()
    
    # avoid zero division 
    daily_totals['reg_cost'] = np.where(daily_totals['reg_pv'] == 0, 0, daily_totals['cost'] / daily_totals['reg_pv'])
    daily_totals['apply_cost'] = np.where(daily_totals['apply_pv'] == 0, 0, daily_totals['cost'] / daily_totals['apply_pv'])
    daily_totals['credit_cost'] = np.where(daily_totals['credit_pv'] == 0, 0, daily_totals['cost'] / daily_totals['credit_pv'])
    #daily_totals['reg_cost'] = daily_totals['cost'].div(daily_totals['reg_pv'], 0)
    #daily_totals['apply_cost'] = daily_totals['cost'].div(daily_totals['apply_pv'], 0)
    #daily_totals['credit_cost'] = daily_totals['cost'].div(daily_totals['credit_pv'], 0)
    
    # metric_columns = ['cost', 'acquisition_cost', 'view_count', 'valid_click_count', 'reg_pv', 'apply_pv', 'credit_pv', 'reg_cost', 'apply_cost', 'credit_cost']
    metric_columns = ['cost', 'view_count', 'valid_click_count', 'reg_pv', 'apply_pv', 'credit_pv', 'reg_cost', 'apply_cost', 'credit_cost']

    rename_dict = {col: f'cur_day_{col}' for col in metric_columns}
    daily_totals = daily_totals.rename(columns=rename_dict)

    return daily_totals


def calculate_historical_totals(df, days_back):
    df['ds'] = pd.to_datetime(df['ds'])

    historical_data = []
    grouped_data = df.groupby(['account_id', 'adgroup_id'])

    for (account_id, adgroup_id), group_data in grouped_data:
        dates_for_group = sorted(group_data['ds'].unique())
        # print(f"dates_for_group: {dates_for_group}")

        for current_date in dates_for_group:
            current_date = pd.to_datetime(current_date)
            start_date = current_date - timedelta(days=days_back)
            end_date = current_date - timedelta(days=1)  # Exclude current day
            historical_period_data = group_data[
                (group_data['ds'] >= start_date) &
                (group_data['ds'] <= end_date)
            ]

            if len(historical_period_data) > 0:
                totals = historical_period_data.agg({
                    'cost': 'sum',
                    # 'acquisition_cost': 'sum',
                    'view_count': 'sum',
                    'valid_click_count': 'sum',
                    'reg_pv': 'sum',
                    'apply_pv': 'sum',
                    'credit_pv': 'sum'
                })

            else:
                # If no historical data, set all values to 0
                totals = pd.Series({
                    'cost': 0,
                    # 'acquisition_cost': 0,
                    'view_count': 0,
                    'valid_click_count': 0,
                    'reg_pv': 0,
                    'apply_pv': 0,
                    'credit_pv': 0,
                    
                })
            # calculate cost per pv
            if totals['reg_pv'] != 0:
                totals['reg_cost'] = totals['cost'] / totals['reg_pv']
            else:
                totals['reg_cost'] = 0
            if totals['apply_pv'] != 0:
                totals['apply_cost'] = totals['cost'] / totals['apply_pv']
            else:
                totals['apply_cost'] = 0
            if totals['credit_pv'] != 0:
                totals['credit_cost'] = totals['cost'] / totals['credit_pv']
            else:
                totals['credit_cost'] = 0
            
            historical_data.append({
                'account_id': account_id,
                'adgroup_id': adgroup_id,
                'ds': current_date,
                **{f'past_{days_back}day_{col}': totals[col] for col in totals.index}
            })
    
    # return a dataframe with all empty cols
    if len(historical_data) == 0:
        print(f"--- 没有找到历史{days_back}天前的数据")
        return pd.DataFrame({'account_id': [], 'adgroup_id': [], 'ds': [], 
                             **{f'past_{days_back}day_{col}': [] for col in ['cost', 'view_count', 'valid_click_count', 'reg_pv', 'apply_pv', 'credit_pv', 'reg_cost', 'apply_cost', 'credit_cost']}
                             })

    return pd.DataFrame(historical_data)


# Feature Engineering
def daily_feature_engineering(data):
    data['ds'] = pd.to_datetime(data['ds'])
    data['day_of_week'] = data['ds'].dt.dayofweek

    return data


def hourly_feature_engineering(hourly_data):
    """
    Ultra-efficient hourly feature engineering using pandas time-based rolling.
    """
    result_data = hourly_data.copy()
    # result_data['is_auto_acquisition'] = (result_data['acquisition_cost'] * 0.01 > 100).astype(int)
    if 'acquisition_cost' in result_data.columns:
        result_data.drop(['acquisition_cost'], axis=1, inplace=True)
    # Create cyclic features
    result_data['hour_sin'] = np.sin(2 * np.pi * result_data['hour'] / 24)
    result_data['hour_cos'] = np.cos(2 * np.pi * result_data['hour'] / 24)

    result_data['ds'] = pd.to_datetime(result_data['ds'])
    result_data['ts'] = result_data['ds'] + pd.to_timedelta(result_data['hour'], unit='h')
    result_data = result_data.sort_values(['account_id', 'adgroup_id', 'ts']).reset_index(drop=True)
    
    metrics = ['cost', 'reg_pv', 'apply_pv', 'credit_pv']
    hour_windows = [1, 2, 4, 6, 8, 12, 16]

    """
    def calculate_group_features(group):
        group = group.sort_values('ds')
        # group['ds'] = pd.to_datetime(group['ds'])
        for window in hour_windows:
            for metric in metrics:
                col_name = f'cum_{window}h_up_to_cur_h_{metric}'
                group[col_name] = 0

                for i, current_time in enumerate(group['ds']):
                    time_window_start = current_time - pd.Timedelta(hours=window)
                    group[col_name].iloc[i] = group.loc[(group['ds'] >= time_window_start) & (group['ds'] < current_time), metric].sum()

        return group
    """

    def calculate_group_features(group):
        group = group.set_index('ts').sort_index()

        for window in hour_windows:
            for metric in metrics:
                # calculate cumulative sum
                col_name = f'cum_{window}h_up_to_cur_h_{metric}'
                group[col_name] = group[metric].rolling(
                    window=f'{window}h',
                    min_periods=1,
                    closed='left'
                ).sum().fillna(0)
                # calculate mean
                col_name = f'mean_{window}h_up_to_cur_h_{metric}'
                group[col_name] = group[metric].rolling(
                    window=f'{window}h',
                    min_periods=1,
                    closed='left'
                ).mean().fillna(0)

        return group.reset_index()

    # Process each adgroup separately with vectorized operations
    result_list = []
    for (account_id, adgroup_id), group in result_data.groupby(['account_id', 'adgroup_id']):
        processed_group = calculate_group_features(group)
        result_list.append(processed_group)

    # Concatenate all results
    result_data = pd.concat(result_list, ignore_index=True)
    # print(f"Total rows after cumulative sum: {result_data.shape[0]}")

    # Create features for metrics of previous hour
    for metric in metrics:
        sub_data = result_data.sort_values(by=['account_id', 'adgroup_id', 'ts'])
        merged = pd.merge(
                sub_data,
                sub_data,
                left_on=['account_id', 'adgroup_id', 'ts'],
                right_on=['account_id', 'adgroup_id', sub_data['ts'] + pd.Timedelta(hours=1)],
                suffixes=('', '_prev'),
                how='left'
            )
        merged = merged.fillna(0)
        result_data[f'prev_h_{metric}'] = merged[f"{metric}_prev"]
    
    # print(f"Total rows after adding previous hour features: {result_data.shape[0]}")

    # Create squared, cubed features for metrics of previous hour
    for metric in [f"prev_h_{m}" for m in metrics]:
        result_data[f'prev_h_{metric}_squared'] = result_data[metric] ** 2
        result_data[f'prev_h_{metric}_cubed'] = result_data[metric] ** 3
    
    return result_data


def init_model(model_path):
    # import xgboost as xgb
    from xgboost.sklearn import XGBClassifier
    # model = xgb.XGBClassifier()
    # model.load_model(model_path)
    model = XGBClassifier(n_jobs=1)
    model.load_model(model_path)
    return model


def prepare_data(hourly_data, daily_data, reg_cost_thres):
    hourly_data = hourly_data.copy()
    daily_data = daily_data.copy()
    hourly_data['ds'] = pd.to_datetime(hourly_data['ds'])
    daily_data['ds'] = pd.to_datetime(daily_data['ds'])
    # drop low cost entries
    # daily_data = daily_data[daily_data['cur_day_cost'] > reg_cost_thres]
    cur_day_cols = [col for col in daily_data.columns if 'cur_day_' in col]
    daily_data.drop(cur_day_cols, axis=1, inplace=True)
    hourly_data = hourly_data.sort_values(['account_id', 'adgroup_id', 'ts']).reset_index(drop=True)
    hourly_data.drop(['cost', 'view_count', 'valid_click_count', 'reg_pv', 'apply_pv', 'credit_pv'], axis=1, inplace=True)
    merged_data = hourly_data.merge(daily_data, on=['account_id', 'adgroup_id', 'ds'], how='left')
    return merged_data



def get_daily_data(account_ids):
    # data_path = '/data2/xizhang/auto_bid/huanbei/data_adgroup_level/daily_reports_REQUEST_TIME'
    def load_and_parse_csv_data(csv_file):
        try:
            df = pd.read_csv(csv_file, index_col=0)
            df['cost'] = df['cost'] * 0.01
            # print(f"Loaded {len(df)} rows from {csv_file}")
            # print(f"Columns: {list(df.columns)}")
            # filter data up to 7 days ago 
            # df = df[df['ds'] >= (datetime.now() - timedelta(days=8)).strftime('%Y%m%d')]
            return df
        except Exception as e:
            print(f"Error loading CSV file: {e}")
            # sys.exit(1)
    all_df = []
    for file in os.listdir(HOURLY_PREV_DAY_DATA_PATH):
        if file.endswith('.csv'):
            file_account_id = file.split('.')[0]
            if file_account_id in [str(account_id) for account_id in account_ids]:
                data_file_path = os.path.join(HOURLY_PREV_DAY_DATA_PATH, file)
                # print(f"Loading {data_file_path}...")
                df = load_and_parse_csv_data(data_file_path)
                all_df.append(df)
            # print(f"Loaded {len(df)} rows from {data_file_path}")
    df = pd.concat(all_df, ignore_index=True)
    df['ds'] = pd.to_datetime(df['ds'])
    hourly_data = aggregate_by_adgroup(df)
    print("Calculating daily totals...")
    daily_totals = calculate_daily_totals(df)
    
    print("Calculating historical totals...")
    past_1day = calculate_historical_totals(hourly_data, 1)
    past_3day = calculate_historical_totals(hourly_data, 3)
    past_7day = calculate_historical_totals(hourly_data, 7)
    
    # Merge all data together
    print("Merging all data...")
    daily_totals['ds'] = pd.to_datetime(daily_totals['ds'])
    past_1day['ds'] = pd.to_datetime(past_1day['ds'])
    past_3day['ds'] = pd.to_datetime(past_3day['ds'])
    past_7day['ds'] = pd.to_datetime(past_7day['ds'])

    daily_data = daily_totals.copy()
    
    # print(f"Columns of daily data before merge: {list(daily_data.columns)}")

    # print(f"Columns in past_1day: {list(past_1day.columns)}")
    
    daily_data = daily_data.merge(past_1day, on=['account_id', 'adgroup_id', 'ds'], how='left')
    daily_data = daily_data.merge(past_3day, on=['account_id', 'adgroup_id', 'ds'], how='left')
    daily_data = daily_data.merge(past_7day, on=['account_id', 'adgroup_id', 'ds'], how='left')

    # print(f"Columns after merge: {list(daily_data.columns)}")
    
    daily_data = daily_data.fillna(0)
    
    # Sort by account_id, adgroup_id, date, hour (if hour column exists)
    sort_columns = ['account_id', 'adgroup_id', 'ds']
    if 'hour' in daily_data.columns:
        sort_columns.append('hour')
    daily_data = daily_data.sort_values(sort_columns)
    daily_data = daily_feature_engineering(daily_data)
    daily_data.to_csv(DAILY_DATA_SAVE_PATH, index=False)
    return daily_data


def json_to_hourly_dataframe(select_account_id, json_file_path: str, include_metadata: bool = True) -> pd.DataFrame:
    # Load JSON data
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"JSON file not found: {json_file_path}")
    except json.JSONDecodeError as e:
        print(f"Invalid JSON format: {e}")
    
    rows = []
    
    # Iterate through accounts
    for account_id, account_data in data.items():
        if int(account_id) != int(select_account_id):
            continue
        # Iterate through adgroups
        for adgroup_id, adgroup_data in account_data.items():
            # Extract metadata
            ds = adgroup_data.get('ds', '')
            
            # Extract today's hourly report
            hourly_report = adgroup_data.get('today_hourly_report', {})
            
            # Get hourly data arrays
            hours = hourly_report.get('hour', [])
            view_counts = hourly_report.get('view_count', [])
            valid_click_counts = hourly_report.get('valid_click_count', [])
            costs = hourly_report.get('cost', [])
            reg_pvs = hourly_report.get('reg_pv', [])
            # reg_cost = hourly_report.get('reg_cost', [])
            apply_pvs = hourly_report.get('apply_pv', [])
            credit_pvs = hourly_report.get('credit_pv', [])
            acquisition_costs = hourly_report.get('acquisition_cost', [])
            
            # Check if all arrays have the same length
            arrays = [hours, view_counts, valid_click_counts, costs, reg_pvs, apply_pvs, credit_pvs, acquisition_costs]
            array_lengths = [len(arr) for arr in arrays]
            
            if len(set(array_lengths)) > 1:
                print(f"Warning: Inconsistent array lengths for account {account_id}, adgroup {adgroup_id}: {array_lengths}")
                # Use the minimum length to avoid index errors
                min_length = min(array_lengths) if array_lengths else 0
            else:
                min_length = array_lengths[0] if array_lengths else 0
                        
            # Create rows for each hour
            for i in range(min_length):
                row = {
                    
                    'account_id': int(account_id),
                    'adgroup_id': int(adgroup_id),
                    'ds': ds,
                    'hour': hours[i] if i < len(hours) else None,
                    # 'date': ds,
                    'cost': costs[i] if i < len(costs) else 0.0,
                    # 'acquisition_cost': acquisition_costs[i] if i < len(acquisition_costs) else 0.0,
                    'view_count': view_counts[i] if i < len(view_counts) else 0,
                    'valid_click_count': valid_click_counts[i] if i < len(valid_click_counts) else 0,
                    'reg_pv': reg_pvs[i] if i < len(reg_pvs) else 0,
                    # 'reg_cost': reg_cost[i] if i < len(reg_cost) else 0,
                    'apply_pv': apply_pvs[i] if i < len(apply_pvs) else 0,
                    'credit_pv': credit_pvs[i] if i < len(credit_pvs) else 0,
                    
                }
                
                # Add metadata if requested
                if include_metadata:
                    # row['life_long_days'] = adgroup_data.get('life_long_days', 0)
                    # row['ds'] = ds
                    # row['system_status'] = adgroup_data.get('system_status', '')
                    pass

                rows.append(row)
    
    # Create DataFrame
    df = pd.DataFrame(rows)


    # check if csv for current account id exists:
    prev_day_hourly_report_path = os.path.join(HOURLY_PREV_DAY_DATA_PATH, f'{select_account_id}.csv')
    if os.path.exists(prev_day_hourly_report_path):
        prev_hours_df = aggregate_by_adgroup(pd.read_csv(prev_day_hourly_report_path))
    else:
        prev_hours_df = pd.DataFrame([])
    df = pd.concat([prev_hours_df, df], ignore_index=True)
    
    # Convert data types
    if not df.empty:
        
        # Ensure numeric columns are numeric
        numeric_columns = ['view_count', 'valid_click_count', 'cost', 'reg_pv', 'apply_pv', 'credit_pv', 'acquisition_cost']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
        
        # Sort by account_id, adgroup_id, date, hour
        sort_columns = ['account_id', 'adgroup_id']
        if 'ds' in df.columns:
            sort_columns.append('ds')
        if 'hour' in df.columns:
            sort_columns.append('hour')
        
        df = df.sort_values(sort_columns).reset_index(drop=True)
        if sum(df['cost']) <= reg_cost_thres:
            print(f"--- 跳过今日消耗小于等于阈值{reg_cost_thres}的广告组: {account_id}-{adgroup_id}")
            return pd.DataFrame([])
    return df


def get_predict_data(model, df):
    # Aggregate by adgroup
    print("Aggregating data by adgroup...")
    # hourly_data = aggregate_by_adgroup(df)
    hourly_data = df.copy()
    # print("hourly_data columns: ", hourly_data.columns)
    # print(f"hourly data length: {len(hourly_data)}")
    daily_data = pd.read_csv(DAILY_DATA_SAVE_PATH)

    """print("Calculating daily totals...")
    daily_totals = calculate_daily_totals(df)
    
    print("Calculating historical totals...")
    past_1day = calculate_historical_totals(hourly_data, 1)
    past_3day = calculate_historical_totals(hourly_data, 3)
    past_7day = calculate_historical_totals(hourly_data, 7)
    
    # Merge all data together
    print("Merging all data...")
    daily_totals['ds'] = pd.to_datetime(daily_totals['ds'])
    past_1day['ds'] = pd.to_datetime(past_1day['ds'])
    past_3day['ds'] = pd.to_datetime(past_3day['ds'])
    past_7day['ds'] = pd.to_datetime(past_7day['ds'])

    daily_data = daily_totals.copy()
    
    # print(f"Columns of daily data before merge: {list(daily_data.columns)}")

    # print(f"Columns in past_1day: {list(past_1day.columns)}")
    
    daily_data = daily_data.merge(past_1day, on=['account_id', 'adgroup_id', 'ds'], how='left')
    daily_data = daily_data.merge(past_3day, on=['account_id', 'adgroup_id', 'ds'], how='left')
    daily_data = daily_data.merge(past_7day, on=['account_id', 'adgroup_id', 'ds'], how='left')

    # print(f"Columns after merge: {list(daily_data.columns)}")
    
    daily_data = daily_data.fillna(0)
    
    # Sort by account_id, adgroup_id, date, hour (if hour column exists)
    sort_columns = ['account_id', 'adgroup_id', 'ds']
    if 'hour' in daily_data.columns:
        sort_columns.append('hour')
    daily_data = daily_data.sort_values(sort_columns)"""

    hourly_data = hourly_feature_engineering(hourly_data)
    # daily_data = daily_feature_engineering(daily_data)
    features = prepare_data(hourly_data, daily_data, reg_cost_thres)

    # only keep the newest entry for each account id, adgroup id 
    grouped = features.groupby(['account_id', 'adgroup_id'])
    for (account_id, adgroup_id), group in grouped:
        group = group.sort_values('ts')
    features = grouped.apply(lambda x: x.iloc[-1]).reset_index(drop=True)
    predict_features = features.drop(['account_id', 'adgroup_id', 'ds', 'hour', 'ts'], axis=1)
    predict_score = model.predict_proba(predict_features)[:, 1]
    predict_df = features[['account_id', 'adgroup_id', 'ds', 'hour', 'ts']].copy()
    predict_df['predict_score'] = predict_score
    return predict_df


def check_if_over_budget_by_model(select_account_id, adgroup_status, account_config, string_format, score_thres=0.8):
    """
    select_account_id: str
    adgroup_status: dict, key is adgroup_id, value is a dict with key 'systemStatus'
    account_config: AccountConfig object
    string_format: StringFormat object
    score_thres: float, threshold for the predict score
    """
    current_time = datetime.datetime.now()
    print(f"--- 当前时间：{str(current_time)}")
    print(f"--- 模型控停配置：{score_thres}")
    res = {}
    model = init_model(MODEL_PATH)
    data_path = HOURLY_REPORT_PATH
    # iterate through the given directory, each csv is for one account id
    print(f"Loading data for account id {select_account_id}...")
    """all_df = []
    for file in os.listdir(data_path):
        if file.endswith('.csv'):
            file_account_id = file.split('.')[0]
            if file_account_id in [str(select_account_id)]:
                data_file_path = os.path.join(data_path, file)
                print(f"Loading {data_file_path}...")
                df = load_and_parse_data(data_file_path)
                all_df.append(df)
            # print(f"Loaded {len(df)} rows from {data_file_path}")
    if len(all_df) == 0:
        print(f"--- 没有找到账号 {select_account_id} 的数据")
        return res
    adgroup_data = pd.concat(all_df, ignore_index=True)"""
    adgroup_data = json_to_hourly_dataframe(select_account_id, data_path, True)
    if adgroup_data.empty:
        print(f"--- 没有找到账号 {select_account_id} 的数据")
        return res

    # filter by adgroup status 
    adgroup_id_to_keep = {adgroup_id for adgroup_id in adgroup_status.keys() if adgroup_status[adgroup_id]['systemStatus'] in account_config.ACTIVE_STATUS}
    adgroup_id_to_keep = [int(x) for x in adgroup_id_to_keep]
    adgroup_data = adgroup_data[adgroup_data['adgroup_id'].isin(adgroup_id_to_keep)]
    
    # Convert date to datetime
    adgroup_data['ds'] = pd.to_datetime(adgroup_data['ds'])

    # get predict result for all eligible adgroups
    predict_df = get_predict_data(model, adgroup_data)

    # filter entries to only keep the newest entry for each adgroup
    # grouped = predict_df.groupby(['account_id', 'adgroup_id'])
    # predict_df = grouped.apply(lambda x: x.sort_values('ts').iloc[-1]).reset_index(drop=True)

    weekends = [4, 5, 6] # Fri, Sat, Sun
    if datetime.datetime.today().weekday() in weekends: 
        tomorrow = str(datetime.datetime.today() + datetime.timedelta(days=7-datetime.datetime.today().weekday())).split(' ')[0]
    else:
        tomorrow = str(datetime.datetime.today() + datetime.timedelta(days=1)).split(' ')[0]
    for account_id in [int(select_account_id)]:
        print('-----------------------------')
        account_id = int(account_id)
        print('account_id: ', account_id)
        select_adgroup_ids = []
        account_df = predict_df[predict_df['account_id'].astype(int) == account_id]
        print(f"predict score for account {account_id}: {account_df}")
        account_df = account_df[account_df['predict_score'] > score_thres]
        if len(account_df) == 0:
            continue
        for idx, row in account_df.iterrows():
            adgroup_id = row['adgroup_id']
            system_status = adgroup_status[adgroup_id]['systemStatus']
            if system_status not in account_config.ACTIVE_STATUS:
                continue
            print(account_id, adgroup_id, '模型控停')
            msg = (f"--- 模型控停 特征时间: {row['ts']} 分数: {row['predict_score']:.4f} > {score_thres}")
            select_adgroup_ids.append([f'{string_format.UPDATE_DELIVERY_DATE}%s' % tomorrow, account_id, int(adgroup_id), msg])
        for d in select_adgroup_ids:
            print(d)
        res[account_id] = select_adgroup_ids
    return res



