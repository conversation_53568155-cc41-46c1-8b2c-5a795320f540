{"target_config": {"year_target": 210, "step_1_target": 220, "step_2_target": 701, "step_3_target": 2588}, "account_config": {"old_accounts": [], "median_accounts": [], "new_accounts": ["********"], "one_hundred_account": [], "one_third_account": [], "two_small_up_account": [], "two_ups_account": [], "afternoon_accounts": [], "control_exclude_accounts": [], "create_exclude_accounts": [], "up_exclude_accounts": [], "over_budget_exclude_accounts": [], "low_cost_exclude_accounts": [], "youju_accounts": [], "stop_accounts": [], "active_status": ["ADGROUP_STATUS_PARTIAL_ACTIVE", "ADGROUP_STATUS_ACTIVE"], "not_in_delivery_status": "ADGROUP_STATUS_NOT_IN_DELIVERY", "auto_acquisition_pending_status": "AUTO_ACQUISITION_STATUS_PENDING"}, "api_config": {"api_url": "https://api.e.qq.com/v3.0/", "adgroups_interface": "adgroups/get", "hourly_interface": "hourly_reports/get", "daily_interface": "daily_reports/get", "access_token": "66c1748bdca5e0016250f3cdd4cdaa4f", "server_port": 8866}, "path_config": {"adgroups_dir": "./adgroups", "adgroup_report_path": "adgroup_report_data.json", "stop_dates_path": "stop_dates.txt"}, "create_config": {"no_create_week_of_day": ["星期二", "星期四", "星期六", "星期日"]}, "budget_control_config": {"check_pre_threshold_ratio": 0.5, "step_1_cost_limit_scale": 3, "step_2_cost_limit_scale": 2, "step_3_cost_limit_scale": 1.5, "use_houxiao_stop": 0, "post_effect_step_3_num_limit": 10, "cost_rate_scale": 2, "morning_scale": 1.0, "afternoon_scale": 1.0, "night_scale": 1.0}, "acquisition_control_config": {"hist_range": 7, "up_all_num_for_new": 1, "up_all_num_for_median": 2, "up_all_num_for_old": 3, "morning_hours": [10, 11], "afternoon_hours": [14, 15], "no_up_day_of_week": ["星期六", "星期日"], "reup_hours": [12, 13, 14, 15], "cost_threshold": 700, "acquisition_budget_for_old": 2000000, "acquisition_budget_for_med": 5000000, "acquisition_budget_for_new": 3000000, "step_1_cost_limit_scale_for_morning": 2, "step_2_cost_limit_scale_for_morning": 2, "step_3_cost_limit_scale_for_morning": 1.6666, "step_1_cost_limit_scale_for_afternoon": 2, "step_2_cost_limit_scale_for_afternoon": 2, "step_3_cost_limit_scale_for_afternoon": 2}, "data_header_config": {"life_long_days": "life_long_days", "up_trend": "up_trend", "is_ever_up": "is_ever_up", "today_prefix": "today", "past_1_days_prefix": "past_1day", "past_3_days_prefix": "past_3day", "past_7_days_prefix": "past_7day", "past_14_days_prefix": "past_14day", "cost_postfix": "cost", "uv_postfix": "pv", "step_1_stat_name": "reg", "step_2_stat_name": "apply", "step_3_stat_name": "credit", "join_char": "_"}, "string_format_config": {"update_delivery_date": "updateDeliveryDate-", "suspend": "suspend"}, "threshold_config": {"min_cost_threshold": 100, "low_cost_threshold": 500, "acquisition_cost_threshold": 100, "signature_cost_threshold": 1000, "page_size_default": 100, "page_size_large": 500}}