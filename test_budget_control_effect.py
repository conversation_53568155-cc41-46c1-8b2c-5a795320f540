import json
import pandas as pd
import numpy as np


# cost per pv thresholds
reg_cost_thres = 220 
apply_cost_thres = 701 
credit_cost_thres =  2588

def calculate_overall_metrics(data):
    # Calculate overall metrics: sum all
    overall_metrics = {
        'cost': data['cost'].sum(),
        'reg_pv': data['reg_pv'].sum(),
        'apply_pv': data['apply_pv'].sum(),
        'credit_pv': data['credit_pv'].sum()
    }

    overall_metrics['reg_cost'] = overall_metrics['cost'] / overall_metrics['reg_pv']
    overall_metrics['apply_cost'] = overall_metrics['cost'] / overall_metrics['apply_pv']
    overall_metrics['credit_cost'] = overall_metrics['cost'] / overall_metrics['credit_pv']
    
    return {k:float(v) for k, v in overall_metrics.items()}


def filter_stopped_adgroups_hourly_entry(data, predict_result, score_thres=0.5, restart_hour=6, use_budget_control=False):
    filtered_data = data.copy()
    filtered_data['ds'] = pd.to_datetime(filtered_data['ds'])

    if use_budget_control:
        for metric in ['reg_pv', 'apply_pv', 'credit_pv']:
            filtered_data[f'cur_day_{metric}_up_to_prev_h'] = 0
        filtered_data['cur_day_cost_up_to_prev_h'] = 0.0
        for metric in ['reg', 'apply', 'credit']:
            filtered_data[f'cur_day_{metric}_cost_up_to_prev_h'] = 0.0
        grouped = filtered_data.groupby(['account_id', 'adgroup_id', 'date'])
        for (account_id, adgroup_id, date), group in grouped:
            group = group.sort_values('hour')
            for i in range(1, len(group)):
                """
                for metric in ['cost', 'reg_pv', 'apply_pv', 'credit_pv']:
                    filtered_data.loc[group.index[i], f'cur_day_{metric}_up_to_prev_h'] = group.loc[group.index[:i], metric].sum()
                for metric in ['reg', 'apply', 'credit']:
                    if filtered_data.loc[group.index[i], f'cur_day_{metric}_up_to_prev_h'] == 0:
                        filtered_data.loc[group.index[i], f'cur_day_{metric}_cost_up_to_prev_h'] = 0.0
                    else:
                        filtered_data.loc[group.index[i], f'cur_day_{metric}_cost_up_to_prev_h'] = filtered_data.loc[group.index[i], 'cur_day_cost_up_to_prev_h'] / filtered_data.loc[group.index[i], f'cur_day_{metric}_up_to_prev_h']
                """
                filtered_data.loc[group.index[i], 'cur_day_cost_up_to_prev_h'] = group.loc[group.index[:i], 'cost'].sum()
                filtered_data.loc[group.index[i], 'cur_day_reg_pv_up_to_prev_h'] = group.loc[group.index[:i], 'reg_pv'].sum()
                filtered_data.loc[group.index[i], 'cur_day_apply_pv_up_to_prev_h'] = group.loc[group.index[:i], 'apply_pv'].sum()
                filtered_data.loc[group.index[i], 'cur_day_credit_pv_up_to_prev_h'] = group.loc[group.index[:i], 'credit_pv'].sum()

                if filtered_data.loc[group.index[i], 'cur_day_reg_pv_up_to_prev_h'] == 0:
                    filtered_data.loc[group.index[i], 'cur_day_reg_cost_up_to_prev_h'] = 0.0
                else:
                    filtered_data.loc[group.index[i], 'cur_day_reg_cost_up_to_prev_h'] = filtered_data.loc[group.index[i], 'cur_day_cost_up_to_prev_h'] / filtered_data.loc[group.index[i], 'cur_day_reg_pv_up_to_prev_h']
                
                if filtered_data.loc[group.index[i], 'cur_day_apply_pv_up_to_prev_h'] == 0:
                    filtered_data.loc[group.index[i], 'cur_day_apply_cost_up_to_prev_h'] = 0.0
                else:
                    filtered_data.loc[group.index[i], 'cur_day_apply_cost_up_to_prev_h'] = filtered_data.loc[group.index[i], 'cur_day_cost_up_to_prev_h'] / filtered_data.loc[group.index[i], 'cur_day_apply_pv_up_to_prev_h']

                if filtered_data.loc[group.index[i], 'cur_day_credit_pv_up_to_prev_h'] == 0:
                    filtered_data.loc[group.index[i], 'cur_day_credit_cost_up_to_prev_h'] = 0.0
                else:
                    filtered_data.loc[group.index[i], 'cur_day_credit_cost_up_to_prev_h'] = filtered_data.loc[group.index[i], 'cur_day_cost_up_to_prev_h'] / filtered_data.loc[group.index[i], 'cur_day_credit_pv_up_to_prev_h']
        # budget control based on budget limit
        reg_limit = reg_cost_thres * 2
        apply_limit = apply_cost_thres * 2
        credit_limit = credit_cost_thres * 1
        stop_by_cost_limit = filtered_data[filtered_data['cur_day_reg_cost_up_to_prev_h'] > reg_limit].copy()
        stop_by_cost_limit = stop_by_cost_limit[stop_by_cost_limit['cur_day_apply_cost_up_to_prev_h'] > apply_limit]
        stop_by_cost_limit = stop_by_cost_limit[stop_by_cost_limit['cur_day_credit_cost_up_to_prev_h'] > credit_limit]
        stop_by_cost_limit['stop_start'] = stop_by_cost_limit['ds']
        stop_by_cost_limit['stop_end'] = (
            pd.to_datetime(stop_by_cost_limit['ds'].dt.date) +
            pd.Timedelta(days=1, hours=restart_hour)
        )
        print(f"Found {len(stop_by_cost_limit)} stop signals by cost limit")

    predict_df = predict_result.copy()
    print(f"Current date range: {predict_df['date'].min()} to {predict_df['date'].max()}")

    predict_df['ds'] = pd.to_datetime(predict_df['ds'])

    stop_signals = predict_df[predict_df['predict_score'] > score_thres].copy()

    """if stop_signals.empty:
        print(f"No stop signals found (no predictions > {score_thres})")
        return filtered_data"""

    print(f"Found {len(stop_signals)} stop signals by model")

    stop_signals['stop_start'] = stop_signals['ds']
    stop_signals['stop_end'] = (
        pd.to_datetime(stop_signals['ds'].dt.date) +
        pd.Timedelta(days=1, hours=restart_hour)
    )

    # Create a list to collect rows to remove
    rows_to_remove = []

    # For each stop signal, find overlapping data entries
    for _, stop_row in stop_signals.iterrows():
        account_id = stop_row['account_id']
        adgroup_id = stop_row['adgroup_id']
        stop_start = stop_row['stop_start']
        stop_end = stop_row['stop_end']

        mask = (
            (filtered_data['account_id'] == account_id) &
            (filtered_data['adgroup_id'] == adgroup_id) &
            (filtered_data['ds'] >= stop_start) &
            (filtered_data['ds'] < stop_end)
        )

        rows_to_remove.extend(filtered_data[mask].index.tolist())
    
    if use_budget_control:
        for _, stop_row in stop_by_cost_limit.iterrows():
            account_id = stop_row['account_id']
            adgroup_id = stop_row['adgroup_id']
            stop_start = stop_row['stop_start']
            stop_end = stop_row['stop_end']

            mask = (
                (filtered_data['account_id'] == account_id) &
                (filtered_data['adgroup_id'] == adgroup_id) &
                (filtered_data['ds'] >= stop_start) &
                (filtered_data['ds'] < stop_end)
            )

            rows_to_remove.extend(filtered_data[mask].index.tolist())


    rows_to_remove = list(set(rows_to_remove))
    filtered_data = filtered_data.drop(index=rows_to_remove)

    print(f"Original data: {len(data)} rows")
    print(f"Filtered data: {len(filtered_data)} rows")
    print(f"Removed: {len(rows_to_remove)} rows")

    return filtered_data


if __name__ == "__main__":
    save_dir = 'data_20250323-********/'
    import os

    score_thres = 0.7
    restart_hour = 5
    # score_thres = 0.68
    # restart_hour = 8
    print(f"===Config: score threshold: {score_thres}, restart hour: {restart_hour}")

    data = pd.read_csv(os.path.join(save_dir, 'aggregated_adgroup_hourly_data.csv'))
    data['ds'] = pd.to_datetime(data['date']) + pd.to_timedelta(data['hour'], unit='h')

    print(f'===Running test for train data...')
    predict_result = pd.read_csv(os.path.join(save_dir, '0_day_predict_result_train.csv'))
    train_data = data[data['ds'] < max(predict_result['ds'])]
    filtered_data = filter_stopped_adgroups_hourly_entry(train_data, predict_result, score_thres, restart_hour)
    org_metrics = calculate_overall_metrics(train_data)
    print(f"\nOverall metrics for original data:")
    print(json.dumps(org_metrics))
    train_metrics = calculate_overall_metrics(filtered_data)
    print(f"\nOverall metrics for filtered data:")
    print(json.dumps(train_metrics))
    # compute overall cost, credit_pv diff ratio
    cost_diff = (train_metrics['cost'] - org_metrics['cost']) / org_metrics['cost']
    credit_pv_diff = (train_metrics['credit_pv'] - org_metrics['credit_pv']) / org_metrics['credit_pv']
    credit_cost_diff = (train_metrics['credit_cost'] - org_metrics['credit_cost']) / org_metrics['credit_cost']
    print(f"\nCost diff ratio: {cost_diff * 100:.2f}%")
    print(f"Credit PV diff ratio: {credit_pv_diff * 100:.2f}%")
    print(f"Credit cost diff ratio: {credit_cost_diff * 100:.2f}%")

    # filtered_data.to_csv('filtered_adgroup_hourly_data.csv', index=False)
    print(f'\n===Running test for test data...')
    print(f'===Running test for test data (first 7 days)...')
    predict_result = pd.read_csv(os.path.join(save_dir, '0_day_predict_result_test.csv'))

    predict_result['ds'] = pd.to_datetime(predict_result['ds'])
    cut_thres = min(predict_result['ds']) + pd.Timedelta(days=7)
    predict_result = predict_result[predict_result['ds'] <= cut_thres]

    test_data = data[data['ds'] >= min(predict_result['ds'])]
    filtered_data = filter_stopped_adgroups_hourly_entry(test_data, predict_result, score_thres, restart_hour)
    org_metrics = calculate_overall_metrics(test_data)
    print(f"\nOverall metrics for original data:")
    print(json.dumps(org_metrics))
    test_metrics = calculate_overall_metrics(filtered_data)
    print(f"\nOverall metrics for filtered data:")
    print(json.dumps(test_metrics))

    cost_diff = (test_metrics['cost'] - org_metrics['cost']) / org_metrics['cost']
    credit_pv_diff = (test_metrics['credit_pv'] - org_metrics['credit_pv']) / org_metrics['credit_pv']
    credit_cost_diff = (test_metrics['credit_cost'] - org_metrics['credit_cost']) / org_metrics['credit_cost']
    print(f"\nCost diff ratio: {cost_diff * 100:.2f}%") # percentage
    print(f"Credit PV diff ratio: {credit_pv_diff * 100:.2f}%")
    print(f"Credit cost diff ratio: {credit_cost_diff * 100:.2f}%")

    print(f'\n===Running test for test data (last 7 days)...')
    predict_result = pd.read_csv(os.path.join(save_dir,'0_day_predict_result_test.csv'))
    predict_result['ds'] = pd.to_datetime(predict_result['ds'])
    predict_result = predict_result[predict_result['ds'] > cut_thres]

    test_data = data[data['ds'] >= min(predict_result['ds'])]
    filtered_data = filter_stopped_adgroups_hourly_entry(test_data, predict_result, score_thres, restart_hour)
    org_metrics = calculate_overall_metrics(test_data)
    print(f"\nOverall metrics for original data:")
    print(json.dumps(org_metrics))
    test_metrics = calculate_overall_metrics(filtered_data)
    print(f"\nOverall metrics for filtered data:")
    print(json.dumps(test_metrics))

    cost_diff = (test_metrics['cost'] - org_metrics['cost']) / org_metrics['cost']
    credit_pv_diff = (test_metrics['credit_pv'] - org_metrics['credit_pv']) / org_metrics['credit_pv']
    credit_cost_diff = (test_metrics['credit_cost'] - org_metrics['credit_cost']) / org_metrics['credit_cost']
    print(f"\nCost diff ratio: {cost_diff * 100:.2f}%") # percentage
    print(f"Credit PV diff ratio: {credit_pv_diff * 100:.2f}%")
    print(f"Credit cost diff ratio: {credit_cost_diff * 100:.2f}%")
    