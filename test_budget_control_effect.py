import pandas as pd 


def calculate_overall_metrics(data):
    # Calculate overall metrics
    overall_metrics = data.groupby('ds').agg({
        'cost': 'sum',
        'reg_pv': 'sum',
        'apply_pv': 'sum',
        'credit_pv': 'sum'
    }).reset_index()

    overall_metrics['reg_cost'] = overall_metrics['cost'] / overall_metrics['reg_pv']
    overall_metrics['apply_cost'] = overall_metrics['cost'] / overall_metrics['apply_pv']
    overall_metrics['credit_cost'] = overall_metrics['cost'] / overall_metrics['credit_pv']

    return overall_metrics


def filter_stopped_adgroups_hourly_entry(data, predict_result, restart_hour=6):
    filtered_data = data.copy()
    predict_df = predict_result.copy()

    print(f"Current date range: {predict_df['date'].min()} to {predict_df['date'].max()}")

    filtered_data['ds'] = pd.to_datetime(filtered_data['ds'])
    predict_df['ds'] = pd.to_datetime(predict_df['ds'])

    filtered_data = filtered_data.sort_values(['account_id', 'adgroup_id', 'ds'])

    rows_to_remove = set()
    for idx, row in filtered_data.iterrows():
        account_id = row['account_id']
        adgroup_id = row['adgroup_id']
        current_ds = row['ds']
        prediction_match = predict_df[
            (predict_df['account_id'] == account_id) &
            (predict_df['adgroup_id'] == adgroup_id) &
            (predict_df['ds'] == current_ds)
        ]

        if not prediction_match.empty:
            predict_score = prediction_match.iloc[0]['predict_score']

            if predict_score > 0.5:
                current_date = current_ds.date()
                next_day_restart_time = pd.Timestamp(current_date) + pd.Timedelta(days=1, hours=restart_hour)

                adgroup_entries = filtered_data[
                    (filtered_data['account_id'] == account_id) &
                    (filtered_data['adgroup_id'] == adgroup_id) &
                    (filtered_data['ds'] >= current_ds) &
                    (filtered_data['ds'] < next_day_restart_time)
                ]

                rows_to_remove.update(adgroup_entries.index.tolist())
    filtered_data = filtered_data.drop(index=rows_to_remove, inplace=True)
    # filtered_data = filtered_data.reset_index(drop=True)

    print(f"Original data: {len(data)} rows")
    print(f"Filtered data: {len(filtered_data)} rows")
    print(f"Removed: {len(rows_to_remove)} rows")

    return filtered_data


if __name__ == "__main__":
    data = pd.read_csv('aggregated_adgroup_hourly_data.csv')
    data['ds'] = pd.to_datetime(data['date']) + pd.to_timedelta(data['hour'], unit='h')

    print(f'===Running test for train data...')
    predict_result = pd.read_csv('0_day_predict_result_train.csv')
    train_data = data[data['ds'] <= '2025-06-08 00:00:00']
    filtered_data = filter_stopped_adgroups_hourly_entry(data, predict_result, 6)
    metrics_df = calculate_overall_metrics(data)
    print(f"Overall metrics for original data:")
    print(metrics_df.to_string())
    metrics_df = calculate_overall_metrics(filtered_data)
    print(f"Overall metrics for filtered data:")
    print(metrics_df.to_string())

    # filtered_data.to_csv('filtered_adgroup_hourly_data.csv', index=False)
    print(f'===Running test for test data...')
    predict_result = pd.read_csv('0_day_predict_result_test.csv')
    filtered_data = filter_stopped_adgroups_hourly_entry(data, predict_result, 6)
    metrics_df = calculate_overall_metrics(data)
    print(f"Overall metrics for original data:")
    print(metrics_df.to_string())
    metrics_df = calculate_overall_metrics(filtered_data)
    print(f"Overall metrics for filtered data:")
    print(metrics_df.to_string())
    