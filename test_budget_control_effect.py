import pandas as pd
import numpy as np


def calculate_overall_metrics(data):
    # Calculate overall metrics: sum all
    overall_metrics = {
        'cost': data['cost'].sum(),
        'reg_pv': data['reg_pv'].sum(),
        'apply_pv': data['apply_pv'].sum(),
        'credit_pv': data['credit_pv'].sum()
    }

    overall_metrics['reg_cost'] = overall_metrics['cost'] / overall_metrics['reg_pv']
    overall_metrics['apply_cost'] = overall_metrics['cost'] / overall_metrics['apply_pv']
    overall_metrics['credit_cost'] = overall_metrics['cost'] / overall_metrics['credit_pv']

    return overall_metrics


def filter_stopped_adgroups_hourly_entry(data, predict_result, restart_hour=6):
    filtered_data = data.copy()
    predict_df = predict_result.copy()

    print(f"Current date range: {predict_df['date'].min()} to {predict_df['date'].max()}")

    # Ensure datetime columns
    filtered_data['ds'] = pd.to_datetime(filtered_data['ds'])
    predict_df['ds'] = pd.to_datetime(predict_df['ds'])

    # Filter predictions with score > 0.5 (these are the stop signals)
    stop_signals = predict_df[predict_df['predict_score'] > 0.5].copy()

    if stop_signals.empty:
        print("No stop signals found (no predictions > 0.5)")
        return filtered_data

    print(f"Found {len(stop_signals)} stop signals")

    # Calculate stop periods for each stop signal
    stop_signals['stop_start'] = stop_signals['ds']
    stop_signals['stop_end'] = (
        pd.to_datetime(stop_signals['ds'].dt.date) +
        pd.Timedelta(days=1, hours=restart_hour)
    )

    # Create a list to collect rows to remove
    rows_to_remove = []

    # For each stop signal, find overlapping data entries
    for _, stop_row in stop_signals.iterrows():
        account_id = stop_row['account_id']
        adgroup_id = stop_row['adgroup_id']
        stop_start = stop_row['stop_start']
        stop_end = stop_row['stop_end']

        # Find all data entries for this adgroup in the stop period
        mask = (
            (filtered_data['account_id'] == account_id) &
            (filtered_data['adgroup_id'] == adgroup_id) &
            (filtered_data['ds'] >= stop_start) &
            (filtered_data['ds'] < stop_end)
        )

        rows_to_remove.extend(filtered_data[mask].index.tolist())

    # Remove duplicates and apply removal
    rows_to_remove = list(set(rows_to_remove))
    filtered_data = filtered_data.drop(index=rows_to_remove)

    print(f"Original data: {len(data)} rows")
    print(f"Filtered data: {len(filtered_data)} rows")
    print(f"Removed: {len(rows_to_remove)} rows")

    return filtered_data


if __name__ == "__main__":
    data = pd.read_csv('aggregated_adgroup_hourly_data.csv')
    data['ds'] = pd.to_datetime(data['date']) + pd.to_timedelta(data['hour'], unit='h')

    print(f'===Running test for train data...')
    predict_result = pd.read_csv('0_day_predict_result_train.csv')
    train_data = data[data['ds'] < max(predict_result['ds'])]
    filtered_data = filter_stopped_adgroups_hourly_entry(train_data, predict_result, 6)
    metrics = calculate_overall_metrics(train_data)
    print(f"Overall metrics for original data:")
    print(metrics)
    metrics = calculate_overall_metrics(filtered_data)
    print(f"Overall metrics for filtered data:")
    print(metrics)

    # filtered_data.to_csv('filtered_adgroup_hourly_data.csv', index=False)
    print(f'===Running test for test data...')
    predict_result = pd.read_csv('0_day_predict_result_test.csv')
    test_data = data[data['ds'] >= min(predict_result['ds'])]
    filtered_data = filter_stopped_adgroups_hourly_entry(test_data, predict_result, 6)
    metrics = calculate_overall_metrics(test_data)
    print(f"Overall metrics for original data:")
    print(metrics)
    metrics = calculate_overall_metrics(filtered_data)
    print(f"Overall metrics for filtered data:")
    print(metrics)
    